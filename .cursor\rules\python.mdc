---
description: 
globs: 
alwaysApply: true
---
When writing python:
- always use type hints in function signatures
- remember that Optional is deprecated and use '| None' instead
- avoid using deprecated primitives like Optional
- add comments to explain *why* code does what it does, not *what* it does (the code itself should be clear enough to explain what it does)
- avoid redundant comments that simply restate what the code obviously does
- do use comments to explain complex logic, design decisions, or non-obvious implications

