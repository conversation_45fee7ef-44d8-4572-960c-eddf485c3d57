# image: python:3.12-slim

# stages:
#   - unit-test
#   - integration-test
#   # - build
#   - publish

# variables:
#   POETRY_CACHE_DIR: "$CI_PROJECT_DIR/.poetry_cache"
#   POETRY_VIRTUALENVS_IN_PROJECT: "true"
#   PYTHONPATH: "$CI_PROJECT_DIR/src"

# cache:
#   key: ${CI_COMMIT_REF_SLUG}
#   paths:
#     - .poetry_cache/
#     - .venv/
#   policy: pull-push

# before_script:
#   - apt-get update
#   - apt-get install -y curl build-essential git openssh-client
#   - curl -sSL https://install.python-poetry.org | python3 -
#   - export PATH="$HOME/.local/bin:$PATH"
#   - poetry config virtualenvs.create true
#   - poetry install --no-root

# test:unit:
#   stage: unit-test
#   cache:
#     key: ${CI_COMMIT_REF_SLUG}
#     paths:
#       - .poetry_cache/
#       - .venv/
#       - .pytest_cache/
#       - .coverage
#     policy: pull-push
#   script:
#     - poetry run pytest tests/unit --cov=src --cov-report=term --cov-report=html:coverage_html_unit
#   artifacts:
#     when: always
#     paths:
#       - pytest_reports/unit/
#       - coverage_html_unit/
#     expire_in: 1 week
#   coverage: '/^TOTAL.*\s+(\d+%)$/'
#   retry:
#     max: 2
#   only:
#     - merge_requests
#     - main

# test:integration:
#   stage: integration-test
#   needs:
#     - test:unit
#   cache:
#     key: ${CI_COMMIT_REF_SLUG}
#     paths:
#       - .poetry_cache/
#       - .venv/
#       - .pytest_cache/
#       - .coverage
#     policy: pull-push
#   services:
#     - name: neo4j:5.15
#       alias: neo4j
#       variables:
#         NEO4J_ACCEPT_LICENSE_AGREEMENT: "yes"
#         NEO4J_AUTH: "neo4j/test1234"
#         NEO4J_server_config_strict__validation_enabled: "false"
#         NEO4J_PLUGINS: '["apoc"]'
#         NEO4J_apoc_export_file_enabled: "true"
#         NEO4J_apoc_import_file_enabled: "true"
#         NEO4J_apoc_import_file_use__neo4j__config: "true"
#   variables:
#     NEO4J_URI: "bolt://neo4j:7687"
#     NEO4J_USER: "neo4j"
#     NEO4J_PASSWORD: "test1234"
#   script:
#     - >
#       until curl -s http://neo4j:7474 > /dev/null;
#       do
#         echo "Waiting for Neo4j to be ready..."
#         sleep 1
#       done
#     - poetry run pytest tests/integration --cov=src --cov-report=term --cov-report=html:coverage_html_integration
#   artifacts:
#     when: always
#     paths:
#       - pytest_reports/integration/
#       - coverage_html_integration/
#     expire_in: 1 week
#   coverage: '/^TOTAL.*\s+(\d+%)$/'
#   only:
#     - merge_requests
#     - main
# # build:package:
# #   stage: build
# #   needs:
# #     - test:integration
# #   script:
# #     - poetry build
# #   artifacts:
# #     paths:
# #       - dist/
# #     expire_in: 1 week
# #   only:
# #     - main

# # special sauce to make gitlab-ci work with artifactory: https://gitlab.dle.afrl.af.mil/dle-pipeline-templates/gitlab-artifactory/-/tree/main?ref_type=heads
# # publish:artifactory:
# #   tags:
# #     - community-dle-shared-runner # Tag to use the shared runner with certs
# #   stage: publish
# #   image:
# #     name: python:3.12-alpine
# #     entrypoint: [""]
# #   before_script:
# #     # Install necessary certificates and tools for secure communication
# #     - cp /dle/gitlab-runner/certs/*.crt /usr/local/share/ca-certificates/
# #     - update-ca-certificates --fresh > /dev/null # Update certificates to enable HTTPS connections
# #     - apk update && apk add openssl curl # Install OpenSSL for SSL, and curl for file transfers
# #     - curl -sSL https://install.python-poetry.org | python3 -
# #     - export PATH="$HOME/.local/bin:$PATH"
# #   script:
# #     # Upload the target file to the specified Artifactory repository using API authentication
# #     - poetry build
# #     - curl -v -H "X-JFrog-Art-Api:${ARTIFACTORY_API_TOKEN}" -T "dist/omnicat-0.0.1-py3-none-any.whl" "https://artifactory.dle.afrl.af.mil/artifactory/api/pypi/ngsx-pypi-local/"
# #     # - poetry config repositories.jfrog https://artifactory.dle.afrl.af.mil/artifactory/api/pypi/ngsx-pypi-local/
# #     # - poetry config http-basic.jfrog ${ARTIFACTORY_USER} ${ARTIFACTORY_API_TOKEN}
# #     # - poetry publish --repository jfrog -n
# #   only:
# #     - main
# # needs:
# #   - build:package
