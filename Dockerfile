# Use Python 3.12 slim image as base
FROM python:3.12-slim AS builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    POETRY_VERSION=1.7.1 \
    POETRY_HOME="/opt/poetry" \
    POETRY_VIRTUALENVS_IN_PROJECT=true \
    POETRY_NO_INTERACTION=1

# Add Poetry to PATH
ENV PATH="$POETRY_HOME/bin:$PATH"

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        curl \
        build-essential \
        openssh-client \
        git \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN curl -sSL https://install.python-poetry.org | python3 -

# Set working directory
WORKDIR /app

# Copy just the dependency files first
COPY pyproject.toml poetry.lock ./

# Setup SSH for git
RUN mkdir -p /root/.ssh && \
    ssh-keyscan gitlab.dle.afrl.af.mil >> /root/.ssh/known_hosts

# Copy the application
COPY . .

# Install only production dependencies
RUN --mount=type=ssh poetry install --without dev

# API service image
FROM python:3.12-slim AS api

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    VIRTUAL_ENV=/app/.venv \
    PATH="/app/.venv/bin:$PATH" \
    PYTHONPATH=/app/src

# Set working directory
WORKDIR /app

# Copy virtual environment and application from builder
COPY --from=builder /app/.venv ./.venv
COPY --from=builder /app/src ./src
COPY logging.json .

# Create and switch to non-root user
RUN useradd --create-home appuser
USER appuser

# Expose the port the app runs on
EXPOSE 8000

# Command to run the API application
CMD ["uvicorn", "omnicat.http.main:app", "--host", "0.0.0.0", "--port", "8000", "--log-config", "logging.json"]

# UDL Ingestor image
FROM python:3.12-slim AS udl-ingestor

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    VIRTUAL_ENV=/app/.venv \
    PATH="/app/.venv/bin:$PATH" \
    PYTHONPATH=/app/src

# Set working directory
WORKDIR /app

# Copy virtual environment and application from builder
COPY --from=builder /app/.venv ./.venv
COPY --from=builder /app/src ./src

# Create and switch to non-root user
RUN useradd --create-home appuser
USER appuser

# Command to run the UDL ingestor
CMD ["python", "-m", "omnicat.ingestor.udl.udl_ingestor"]