# Welcome to OmniCat

![Project Logo](omnicat.webp)

This package catalogs tracks, objects, states, etc. in an effort to assist with SDA track and threat analysis.

In particular, this package is designed to assist in correlating uncorrelated tracks.

## Quick Start
```
poetry install
poetry shell
```

## Secrets Management
OmniCat supports loading secrets from both Docker secrets (when running in containers) and local files (for development). The secrets are configured in [docker-compose.yml](docker-compose.yml) and loaded by the application settings.

For local development, you can create a `./secrets` directory in your project root and add secret files with the same names as the secret keys. For example:

```bash
./secrets/
  ├── udl_user           # Contains your UDL username
  └── udl_password       # Contains your UDL password
```

Each file should contain just the secret value with no newlines. The application will automatically load these secrets when running locally, and use Docker secrets when running in containers.

## Configuration Options
OmniCat uses Pydantic Settings to manage configuration, which supports multiple ways to set values. The settings are loaded in the following order of precedence (highest to lowest):

1. Environment variables (e.g., `NEO4J_PASSWORD=mysecret`)
2. Secrets directory (either `/run/secrets` in Docker or `./secrets` locally)
3. `.env` file in the project root
4. Default values in the code

For example, to configure Neo4j connection settings, you can:
- Set environment variables: `export NEO4J_PASSWORD=mysecret`
- Create a `.env` file:
  ```bash
  NEO4J_USER=neo4j
  NEO4J_PASSWORD=mysecret
  NEO4J_URI=bolt://localhost:7687
  ```
- Or use the secrets directory as described above

The same pattern applies to all configuration options in OmniCat, including database connections, API credentials, and service-specific settings.

## Documentation
View the api documentation [here](doc/api/OmniCat_API_Docs.md).

## Running the Swagger Web UI Locally for Development
```bash
docker-compose -f docker-compose.test-deps.yml up -d  # This will start both neo4j and postgis
NEO4j_URI=bolt://localhost:7688 POSTGIS_PORT=5433 fastapi run src/omnicat/http/main.py
```
Open a browser and navigate to http://127.0.0.1:8000/docs to interact with the API.
Navigate to http://localhost:7474 to see the state of the neo4j database.

## Running Unit Tests 
```bash
poetry run pytest --cov=omnicat tests/unit
```

## Building an OmniCat Docker Image
For the api server:
```bash
DOCKER_BUILDKIT=1 docker build --ssh default="/path/to/.ssh/id_rsa" --target api -t omnicat-api .
DOCKER_BUILDKIT=1 docker build --ssh default="/path/to/.ssh/id_rsa" --target api -t omnicat-udl-ingestor .
```

You need the ssh path because OmniCat depends on [orbitalopsengine](https://gitlab.dle.afrl.af.mil/space-superiority-sandbox/ngsx/engine/OrbitalOpsEngine) and [batchprop](https://gitlab.dle.afrl.af.mil/space-superiority-sandbox/ngsx/services/batchprop).


## Running Integration Tests Locally
```bash
# Start the test databases
docker-compose -f docker-compose.test-deps.yml up -d

# Run the tests
poetry run pytest tests/integration

# When done, stop the test databases
docker-compose -f docker-compose.test-deps.yml down
```

## Running with docker-compose
Note: first build the omnicat docker image with instructions in [#building-an-omnicat-docker-image].

The easiest way to run OmniCat is using docker-compose, which will automatically set up the OmniCat service, its required databases (Neo4j and PostGIS), and a UDL Ingestor to add UDL data to your databases.

You will need to put your UDL credentials in `secrets/udl_password.txt` and `secrets/udl_user.txt` so docker compose can pass them to the udl-ingestor container.

Your UDL credentials will not show up in a `docker inspect` or in the environment of the running container, but anyone who can `docker exec` into the udl-ingestor container will be able to read your credentials from `/run/secrets` inside the container.

Then you can build and run with `DOCKER_BUILDKIT=1 docker-compose up --build`, provided you edit the 'ssh: --default' fields to match the path to your id_rsa for the same reasons described in [#building-an-omnicat-docker-image].

```bash
# Start the services locally
docker-compose up -d

# View the logs
docker-compose logs -f

# Stop the services
docker-compose down

# Stop and remove all data volumes (useful for a clean start)
docker-compose down -v
```

Once running, you can access:
- OmniCat API and Swagger UI: http://localhost:8000/docs
- Neo4j Browser: http://localhost:7474

**Tip:** To visualize the graph structure in the Neo4j Browser, run the following command:
```cypher
CALL db.schema.visualization
```

The default credentials are:
- Neo4j:
  - Username: neo4j
  - Password: password
- PostGIS:
  - Username: gis
  - Password: password

## Ingesting UDL Data
This assumes you have a .env file with 
```bash
udl_user=your.username
udl_password=your.password
```
... or you have those environment variables defined when you run the ingestor.

### Ingesting Demo Data

To ingest demo data, you can run the `ingest_demo_data.py` script.

The script assumes 
- you have a UDL account and have set the `UDL_USER` and `UDL_PASSWORD` environment variables.
- you have a local neo4j database running and the `NEO4J_URI` environment variable set to the correct uri.


```bash
POSTGIS_PORT=5433 NEO4J_URI='bolt://localhost:7688' python src/omnicat/ingestor/udl/ingest_demo_data.py --max-correlated-elsets 50 --max-uncorrelated-elsets 50
```

This will ingest elsets from UDL and create objects and tracks in the database.

### Manually Running the Automated Ingestor Propagator

```bash
NEO4j_URI=bolt://localhost:7688 POSTGIS_PORT=5433 python src/omnicat/ingestor/udl/udl_ingestor.py
```

or add the above environment variables to `.env` file or the './secrets' directory.

## Publishing to Junior Lab
Assumes you have access to the Junior Lab machine and your ssh config contains:

```bash
Host juniorlab
  HostName ************
  IdentityFile ~/.ssh/id_rsa_juniorlab
  User pvanevery
  LocalForward 7474 localhost:7474
  LocalForward 7687 localhost:7687
  LocalForward 8000 localhost:8000 
```
... with the correct id_rsa_juniorlab in place.

You can use cicd.sh:

```bash
# To scp omnicat to the juniorlab server:
./cicd.sh --deliver --remote-host juniorlab --remote-docker-compose docker-compose.taplab.yml

# To scp omnicat to the juniorlab server and deploy it:
./cicd.sh --deliver --deploy --remote-host juniorlab --remote-docker-compose docker-compose.taplab.yml
```

Keep an open ssh session to the juniorlab machine and then via the LocalForward entries in the ssh config above you can access the neo4j database ui at http://localhost:7474.

Note: make sure you don't have a local neo4j or omnicat api running on the same ports.