#!/bin/bash

# Exit on any error
set -e

# Parse command line arguments
TEST=false
BUILD=false
DELIVER=false
DEPLOY=false
REMOTE_HOST=""
REMOTE_DOCKER_COMPOSE=""

show_help() {
    echo "Usage: ./cicd.sh [options]"
    echo "CI/CD script for Omnicat project"
    echo
    echo "Options:"
    echo "  --help                   Show this help message"
    echo "  --all                    Run all stages (test, build, deliver, deploy)"
    echo "  --test                   Run unit and integration tests"
    echo "  --build                  Build Docker image locally"
    echo "  --deliver                Package and transfer files to remote host (junior lab)"
    echo "  --deploy                 Deploy application on remote host (junior lab server)"
    echo "  --remote-host            Remote hostname for deployment (required for deliver/deploy)"
    echo "  --remote-docker-compose  Additional docker-compose file for remote deployment"
}

while [[ "$#" -gt 0 ]]; do
    case $1 in
        --help) show_help; exit 0 ;;
        --all)
            TEST=true
            BUILD=true
            DELIVER=true
            DEPLOY=true
            ;;
        --test) TEST=true ;;
        --build) BUILD=true ;;
        --deliver) DELIVER=true ;;
        --deploy) DEPLOY=true ;;
        --remote-host)
            REMOTE_HOST="$2"
            shift
            ;;
        --remote-docker-compose)
            REMOTE_DOCKER_COMPOSE="$2"
            shift
            ;;
        *) echo "Unknown parameter: $1"; show_help; exit 1 ;;
    esac
    shift
done

# Enable command printing
set -x

# Set variables
API_IMAGE_NAME="omnicat-api"
UDL_IMAGE_NAME="omnicat-udl-ingestor"
IMAGE_TAG="latest"

# Check if REMOTE_HOST is provided when needed
if [ "$DELIVER" = true ] || [ "$DEPLOY" = true ]; then
    if [ -z "$REMOTE_HOST" ]; then
        echo "Error: --remote-host is required for delivery or deployment"
        show_help
        exit 1
    fi
fi

# Check if REMOTE_DOCKER_COMPOSE exists if provided
if [ ! -z "$REMOTE_DOCKER_COMPOSE" ] && [ ! -f "$REMOTE_DOCKER_COMPOSE" ]; then
    echo "Error: Specified remote docker-compose file '$REMOTE_DOCKER_COMPOSE' does not exist"
    exit 1
fi

# Build the image if --build is set
if [ "$BUILD" = true ]; then
    echo "Building Docker images locally..."
    docker-compose build --no-cache
else
    echo "Skipping build. Use --build to force a new build."
fi

# Run tests if --test is set
if [ "$TEST" = true ]; then
    echo "Running unit tests..."
    poetry run pytest tests/unit
    
    # Check if test dependencies are already running
    TEST_DEPS_RUNNING=false
    if [ -n "$(docker-compose -f docker-compose.test-deps.yml ps -q 2>/dev/null)" ]; then
        echo "Test dependencies are already running"
        TEST_DEPS_RUNNING=true
    else
        echo "Starting test dependencies..."
        DOCKER_BUILDKIT=1 docker compose -f docker-compose.test-deps.yml up -d
        sleep 10
    fi
    
    echo "Running integration tests..."
    poetry run pytest tests/integration
    
    # Only shut down test dependencies if we started them
    if [ "$TEST_DEPS_RUNNING" = false ]; then
        echo "Shutting down test dependencies..."
        docker-compose -f docker-compose.test-deps.yml down
    else
        echo "Leaving test dependencies running as they were already up"
    fi
else
    echo "Skipping tests. Use --test to run unit and integration tests."
fi

# Create and deliver deployment package if --deliver is set
if [ "$DELIVER" = true ]; then
    echo "Saving Docker images to tar files..."
    docker save ${API_IMAGE_NAME}:${IMAGE_TAG} -o ${API_IMAGE_NAME}.tar
    docker save ${UDL_IMAGE_NAME}:${IMAGE_TAG} -o ${UDL_IMAGE_NAME}.tar

    echo "Creating deployment package..."
    mkdir -p omnicat_deploy
    cp docker-compose.yml omnicat_deploy/
    cp ${API_IMAGE_NAME}.tar omnicat_deploy/
    cp ${UDL_IMAGE_NAME}.tar omnicat_deploy/

       # Copy secrets directory
    if [ -d "./secrets" ]; then
        echo "Copying secrets directory..."
        cp -r ./secrets omnicat_deploy/
    else
        echo "Warning: ./secrets directory not found!"
        exit 1
    fi
    
    # Copy additional docker-compose file if provided
    if [ ! -z "$REMOTE_DOCKER_COMPOSE" ]; then
        cp "$REMOTE_DOCKER_COMPOSE" omnicat_deploy/
    fi

    echo "Transferring files to ${REMOTE_HOST}..."
    ssh ${REMOTE_HOST} "mkdir -p ~/omnicat_deploy"
    scp -r omnicat_deploy/* "${REMOTE_HOST}:~/omnicat_deploy/"

    if [ $? -eq 0 ]; then
        echo "Transfer completed successfully"
        rm -rf omnicat_deploy
        rm ${API_IMAGE_NAME}.tar
        rm ${UDL_IMAGE_NAME}.tar
    else
        echo "Transfer failed"
        rm -rf omnicat_deploy
        rm ${API_IMAGE_NAME}.tar
        rm ${UDL_IMAGE_NAME}.tar
        exit 1
    fi
else
    echo "Skipping delivery. Use --deliver to transfer files."
fi

# Deploy if --deploy is set
if [ "$DEPLOY" = true ]; then
    # Setup compose command parts
    COMPOSE_DOWN_CMD="docker compose -f docker-compose.yml"
    COMPOSE_UP_CMD="docker compose -f docker-compose.yml"
    
    # Add additional compose file if provided
    if [ ! -z "$REMOTE_DOCKER_COMPOSE" ]; then
        REMOTE_COMPOSE_FILENAME=$(basename "$REMOTE_DOCKER_COMPOSE")
        COMPOSE_DOWN_CMD="$COMPOSE_DOWN_CMD -f $REMOTE_COMPOSE_FILENAME"
        COMPOSE_UP_CMD="$COMPOSE_UP_CMD -f $REMOTE_COMPOSE_FILENAME"
    fi
    
    echo "Executing remote deployment..."
    ssh ${REMOTE_HOST} "
        cd ~/omnicat_deploy
        # Stop and remove existing containers
        $COMPOSE_DOWN_CMD down 
        # Remove existing images
        docker rmi ${API_IMAGE_NAME}:${IMAGE_TAG} || true
        docker rmi ${UDL_IMAGE_NAME}:${IMAGE_TAG} || true
        # Load the new Docker images
        docker load < ${API_IMAGE_NAME}.tar
        docker load < ${UDL_IMAGE_NAME}.tar
        # Start new containers
        $COMPOSE_UP_CMD up -d
    "
    echo "Deployment complete!"
else
    echo "Skipping deployment. Use --deploy to deploy the application."
fi