# OmniCat Service API Documentation

Welcome to the API documentation for the OmniCat Service. This API allows users to manage and query spacecraft objects, states, and tracks within a space-time framework. It also provides endpoints for managing metadata correlated with objects, states, and tracks, which are mutable and auditable.

OmniCat treats tracks as the core data type it catalogs, calculates, and manipulates.
Therefore, the service maintains correlations between tracks and other data types it catalogs.

Finally, the OmniCat API allows users to perform track computations like propagation and conjunction estimation.

---

# Table of Contents

1. [Definitions](#definitions)
2. [Data Types](#data-types)
    1. [Common Data Types](#common-data-types)
    2. [Object Data Type](#object-data-type)
    3. [Track Data Type](#track-data-type)
3. [Data Endpoints](#data-endpoints)
    1. [Object Data Endpoints](#object-data-endpoints)
       1. [Add Object](#add-object)
       2. [Get Object](#get-object)
    2. [Track Data Endpoints](#track-data-endpoints)
       1. [Add Track](#add-track)
       2. [Get Track](#get-track)
4. [Metadata Endpoints](#metadata-endpoints)
    1. [Object Metadata Endpoints](#object-metadata-endpoints)
       1. [Add Object Metadata](#add-object-metadata)
       2. [Get Object Metadata](#get-object-metadata)
       3. [Update Object Metadata](#update-object-metadata)
    2. [Track Metadata Endpoints](#track-metadata-endpoints)
       1. [Add Track Metadata](#add-track-metadata)
       2. [Get Track Metadata](#get-track-metadata)
       3. [Update Track Metadata](#update-track-metadata)
5. [Functional Endpoints](#functional-endpoints)
   1. [Correlate Track](#correlate-track)
   2. [Query Tracks Within AOI](#query-tracks-within-aoi)
   3. [Query Conjunctions Within AOI](#query-conjunctions-within-aoi)
   4. [Query Conjunctions For Track](#query-conjunctions-for-track)
   5. [Query For Uncorrelated Tracks](#query-for-uncorrelated-tracks)
6. [Error Handling](#error-handling)
7. [Units and Formats](#units-and-formats)
8. [Pagination](#pagination)
9. [Notes](#notes)
10. [Contact](#contact)

---

# Definitions

| Term                     | Definition                                                                                                         | Example                                                                                                         |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------ | --------------------------------------------------------------------------------------------------------------- |
| Object                   | an item in space                                                                                                   | the International Space Station                                                                                 |
| Observation              | raw data from a sensor, potentially containing objects                                                             | images of the ISS from a ground telescope                                                                       |
| State                    | general status of an object at a particular time, including ephemeris and possibly including attitude and metadata | the location of the ISS in its orbit and its attitude at a particular timestamp                                 |
| Correlation | connection between a track and an object                                                                           | a sensor reports an observed track for the ISS                                                                  |
| Synthetic State          | expected or propagated State                                                                                       | the propagated and expected location of the ISS along its track at a future timestamp                           |
| Synthetic Track          | calculated track for an object along which it has not been observed                                                | a hypothetical track computed for the ISS if it were to perform a particular maneuver                           |
| Track                    | a description of observed object motion, typically given as a TLE, state vector, ephemeris, etc.                   | ALTAIR detects the ISS, computes a LEO orbit element set, and publishes the computed TLE to UDL                 |
| Uncorrelated Track (UCT) | a track with no correlation to an object                                                                           | a sensor observes an object moving along a track, but the sensor cannot identify the object following the track |

---
# Data Types
## Common Data Types

### State Vector or RVT

A **state vector** or **rvt** (for position, velocity, time) is a timestamped position and velocity in the intertial reference frame (ECI).

Example:

```json
 "state_vector": {
    "position": [7000.0, 0.0, 0.0],
    "velocity": [0.0, 7.5, 1.0],
    "attitude": [1.0, 0, 0],
    "angular_velocity": [0, 3.14, 0],
    "epoch": "2023-10-05T12:30:00Z"
}
```

The optional "attitude" field contains Euler angles in radians.

The optional "angular_velocity" field contains the rate of change of the "attitude" field in radians per second.

Position components are given in kilometers. Velocity components are given in kilometers per second. The epoch is formatted as an ISO8601 datetime. 

### AOI

An **Area of Interest (AOI)** represents a spherical area in space over a duration of time.

Example:

```json
"area_of_interest": {
    "center": [6900, 0, 0],
    "radius": 200,
    "time_start": "2023-10-05T12:00:00Z",
    "time_end": "2023-10-05T13:00:00Z"
}
```

Position components are given in kilometers in the ECI reference frame.

Radius is given in kilometers.

Time components are in ISO 8601 format (UTC).

### Correlations

An **correlation** represents a connection between a [Track](#track-data-type) and an [Object](#object-data-type).

Example Object Correlation:

```json
{
    "object_id": "OBJ-789",
    "epoch": "2023-10-06T10:00:00Z",
    "confidence": 0.95,
    "metadata": {
        "source": "Dr. Smith",
        "explanation": "Based on trajectory similarity",
        "analysis_date": "2023-10-05T12:30:00Z"
    }
}
```

The "epoch" field denotes the time that the correlation was made.

The "confidence" field denotes probability that the correlation is correct.

The "metadata" field is optional and has no strict schema.

---
## Object Data Type

An object is an item in space.

Example:

```json
"object": {
  "data_uris": ["http://example.com/data/abc"],
  "metadata": {
    "International Designator": "2023-045A",
    "operator_notes": "New satellite deployment.",
    "country_of_origin": "CN",
    "area_to_mass_ratio": 2.718
  }
}
```

The "data_uris" field, containing uris describing data defining the object, is optional

The "metadata" field is optional and has no strict schema.

---
## Track Data Type

A **track** contains the information necessary to describe and propagate object motion. 

A track can be provided in either [state vector](#state-vector-or-rvt) or TLE format with correlated metadata.

Regardless of the input format, the underlying storage will always convert and store the track as a state vector in the Earth-Centered Inertial (ECI) frame.

### State Vector Format Example

```json
"track": {
    "source": "SENSOR-001",
    "format": "state_vector",
    "state_vector": {
        "position": [7000.0, 0.0, 0.0],
        "velocity": [0.0, 7.5, 1.0],
        "epoch": "2023-10-05T12:30:00Z"
    },
    "metadata": {
        "operator_notes": "Initial track observation.",
        "observation_uri": "http://example.com/observation/123"
    },
    "object_correlations" : [
      {
        "object_id": "OBJ-456",
        "epoch": "2023-10-05T12:30:00Z",
        "confidence": 0.65
      },
      {
        "object_id": "OBJ-456",
        "epoch": "2023-10-05T12:30:00Z",
        "confidence": 0.90
      }
    ]
}
```

The "metadata" field is optional and has no strict schema.

The "object_correlations" field, denoting [Correlations](#correlations) to objects, is optional.

The "source" field, denoting which entity originally computed and reported the track, is optional.

### Two Line Element (TLE) Format Example

```json
"track": {
  "source": "operator 345",
  "format": "tle",
  "tle": {
    "line1": "1 25544U 98067A   20062.54862276  .00001264  00000-0  29622-4 0  9994",
    "line2": "2 25544  51.6448  21.4733 0005589 270.4455 206.8124 15.49172153211639"
  },
  "epoch": "2023-10-05T12:30:00Z",
  "metadata": {
    "operator_notes": "TLE data provided.",
    "sensor_id": "SENSOR-002",
    "observation_uri": "http://example.com/observation/123"
  }
}
```

The "epoch" field is optional for a track formated as a TLE. If no separate "epoch" field is provided, the TLE line 1 epoch encoding will be used.

The "metadata" field is optional and has no strict schema.

---
# Data Endpoints
## Object Data Endpoints

[Object](#object-data-type) data endpoints allow for creation and retrieval of catalog objects.

### Add Object

#### Endpoint

```
POST /objects
```

#### Description

Creates a new object, optionally correlating it with existing data sources. You can also include metadata, such as the "International Designator."

#### Parameters

- **object** (required): The [Object](#object-data-type) to add.

#### Request Example

```json
POST /objects
Content-Type: application/json

{
  "data_uris": ["http://example.com/data/abc"],
  "metadata": {
    "International Designator": "2023-045A",
    "operator_notes": "New satellite deployment.",
    "country_of_origin": "CN",
    "area_to_mass_ratio": 2.718
  }
}
```

#### Response

- **201 Created**

```json
{
  "object_id": "OBJ-456",
  "message": "Object created successfully."
}
```

---

### Get Object

#### Endpoint

```
GET /objects/{identifier}
```

#### Description

Retrieves detailed information about a specific object, including correlated tracks, epochs, probabilities, and metadata. You can retrieve an object either by its `object_id` or by its `International Designator`.

#### Parameters

- **identifier** (required): The unique identifier of the object. This can be either the `object_id` (e.g., "OBJ-456") or the `International Designator` (e.g., "2023-045A").

#### Request Example

```
GET /objects/2023-045A
```

#### Response Example

```json
{
  "object_id": "OBJ-456",
  "object": {
    "data_uris": ["http://example.com/data/abc"],
    "metadata": {
      "International Designator": "2023-045A",
      "operator_notes": "New satellite deployment."
    }
  },
  "correlations": [
    {
      "track_id": "TRK-789",
      "epoch": "2023-10-05T12:30:00Z",
      "confidence": 0.95
    },
    {
      "track_id": "TRK-790",
      "epoch": "2023-10-05T13:00:00Z",
      "confidence": 0.90
    }
  ]
}
```

---

## Track Data Endpoints

[Track](#Track-Data-Type) data endpoints allow for creation and retrieval of catalog tracks.

### Add Track 

#### Endpoint

```
POST /tracks
```

#### Description

Adds a new track to the system. A track can be provided in either **state vector** format or **Two-Line Element (TLE)** format. Regardless of the input format, the underlying storage will always convert and store the track as a state vector in the Earth-Centered Inertial (ECI) frame.

#### Parameters

- **track** (required): The [Track](#Track-Data-Type) to be added.

#### Request Examples

##### Adding a Track Using State Vector

```json
POST /tracks
Content-Type: application/json

{
  "source": "SENSOR-001",
  "format": "state_vector",
  "state_vector": {
      "position": [7000.0, 0.0, 0.0],
      "velocity": [0.0, 7.5, 1.0],
      "epoch": "2023-10-05T12:30:00Z"
  },
  "metadata": {
      "operator_notes": "Initial track observation.",
      "observation_uri": "http://example.com/observation/123"
  },
  "object_correlations" : [
      {
          "object_id": "OBJ-456",
          "epoch": "2023-10-05T12:30:00Z",
          "confidence": 0.90
      }
  ]
}
```

##### Adding a Track Using TLE

```json
POST /tracks
Content-Type: application/json

{
  "source": "operator 345",
  "format": "tle",
  "tle": {
      "line1": "1 25544U 98067A   20062.54862276  .00001264  00000-0  29622-4 0  9994",
      "line2": "2 25544  51.6448  21.4733 0005589 270.4455 206.8124 15.49172153211639"
  },
  "epoch": "2023-10-05T12:30:00Z",
  "metadata": {
      "operator_notes": "TLE data provided.",
      "sensor_id": "SENSOR-002",
      "observation_uri": "http://example.com/observation/123"
  }
}
```

#### Response

- **201 Created**

```json
{
  "track_id": "TRK-789",
  "message": "Track added successfully."
}
```

---

### Get Track 

#### Endpoint

```
GET /tracks/{track_id}
```

#### Description

Retrieves detailed information about a specific track, including correlations, probabilities, and metadata. You can specify the format in which the track data should be returned.

#### Parameters

- **track_id** (required): The unique identifier of the track.
- **format** (optional): Specifies the format of the track data in the response. Accepted values are `"state_vector"` (default) or `"tle"`.

#### Request Example

```
GET /tracks/TRK-789?format=tle
```

#### Response Examples

##### Response with State Vector (Default)

```json
{
  "track_id": "TRK-789",
  "track": {
    "source": "SENSOR-001",
    "format": "state_vector",
    "state_vector": {
        "position": [7000.0, 0.0, 0.0],
        "velocity": [0.0, 7.5, 1.0],
        "epoch": "2023-10-05T12:30:00Z"
    },
    "metadata": {
        "operator_notes": "Initial track observation.",
        "observation_uri": "http://example.com/observation/123"
    },
    "object_correlations" : [
      {
        "object_id": "OBJ-456",
        "epoch": "2023-10-05T12:30:00Z",
        "confidence": 0.65
      },
      {
        "object_id": "OBJ-456",
        "epoch": "2023-10-05T12:30:00Z",
        "confidence": 0.90
      }
    ]
  }
}
```

##### Response with TLE

```json
{
  "track_id": "TRK-789",
  "track": {
    "source": "operator 345",
    "format": "tle",
    "tle": {
      "line1": "1 25544U 98067A   20062.54862276  .00001264  00000-0  29622-4 0  9994",
      "line2": "2 25544  51.6448  21.4733 0005589 270.4455 206.8124 15.49172153211639"
    },
    "epoch": "2023-10-05T12:30:00Z",
    "metadata": {
      "operator_notes": "TLE data provided.",
      "sensor_id": "SENSOR-002",
      "observation_uri": "http://example.com/observation/123"
    }
  }
}
```

---
# Metadata Endpoints
## Object Metadata Endpoints

These endpoints allow you to manage metadata correlated with [Objects](#object-data-type). Metadata is mutable and auditable, with all changes being tracked over time. The "International Designator" is a special metadata property for objects, and objects can be retrieved using this identifier.

### Add Object Metadata

#### Endpoint

```
POST /objects/{identifier}/metadata
```

#### Description

Adds metadata to a specific object. If the metadata key already exists, a new entry is added to the metadata history. The "International Designator" can be set or updated through this endpoint.

#### Parameters

- **identifier** (required): The unique identifier of the object. This can be either the `object_id` or the `International Designator`.
- **metadata** (required): A JSON object containing key-value pairs representing metadata entries.

#### Request Example

```json
POST /objects/OBJ-456/metadata
Content-Type: application/json

{
  "metadata": {
    "International Designator": "2023-045A",
    "classification": "Classified",
    "operator_notes": "Object requires special handling."
  }
}
```

#### Response

- **201 Created**

```json
{
  "message": "Metadata added to object OBJ-456 successfully."
}
```

---

### Get Metadata for Object

#### Endpoint

```
GET /objects/{identifier}/metadata
```

#### Description

Retrieves all metadata correlated with a specific object, including historical entries.

#### Parameters

- **identifier** (required): The unique identifier of the object. This can be either the `object_id` or the `International Designator`.

#### Response Example

```json
{
  "object_id": "OBJ-456",
  "metadata": {
    "International Designator": [
      {
        "value": "2023-045A",
        "timestamp": "2023-10-06T10:00:00Z"
      }
    ],
    "classification": [
      {
        "value": "Unclassified",
        "timestamp": "2023-10-05T12:00:00Z"
      },
      {
        "value": "Classified",
        "timestamp": "2023-10-06T10:00:00Z"
      }
    ],
    "operator_notes": [
      {
        "value": "Object requires special handling.",
        "timestamp": "2023-10-06T10:00:00Z"
      }
    ]
  }
}
```

---

### Update Metadata for Object

#### Endpoint

```
PUT /objects/{identifier}/metadata
```

#### Description

Updates metadata for a specific object by adding new entries to the metadata history. The "International Designator" can be updated through this endpoint.

#### Parameters

- **identifier** (required): The unique identifier of the object. This can be either the `object_id` or the `International Designator`.
- **metadata** (required): A JSON object containing key-value pairs representing metadata entries to update.

#### Request Example

```json
PUT /objects/2023-045A/metadata
Content-Type: application/json

{
  "metadata": {
    "International Designator": "2023-045B",
    "classification": "Top Secret",
    "operator_notes": "Access restricted."
  }
}
```

#### Response

- **200 OK**

```json
{
  "message": "Metadata for object OBJ-456 updated successfully."
}
```

---
## Track Metadata Endpoints

These endpoints allow you to manage metadata correlated with tracks. Metadata is mutable and auditable, with all changes being tracked over time. 

### Add Track Metadata

#### Endpoint

```
POST /tracks/{track_id}/metadata
```

#### Description

Adds metadata to a specific track. If the metadata key already exists, a new entry is added to the metadata history.

#### Parameters

- **track_id** (required): The unique identifier of the track.
- **metadata** (required): A JSON object containing key-value pairs representing metadata entries.

#### Request Example

```json
POST /tracks/TRK-789/metadata
Content-Type: application/json

{
  "metadata": {
    "status": "verified",
    "operator_notes": "Initial observation confirmed."
  }
}
```

#### Response

- **201 Created**

```json
{
  "message": "Metadata added to track TRK-789 successfully."
}
```

---

### Get Metadata for Track

#### Endpoint

```
GET /tracks/{track_id}/metadata
```

#### Description

Retrieves all metadata correlated with a specific track, including historical entries.

#### Parameters

- **track_id** (required): The unique identifier of the track.

#### Response Example

```json
{
  "track_id": "TRK-789",
  "metadata": {
    "status": [
      {
        "value": "pending",
        "timestamp": "2023-10-05T12:30:00Z"
      },
      {
        "value": "verified",
        "timestamp": "2023-10-06T09:15:00Z"
      }
    ],
    "operator_notes": [
      {
        "value": "Initial observation confirmed.",
        "timestamp": "2023-10-06T09:15:00Z"
      }
    ],
    "sensor_id": [
      {
        "value": "SENSOR-001",
        "timestamp": "2023-10-05T12:30:00Z"
      }
    ]
  }
}
```

---

### Update Metadata for Track

#### Endpoint

```
PUT /tracks/{track_id}/metadata
```

#### Description

Updates metadata for a specific track by adding new entries to the metadata history.

#### Parameters

- **track_id** (required): The unique identifier of the track.
- **metadata** (required): A JSON object containing key-value pairs representing metadata entries to update.

#### Request Example

```json
PUT /tracks/TRK-789/metadata
Content-Type: application/json

{
  "metadata": {
    "status": "archived",
    "operator_notes": "Track archived after review."
  }
}
```

#### Response

- **200 OK**

```json
{
  "message": "Metadata for track TRK-789 updated successfully."
}
```

---
# Functional Endpoints
## Correlate Track

### Endpoint

```
POST /correlations
```

### Description

Connects a track with a state or object, optionally specifying the probability of correlation.

### Parameters

- **track_id** (required): The track ID to correlate.
- **correlation_type** (required): The type of correlate (object).
- **object_correlation** (optional): The [Object Correlation](#correlations).

### Request Example with Object Correlation 

```json
POST /correlate
Content-Type: application/json

{
  "track_id": "TRK-791",
  "correlation_type": "object",
  "object_correlation": {
    "object_id": "OBJ-456",
    "confidence": 0.85,
    "metadata": {
      "explanation": "Based on trajectory similarity.",
      "analyst": "Dr. Smith",
      "analysis_date": "2023-10-06T10:00:00Z"
    }
  }
}
```

---
## Query Tracks Within AOI

### Endpoint

```
GET /tracks/query
```

### Description

Retrieves all tracks within a specified [AOI](#aoi).

### Parameters

- **center** (required): An array `[x_min, y_min, z_min]` in kilometers (ECI frame) representing the center of the [AOI](#aoi) sphere.
- **radius** (required): Radius of [AOI](#aoi) sphere in kilometers.
- **time_start** (required): The start time in ISO 8601 format (UTC).
- **time_end** (required): The end time in ISO 8601 format (UTC).
- **format** (optional): Specifies the format of the track data in the response. Accepted values are `"state_vector"` (default) or `"tle"`.
- **page** (optional): Page number for pagination (default is 1).
- **page_size** (optional): Number of records per page (default is 50).

### Request Example

```
GET /tracks/query?center=[6900,0,0]&radius=200&time_start=2023-10-05T12:00:00Z&time_end=2023-10-05T13:00:00Z&format=state_vector&page=1&page_size=20
```

### Response Example

```json
{
  "tracks": [
    {
      "track_id": "TRK-789",
      "format": "state_vector",
      "state_vector": {
        "position": [7000.0, 0.0, 0.0],
        "velocity": [0.0, 7.5, 1.0],
        "epoch": "2023-10-05T12:30:00Z",
      },
      "metadata": { ... }
    }
    // Additional track objects
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total_pages": 3,
    "total_records": 60
  }
}
```

---
## Query Conjunctions Within AOI

### Endpoint

```
GET /conjunctions/query
```

### Description

Retrieves conjunctions (close approaches) within a specified [AOI](#aoi), with optional filters on distance and relative velocity.

### Parameters

- **center** (required): An array `[x_min, y_min, z_min]` in kilometers (ECI frame) representing the center of the [AOI](#aoi) sphere.
- **radius** (required): Radius of [AOI](#aoi) sphere in kilometers.
- **time_start** (required): The start time in ISO 8601 format (UTC).
- **time_end** (required): The end time in ISO 8601 format (UTC).
- **max_distance** (optional): Maximum distance in kilometers for conjunctions.
- **max_relative_velocity** (optional): Maximum relative velocity in km/s.
- **page** (optional): Page number for pagination (default is 1).
- **page_size** (optional): Number of records per page (default is 50).

### Request Example

```
GET /conjunctions/query?center=[6900,0,0]&radius=200&time_start=2023-10-05T12:00:00Z&time_end=2023-10-05T13:00:00Z&max_distance=10&max_relative_velocity=10
```

### Response Example

```json
{
  "conjunctions": [
    {
      "track_a_id": "TRK-123",
      "track_b_id": "TRK-456",
      "closest_approach_distance": 5.2,
      "relative_velocity": 8.5,
      "time_of_closest_approach": "2023-10-05T12:45:00Z"
    }
    // Additional conjunction objects
  ],
  "pagination": {
    "page": 1,
    "page_size": 50,
    "total_pages": 1,
    "total_records": 10
  }
}
```

---
## Query Conjunctions for Track

### Endpoint

```
GET /conjunctions/{track_id}
```

### Description

Retrieves conjunctions (close approaches) for a track within a specified time interval, with optional filters on distance and relative velocity.

### Parameters

- **time_start** (required): The start time in ISO 8601 format (UTC).
- **time_end** (required): The end time in ISO 8601 format (UTC).
- **max_distance** (optional): Maximum distance in kilometers for conjunctions.
- **max_relative_velocity** (optional): Maximum relative velocity in km/s.
- **page** (optional): Page number for pagination (default is 1).
- **page_size** (optional): Number of records per page (default is 50).

### Request Example

```
GET /conjunctions/TRK-314?time_start=2023-10-05T12:00:00Z&time_end=2023-10-05T13:00:00Z&max_distance=10&max_relative_velocity=10
```

### Response Example

```json
{
  "conjunctions": [
    {
      "track_a_id": "TRK-314",
      "track_b_id": "TRK-456",
      "closest_approach_distance": 5.2,
      "relative_velocity": 8.5,
      "time_of_closest_approach": "2023-10-05T12:45:00Z"
    }
    // Additional conjunction objects
  ],
  "pagination": {
    "page": 1,
    "page_size": 50,
    "total_pages": 1,
    "total_records": 10
  }
}
```

---

## Query For Uncorrelated Tracks

### Endpoint

```
GET /tracks/uncorrelated
```

### Description

Retrieves all tracks that are not correlated with any object within an optionally specified time interval.

### Parameters

- **time_start** (optional): The start time in ISO 8601 format (UTC).
- **time_end** (optional): The end time in ISO 8601 format (UTC).
- **format** (optional): Specifies the format of the track data in the response. Accepted values are `"state_vector"` (default) or `"tle"`.
- **page** (optional): Page number for pagination (default is 1).
- **page_size** (optional): Number of records per page (default is 50).

### Request Example

```
GET /tracks/uncorrelated?time_start=2023-10-05T12:00:00Z&time_end=2023-10-05T13:00:00Z&format=tle&page=1&page_size=20
```

### Response Example

```json
{
  "tracks": [
    {
      "track_id": "TRK-792",
      "format": "tle",
      "tle": {
        "line1": "1 25544U 98067A   20062.54862276  .00001264  00000-0  29622-4 0  9994",
        "line2": "2 25544  51.6448  21.4733 0005589 270.4455 206.8124 15.49172153211639"
      },
      "metadata": { ... }
    }
    // Additional uncorrelated track objects
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total_pages": 2,
    "total_records": 40
  }
}
```

---
## Correlate Track 

### Endpoint

```
POST /tracks/correlate
```

### Description

Correlates a given [Track](#track-data-type) against the catalog to find potential matches based on specified tolerances. 

### Parameters

- **track** (required): The [Track](#track-data-type) to correlate.
- **position_tolerance** (optional): A numerical value in kilometers.
- **velocity_tolerance** (optional): A numerical value in km/s.

### Request Examples

#### Correlate Using State Vector

```json
POST /tracks/correlate
Content-Type: application/json

{
  "track": {
    "format": "state_vector",
    "state_vector": {
        "position": [7000.0, 0.0, 0.0],
        "velocity": [0.0, 7.5, 1.0],
        "epoch": "2023-10-05T12:30:00Z"
    }
  },
  "position_tolerance": 5.0,
  "velocity_tolerance": 0.5
}
```

#### Correlate Using TLE

```json
POST /tracks/correlate
Content-Type: application/json

{
  "track" : {
    "format": "tle",
    "tle": {
      "line1": "1 25544U 98067A   20062.54862276  .00001264  00000-0  29622-4 0  9994",
      "line2": "2 25544  51.6448  21.4733 0005589 270.4455 206.8124 15.49172153211639"
    }
  },  
  "tolerances": {
    "position_tolerance": 5.0,
    "velocity_tolerance": 0.5
  }
}
```

### Response Example

```json
{
  "potential_matches": [
    {
      "track_id": "TRK-789",
      "object_id": "OBJ-456",
      "match_score": 0.98
    }
    // Additional potential match objects
  ]
}
```

---
## Error Handling

All endpoints will return standard HTTP status codes to indicate the success or failure of an API request.

- **200 OK**: The request was successful.
- **201 Created**: A new resource was successfully created.
- **400 Bad Request**: The request was invalid or cannot be served. An error message will be provided.
- **404 Not Found**: The requested resource could not be found.
- **422 Unprocessable Entity**: The request is well formed but contains semantic errors.
- **500 Internal Server Error**: An error occurred on the server.

### Error Response Format

```json
{
  "error": {
    "code": 400,
    "message": "Invalid input: 'epoch' is required."
  }
}
```

---

## Units and Formats

- **Position and Velocity**: All position vectors are in kilometers (km), and velocity vectors are in kilometers per second (km/s), in the Earth-Centered Inertial (ECI) frame.
- **Attitude and Angular Velocity**: All angles are Euler angles in radians in the ECI reference frame, and angular velocities are in radians per second (rad/s).
- **Time**: All timestamps are in ISO 8601 format (UTC).
- **Formats**:
  - **State Vector**: Represents the ephemeris and attitude in ECI coordinates.
  - **TLE (Two-Line Element Set)**: A standard format encoding a list of orbital elements of an Earth-orbiting object.

---

## Pagination

For endpoints that support pagination, the following query parameters are used:

- **page**: The page number (starting from 1).
- **page_size**: The number of records per page.

Pagination details are provided in the response under the `"pagination"` field.

---

## Notes

- **Correlations**: Correlations between tracks and objects can be activated or deactivated but not deleted.
- **Data Mutability**: Tracks and objects are immutable in terms of their core data. Metadata correlated with tracks and objects is mutable and can be managed through the metadata endpoints. All changes to metadata are auditable, with historical entries preserved.
- **Metadata History**: When metadata is updated, previous values are retained in the history along with timestamps. This allows for full auditing of metadata changes over time.
- **International Designator**: This is a special metadata property for objects. You can retrieve and update an object by its International Designator as well as by its `object_id`. Changes to the International Designator are tracked in the metadata history.
- **Format Parameter**: The `format` parameter allows clients to specify the format of track data in requests and responses. Accepted values are `"state_vector"` and `"tle"`. The underlying storage always converts and stores tracks as state vectors in the ECI frame.