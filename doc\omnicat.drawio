<mxfile host="65bd71144e">
    <diagram id="m4O8xmtsTJFDvL5M2JS-" name="Page-1">
        <mxGraphModel dx="1177" dy="538" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="4" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#52A2D8;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="160" y="310" width="160" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;Neo4J&lt;/font&gt;" style="shape=datastore;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="185" y="335" width="110" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#52A2D8;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="880" y="310" width="160" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;PostGIS&lt;/font&gt;" style="shape=datastore;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="905" y="335" width="110" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;Ingestor/Propagator&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#52A2D8;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="480" y="350" width="240" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="&lt;span style=&quot;font-size: 24px;&quot;&gt;API&lt;br&gt;&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#52A2D8;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="480" y="550" width="240" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="" style="endArrow=classic;startArrow=classic;html=1;fontSize=24;entryX=0;entryY=0;entryDx=0;entryDy=0;" parent="1" target="25" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="320" y="470" as="sourcePoint"/>
                        <mxPoint x="480" y="470" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="32" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;Visualizer&lt;br&gt;&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1E4074;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="480" y="710" width="240" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="33" value="" style="endArrow=classic;startArrow=classic;html=1;fontSize=24;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="25" target="32" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="330" y="480" as="sourcePoint"/>
                        <mxPoint x="490" y="520" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="34" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;Simulation File&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="770" y="640" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="" style="endArrow=classic;html=1;fontSize=24;exitX=1;exitY=1;exitDx=0;exitDy=0;fillColor=#1ba1e2;strokeColor=#006EAF;" parent="1" source="25" target="34" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="550" y="600" as="sourcePoint"/>
                        <mxPoint x="600" y="550" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="" style="endArrow=classic;html=1;fontSize=24;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0;entryDx=0;entryDy=0;fillColor=#1ba1e2;strokeColor=#006EAF;" parent="1" source="34" target="32" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="730" y="640" as="sourcePoint"/>
                        <mxPoint x="780" y="653.3333333333334" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="37" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;UDL&lt;br&gt;&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#52A2D8;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="480" y="170" width="240" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="40" value="" style="endArrow=classic;startArrow=classic;html=1;fontSize=24;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="600" y="630" as="sourcePoint"/>
                        <mxPoint x="600" y="710" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="41" value="" style="endArrow=classic;startArrow=classic;html=1;fontSize=24;entryX=0;entryY=0;entryDx=0;entryDy=0;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="320" y="470" as="sourcePoint"/>
                        <mxPoint x="480" y="550" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="42" value="" style="endArrow=classic;html=1;fontSize=24;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0;entryDx=0;entryDy=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=4;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="770" y="700" as="sourcePoint"/>
                        <mxPoint x="720" y="710" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="43" value="" style="endArrow=classic;html=1;fontSize=24;exitX=1;exitY=1;exitDx=0;exitDy=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=4;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="720" y="630" as="sourcePoint"/>
                        <mxPoint x="770" y="643.3333333333334" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="46" value="" style="endArrow=classic;startArrow=classic;html=1;fontSize=24;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=4;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="600" y="630" as="sourcePoint"/>
                        <mxPoint x="600" y="710" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="47" value="" style="endArrow=classic;startArrow=classic;html=1;fontSize=24;entryX=0;entryY=0;entryDx=0;entryDy=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=4;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="320" y="470" as="sourcePoint"/>
                        <mxPoint x="480" y="550" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="51" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#6c8ebf;strokeWidth=4;fillColor=#dae8fc;" edge="1" parent="1" source="21" target="4">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="570" y="360" as="sourcePoint"/>
                        <mxPoint x="620" y="310" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="52" value="" style="endArrow=classic;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#6c8ebf;strokeWidth=4;fillColor=#dae8fc;" edge="1" parent="1" source="21" target="17">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="730" y="400" as="sourcePoint"/>
                        <mxPoint x="330" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="53" value="" style="endArrow=classic;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeColor=#6c8ebf;strokeWidth=4;fillColor=#dae8fc;" edge="1" parent="1" source="37" target="21">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="730" y="400" as="sourcePoint"/>
                        <mxPoint x="890" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="54" value="" style="endArrow=classic;html=1;entryX=1;entryY=0;entryDx=0;entryDy=0;exitX=0;exitY=1;exitDx=0;exitDy=0;strokeColor=#6c8ebf;strokeWidth=4;fillColor=#dae8fc;" edge="1" parent="1" source="17" target="25">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="730" y="400" as="sourcePoint"/>
                        <mxPoint x="890" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>