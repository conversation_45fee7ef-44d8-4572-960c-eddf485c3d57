# Contents

- [Contents](#contents)
- [Vision](#vision)
  - [Vision for OmniCat in the Context of OTTI NFL to GEO Simulation Mission:](#vision-for-omnicat-in-the-context-of-otti-nfl-to-geo-simulation-mission)
  - [Progress on OmniCat's Vision as of 2/7/2025](#progress-on-omnicats-vision-as-of-272025)
    - [AOI Query Demo](#aoi-query-demo)
    - [Conjunction Analysis Demo](#conjunction-analysis-demo)
  - [Integration with NGSX Visualization Component](#integration-with-ngsx-visualization-component)
  - [Data Storage Strategy](#data-storage-strategy)
  - [Data Access Strategy](#data-access-strategy)
  - [Data Ingestion Strategy](#data-ingestion-strategy)
  - [OmniCat Data Content Strategy](#omnicat-data-content-strategy)
- [Functions](#functions)
  - [1. RSO Data Store:](#1-rso-data-store)
  - [2. Spatio-Temporal Analytics:](#2-spatio-temporal-analytics)
  - [3. UCT Analytics:](#3-uct-analytics)
  - [4. Front End Translator/Formatter:](#4-front-end-translatorformatter)
- [Architecture](#architecture)

# Vision
OmniCat is a modular, extensible, general purpose space catalog that supports spatio-temporal and UCT analytics.

OmniCat presents several advantages over other catalogs for NGSX:
- OmniCat data formats are specialized for NGSX's unique performance and simulation use cases.
- The use of a graph database allows for easy extensibility to support new data types and query strategies.
- The use of PostGIS allows for high performance spatio-temporal queries.
- Finally, the modular design of OmniCat allows it to support multiple api frameworks as needed by NGSX.


## Vision for OmniCat in the Context of OTTI NFL to GEO Simulation Mission:
Omnicat can support OTTI NFL to GEO simulation mission in NGSX in several important ways:
- First, OmniCat will be able to store simulated NFL data coming from a myriad of simulated sensors for high speed playback during simulation visualization.
- Second, OmniCat will be able to perform conjunction analysis and clearing for the NFL payload as it moves from NFL to GEO.
- Finally, OmniCat will be able to catalog and perform spatio-temporal analytics for the NFL payload once it reaches a GEO orbit.

## Progress on OmniCat's Vision as of 2/7/2025
Preliminary prototypes demonstrating UDL ingestion, conjunction analysis, and spatio-temporal queries were exhibited at the TAP Lab Cohort 5 Expo on 1/29/2025.


These preliminary prototypes can be polished to a production ready state with about two weeks of effort.

### AOI Query Demo
Querying which objects in LEO will pass over Ukraine and the surrounding area over a 30 minute time period: 
![AOI Query Demo](./aoi_query_demo.png)

### Conjunction Analysis Demo
Querying which objects will pass within 200 km of one another over a 30 minute time period:
![Conjunction Analysis Demo](./conjunction_query_demo.png)


## Integration with NGSX Visualization Component

We will build a compiled client in Unreal that NGSX can use to read data from OmniCat. 

To start, the client will be able to read precompiled simulation results from
files written by OmniCat (see [Architecture](#architecture)).
By reading and displaying precomputed simulation data
from an OmniCat produced file, the visualizer will be able to run at lightning
execution speeds because it will be free from the burden of real time
propagation, sensor data, and event computation.

In the future, the OmniCat client can be extended to read data directly from the OmniCat datastore.

In either case, the OmniCat client will be able to feed data to the NGSX visualizer in any desired format, including ECS structures.
When dealing with precomputed simulation data, the OmniCat service can write files in an ECS friendly format for the NGSX visualizer to read.
When dealing with data read directly from the OmniCat datastore, the OmniCat client can request ECS formatted data from the service or translate OmniCat query data into ECS structures.


## Data Storage Strategy
OmniCat data structures are different from UDL.

UDL employs a multi-purpose, multi-tiered, and multi-source data index geared toward serving broad and diverse SDA community needs.
The resulting entity relationship schema is notoriously complex, redundant, difficult to navigate, and difficult to extend.

OmniCat indexes all data in a spacetime centric format to make it easy to index in support of the high performance demands of the NGSX visualizer.

OmniCat data storage is centered around the concept of a Track, which has correlated Objects.

When ingesting data from UDL or any other external source, OmniCat removes extraneous information and stores only the core data that NGSX needs for spatio-temporal analytics and visualization. OmniCat's Metadata storage allows for non spatio-temporal data to be stored with each Track and Object as needed.

![OmniCat Data Schema](./neo4j.png)

## Data Access Strategy
Currently, the fundamental data element in OmniCat is a Track, which has correlated Objects. The existing API (referenced in the [API Document](./api/OmniCat_API_Docs.md)) supports generic operations across all Tracks, Objects.

Moving forward, a rich framework of integration tests and established API infrastructure libraries will make the addition of further query strategies needed for OTTI/NGSX trivial.
Tell us how you want to query data and we'll be able to add API support with minimal effort.

## Data Ingestion Strategy
Data from UDL (and other external catalogs) will be refreshed/ingested into OmniCat by the Ingestor component on a configurable basis. 

As of 3/17, the Ingestor is deployed as part of the OmniCat docker-compose.

Currently, the Ingestor uses UDL user credentials stored in Docker Secrets to index the _current elsets_ endpoint.

The Ingestor maintains rolling time window of cataloged and propagated RSO states by:
- reading in a batch of current elsets for the current time window
- getting any correlated OnOrbit objects for the elset batch from the UDL 
- adding the OnOrbit nodes to the Neo4J database 
- adding the ElSet nodes to the Neo4j database with correlations (Neo4J relationships) to OnOrbit nodes
- propagating the batch of elsets at a certain time step within the time window
- adding all of the propagated states to the PostGIS database
- deleting entries outside of the time window (old states) from the PostGIS database

A user can then query OmniCat via it's API within the current time window for Tracks in an AOI or Tracks appearing in a Conjunction.

Currently configurable Ingestor parameters include:
- UDL endpoints and credentials
- the rate with which we query UDL data
- the batch size we index and propagate
- the number of days forward and backward from the current time we maintain in the rolling time window
- the period between UDL syncs
- the period between propagated points we maintain in PostGIS

As a starting point, we'll maintain a 30 rolling window of data 4 days behind and 26 days ahead of the current time 
with States for RSOs propagated at 300s apart.

## OmniCat Data Content Strategy
Data from both commercial and military sources can be ingested into OmniCat.

As a launching point, OmniCat aims to ingest everything from UDL.

# Functions

OmniCat currently targets 4 primary functions.

## 1. RSO Data Store:

OmniCat can store both external real data and internally simulated data needed for high performance NGSX simulation scenarios.

OmniCat can also store metadata, including data origins and lifecycle events for the objects it catalogs.

## 2. Spatio-Temporal Analytics:
OmniCat can perform conjunction detection and analysis for the objects it catalogs.
For instance, OmniCat will be able to perform conjunction analysis and clearing for an NFL host vehicle as it moves toward GEO and for it's payload in orbit.

OmniCat can also perform spatio-temporal queries to answer questions like:
- "What objects will fly over an NFL launch site during the launch window?"
- "Where will a particular object be during a scheduled NFL launch window?"
- "In which time window will there be the fewest number of LEO sats above my launch site over the next 24 hours?"

## 3. UCT Analytics:
OmniCat can identify UCTs and perform UCT deduplication for the objects it catalogs.

## 4. Front End Translator/Formatter:
OmniCat can translate data from external sources into custom internal formats used by the NGSX visualizer, including ECS structures.

OmniCat can also write data in whichever custom file formats best serve the NGSX visualizer.

# Architecture
![OmniCat Internal Architecture](./omnicat.png)

