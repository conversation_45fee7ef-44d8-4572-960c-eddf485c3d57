# Visualizer Interface Specification

This document describes how the visualizer component reads data from input files.

## Overview

The visualizer reads data from a set of files that represent a simulation of space objects, sensors, and events. The primary file format is CSV, which contains metadata, sensor definitions, and time series data for various objects. Additional JSON files provide supplementary information about rockets, ground stations, and events.

## File Structure

### Main CSV File

The main CSV file contains three main sections:

1. **Header Section**: Contains simulation metadata parameters
2. **Sensor Definitions**: Multiple sensor definitions
3. **Time Series Data**: Position, velocity, and orientation data for objects over time

### JSON Files

Several JSON files complement the main CSV file:

1. **Rockets.json**: Defines rocket models, stages, burns, and payloads
2. **Events.json**: Defines events such as collisions that occur during the simulation
3. **GroundStations.json**: Defines ground-based sensors with location and coverage information

## CSV File Format Details

### Header Section

The header section is delimited by `HEADER_START` and `HEADER_END` tags and contains simulation parameters in a variable-value format:

```
HEADER_START
variable_name,value
time_start,2024-08-21 00:00:00
time_end,2024-08-21 03:16:40
number_of_timesteps,196
timestep_length_seconds,60
HEADER_END
```

### Sensor Definitions

Each sensor definition is delimited by `SENSOR_START` and `SENSOR_END` tags:

```
SENSOR_START
id,1
name,SBSS_1
type,eo
x,1
y,1
z,1
pitch,0
yaw,0
roll,0
max_pitch,-90,90
max_yaw,-90,90
max_roll,-180,180
pixel_x,512
pixel_y,512
fov_x,0.308312
fov_y,0.308312
associated_sats,SBSS_1
SENSOR_END
```

### Time Series Data

The time series data follows the sensor definitions and consists of rows with the following columns:
1. SatNo - Object identifier
2. timestamp - Time in seconds from simulation start
3. x - X position coordinate
4. y - Y position coordinate 
5. z - Z position coordinate
6. vx - X velocity component (optional)
7. vy - Y velocity component (optional)
8. vz - Z velocity component (optional)
9. rot_x - X rotation (optional)
10. rot_y - Y rotation (optional)
11. rot_z - Z rotation (optional)
12. sensors - JSON-embedded list of sensors detecting the object (optional)
13. fused_x - Fused X position (optional)
14. fused_y - Fused Y position (optional)
15. fused_z - Fused Z position (optional)
16. entry_type - Type of entry (e.g., "Propagated") (optional)

```
SatNo,timestamp,x,y,z,vx,vy,vz,rot_x,rot_y,rot_z,sensors,fused_x,fused_y,fused_z,entry_type
ASAT_TARGET_1,0,-7730.65371455598,-36578.08308865079,19477.23365068704,-2.9471934577475265,-0.8069253793897389,0.345502215298435,244.88012159659263,247.68849039412964,337.755914759495,[],-7730.65371455598,-36578.08308865079,19477.23365068704,Propagated
```

**Important**: The first five columns (SatNo, timestamp, x, y, z) are required; all other columns are optional.

## JSON File Format Details

### Rockets.json

This file defines rocket models, their stages, and payloads:

```json
[
  {
    "id": 1,
    "name": "NFL1",
    "model": "China_Rocket",
    "parts": [
      {
        "name": "STAGE1",
        "sep": 86,
        "burns": [
          {
            "burn_id": 1,
            "burn_start": 0,
            "burn_duration": 86
          }
        ]
      },
      ...
    ],
    "payload": {
      "name": "PAYLOAD",
      "sep": 500
    }
  }
]
```

**Note**: The rocket definitions were previously included in the CSV file between `ROCKET_START` and `ROCKET_END` tags, but this approach is now deprecated. Rocket definitions should be provided in the separate Rockets.json file.

### Events.json

This file defines events such as collisions:

```json
{
  "collisions": [
    {
      "time": 3353.7765,
      "assetA": "NFL1_PAYLOAD",
      "assetB": "ASAT_Target_1",
      "position": [-8827.97771406857, 36020.13522553584, 20050.52313509695]
    }
  ]
}
```

### GroundStations.json

This file defines ground-based sensors with their locations and properties:

```json
{
  "radar_sensors": [
    {
      "name": "EGLIN_NE",
      "sensor": "EGLIN_AN_FPS-85",
      "location": {
        "latitude": "30:34:20n",
        "longitude": "86:12:52w",
        "altitude": 0.036
      },
      "heading": 0,
      "fixed": true,
      "azimuth_coverage": 120,
      "elevation_coverage": 85
    },
    ...
  ]
}
```

## Reading the Data

When reading these files, the visualizer should:

1. Parse the CSV file header to extract simulation parameters
2. Process all sensor definitions
3. Read the time series data, noting that only the first five columns are required
4. Parse the sensors column which contains JSON-embedded data
5. Load rocket definitions from Rockets.json (not from the CSV file)
6. Load event definitions from Events.json
7. Load ground station definitions from GroundStations.json

## Future Considerations

As the OmniCat system evolves, the visualizer interface may need to be updated to incorporate additional data types or modified data structures. Any changes to the interface should be backward compatible with existing visualizers where possible.
