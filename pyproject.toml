[tool.poetry]
name = "omnicat"
version = "0.0.1"
description = "catalogs tracks, objects, states, etc. in an effort to assist with SDA track and threat analysis"
authors = ["<PERSON> <<EMAIL>>"]
license = "LICENSE.txt"
readme = "README.md"
include = [
    "src/omnicat/http/templates/**",
    "src/omnicat/http/static/**"
]
packages = [
    { include = "omnicat", from = "src" }
]

[tool.poetry.dependencies]
python = "~3.12"
neo4j = "^5.27.0"
fastapi = {extras = ["all"], version = "^0.115.6"}
orbitalopsengine = {git = "**************************:space-superiority-sandbox/ngsx/engine/OrbitalOpsEngine.git", rev = "main"}
tenacity = "^9.0.0"
ratelimit = "^2.2.1"
requests = "^2.32.3"
sqlalchemy = "^2.0.36"
geoalchemy2 = "^0.16.0"
pandas = "^2.2.3"
psycopg2-binary = "^2.9.10"
jinja2 = "^3.1.5"
aiofiles = "^24.1.0"
pillow = "^11.1.0"
sgp4 = "^2.23"
batchprop = {git = "**************************:space-superiority-sandbox/ngsx/services/batchprop.git", rev = "main"}
python-dotenv = "^1.0.1"
playwright = "^1.51.0"
pytest-playwright = "^0.7.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.4"
pytest-cov = "^6.0.0"
fastapi-cli = "^0.0.5"
httpx = "^0.28.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"