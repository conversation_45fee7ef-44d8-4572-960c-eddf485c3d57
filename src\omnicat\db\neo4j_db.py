from omnicat.models import Object, Track, ObjectCorrelation, StateVector, TLE, TrackCorrelation
from omnicat.models.track_models import CorrelatedTracksFilter, TrackFilter, CorrelationStatus
from omnicat import Neo4jSettings
from neo4j import GraphDatabase, Record
import uuid
from datetime import datetime
from typing import Any

from omnicat.db.neo4j_utils import (_json_dumps_or_none, _json_loads_or_none, _create_track_from_record,
                                    _create_object_from_record, _prepare_track_params, _prepare_object_params,
                                    _prepare_correlation_params)
from omnicat.db.neo4j_operations import (_create_source_nodes, _create_object_type_nodes,
                                         _ensure_country_nodes_exist, _create_objects_with_relationships,
                                         _create_tracks_with_source_relationships, _create_object_correlations,
                                         _fetch_tracks_by_ids,
                                         _build_uncorrelated_tracks_query, _collect_correlated_track_ids,
                                         _collect_uncorrelated_track_ids)


class Neo4jDb(object):
    def __init__(self, uri: str, user: str, password: str):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        self._create_constraints()
        self._create_indices()

    def __del__(self):
        self.driver.close()

    def _create_constraints(self):
        with self.driver.session() as session:
            session.run(
                "CREATE CONSTRAINT IF NOT EXISTS FOR (o:Object) REQUIRE o.id IS UNIQUE")
            session.run(
                "CREATE CONSTRAINT IF NOT EXISTS FOR (t:Track) REQUIRE t.id IS UNIQUE")
            session.run(
                "CREATE CONSTRAINT IF NOT EXISTS FOR (ot:ObjectType) REQUIRE ot.name IS UNIQUE")
            session.run(
                "CREATE CONSTRAINT IF NOT EXISTS FOR (c:Country) REQUIRE c.code IS UNIQUE")
            session.run(
                "CREATE CONSTRAINT IF NOT EXISTS FOR (a:Allegiance) REQUIRE a.name IS UNIQUE")

    def _create_indices(self):
        with self.driver.session() as session:
            session.run("CREATE INDEX IF NOT EXISTS FOR (t:Track) ON (t.id)")
            session.run("CREATE INDEX IF NOT EXISTS FOR (o:Object) ON (o.id)")
            session.run(
                "CREATE INDEX IF NOT EXISTS FOR (o:Object) ON (o.type)")
            session.run(
                "CREATE INDEX IF NOT EXISTS FOR (s:Source) ON (s.name)")
            session.run(
                "CREATE INDEX IF NOT EXISTS FOR (ot:ObjectType) ON (ot.name)")
            session.run(
                "CREATE INDEX IF NOT EXISTS FOR (c:Country) ON (c.code)")
            session.run(
                "CREATE INDEX IF NOT EXISTS FOR (c:Country) ON (c.name)")
            session.run(
                "CREATE INDEX IF NOT EXISTS FOR (a:Allegiance) ON (a.name)")
            session.run(
                "CREATE INDEX IF NOT EXISTS FOR (a:Allegiance) ON (a.color)")

    def get_all_track_ids(self) -> list[str]:
        """
        Returns a list of all track IDs from the database.

        Returns:
            list[str]: A list of track IDs
        """
        with self.driver.session() as session:
            result = session.run("MATCH (t:Track) RETURN t.id")
            return [record["t.id"] for record in result]

    def get_non_current_track_ids(self) -> list[str]:
        """
        Returns a list of track IDs for all tracks correlated with each object
        that are not the most recently created track (based on track validity time)
        for the object.

        Returns:
            list[str]: A list of non-current track IDs
        """
        with self.driver.session() as session:
            # This query finds all tracks that are not the most recent track for their object
            # It works by:
            # 1. Matching tracks correlated with objects
            # 2. Finding the most recent track for each object (based on validity_time)
            # 3. Returning all track IDs except those of the most recent tracks
            result = session.run("""
                MATCH (track:Track)-[r:CORRELATED_WITH {type: 'object'}]->(object:Object)
                WITH object, track.id AS trackId, track.validity_time AS validityTime
                ORDER BY object.id, validityTime DESC
                WITH object.id AS objectId, collect(trackId) AS trackIds
                UNWIND trackIds[1..] AS nonCurrentTrackId
                RETURN nonCurrentTrackId
            """)
            return [record["nonCurrentTrackId"] for record in result]

    def add_object(self, obj: Object) -> str:
        """
        Add a single object to the database.

        Args:
            obj: The Object instance to add

        Returns:
            str: The generated object ID
        """
        # Reuse the batch function with a single-item list
        obj_ids = self.add_objects([obj])
        # Return the first (and only) ID
        return obj_ids[0]

    def add_objects(self, objects: list[Object]) -> list[str]:
        """
        Adds multiple objects to the database in a batch operation.
        If an object with the same data_uris and metadata already exists,
        no new object is created and the ID of the existing object is returned.

        If an object has a country_code:
        1. The country node is created if it doesn't exist
        2. The corresponding allegiance node is created if it doesn't exist
        3. A HAS_ALLEGIANCE relationship is created between the country and allegiance

        Args:
            objects: A list of Object instances to add

        Returns:
            list[str]: A list of object IDs for the added or existing objects
        """
        if len(objects) == 0:
            return []

        # Collect unique source names, object types, country codes
        source_names = set(obj.source for obj in objects)
        # Remove None values
        object_types = set(obj.type for obj in objects if obj.type is not None)
        # Always include "UNKNOWN" type for objects with no type
        object_types.add("UNKNOWN")
        country_codes = set(
            obj.country_code for obj in objects if obj.country_code is not None)

        # Prepare object parameters
        obj_params = _prepare_object_params(objects)

        with self.driver.session() as session:
            # Using a transaction to execute all operations in a single database connection
            with session.begin_transaction() as tx:
                # Create Source, ObjectType, and Country nodes
                _create_source_nodes(tx, source_names)
                _create_object_type_nodes(tx, object_types)
                _ensure_country_nodes_exist(tx, country_codes)

                # Create objects and establish relationships
                object_ids = _create_objects_with_relationships(tx, obj_params)

            return object_ids

    def get_objects(self, identifiers: list[str]) -> list[Object]:
        """
        Retrieve multiple objects by their IDs or International Designators.
        Each identifier is checked first as an ID, then as an International Designator.

        Args:
            identifiers: List of object IDs or international designators

        Returns:
            List of Object instances in the same order as the input identifiers.
            If an identifier doesn't exist, it will be skipped.
        """
        if not identifiers:
            return []

        with self.driver.session() as session:
            # Create a list of conditions for each identifier
            # Each identifier can match either by ID or by International Designator in metadata
            result = session.run(
                """
                UNWIND $identifiers as identifier
                MATCH (o:Object)
                WHERE o.id = identifier OR o.metadata CONTAINS ('"International Designator": "' + identifier + '"')
                OPTIONAL MATCH (o)-[:BELONGS_TO]->(c:Country)
                OPTIONAL MATCH (c)-[:HAS_ALLEGIANCE]->(a:Allegiance)
                OPTIONAL MATCH (o)-[:INGESTED_FROM]->(s:Source)
                OPTIONAL MATCH (o)-[:IS_A]->(t:ObjectType)
                RETURN 
                    identifier,
                    o.id as id, 
                    o.data_uris as data_uris, 
                    o.metadata as metadata,
                    o.common_name as common_name,
                    s.name as source,
                    t.name as type,
                    c.code as country_code,
                    a.color as allegiance
                """,
                identifiers=identifiers
            )

            # Create a mapping of identifier to Object
            objects_by_identifier = {}
            for record in result:
                identifier = record['identifier']
                obj = _create_object_from_record(record)
                objects_by_identifier[identifier] = obj

            # Return objects in the same order as input identifiers, skipping missing ones
            return [objects_by_identifier[identifier] for identifier in identifiers if identifier in objects_by_identifier]

    def get_object(self, identifier: str) -> Object | None:
        """
        Retrieve an object by its ID or International Designator.
        First tries to look up the object by ID, then by International Designator.

        Args:
            identifier: The object ID or international designator

        Returns:
            The Object if found, None otherwise
        """
        objects = self.get_objects([identifier])
        return objects[0] if objects else None

    def add_track(self, track: Track) -> str:
        """
        Add a single track to the database.

        Args:
            track: The Track instance to add

        Returns:
            str: The generated track ID
        """
        # Reuse the batch function with a single-item list
        track_ids = self.add_tracks([track])
        # Return the first (and only) ID
        return track_ids[0]

    def add_tracks(self, tracks: list[Track]) -> list[str]:
        """
        Adds multiple tracks to the database in a batch operation.
        If a track with the same data and source already exists,
        no new track is created and the ID of the existing track is returned.
        Object correlations are created as needed.

        Args:
            tracks: A list of Track instances to add

        Returns:
            list[str]: A list of track IDs for the added or existing tracks
        """
        if len(tracks) == 0:
            return []

        # Collect unique source names
        source_names = set(track.source for track in tracks)

        # Prepare track parameters
        track_params = [_prepare_track_params(track) for track in tracks]

        # Prepare object correlation parameters
        object_correlations = _prepare_correlation_params(track_params, tracks)

        with self.driver.session() as session:
            # Using a transaction to execute all operations in a single database connection
            with session.begin_transaction() as tx:
                # Create Source nodes
                _create_source_nodes(tx, source_names)

                # Create tracks and establish source relationships
                track_ids = _create_tracks_with_source_relationships(
                    tx, track_params)

                # Create object correlations
                _create_object_correlations(tx, object_correlations)

        return track_ids

    def correlate_track_with_object(self, track_id: str, object_correlation: ObjectCorrelation) -> None:
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (t:Track {id: $track_id})
                MATCH (o:Object {id: $object_id})
                MERGE 
                    (t)-[r:CORRELATED_WITH {
                        type: 'object',
                        validity_time: $validity_time,
                        object_id: $object_id,
                        confidence: $confidence
                    }]->(o)
                ON CREATE SET
                    r.creation_time = datetime(),
                    r.metadata = $metadata
                RETURN t.id, o.id
                """,
                track_id=track_id,
                object_id=object_correlation.object_id,
                validity_time=object_correlation.validity_time.isoformat(),
                confidence=object_correlation.confidence,
                metadata=_json_dumps_or_none(
                    object_correlation.metadata) if object_correlation.metadata else None
            )

            record = result.single()
            if not record:
                raise ValueError(
                    f"Track {track_id} or Object {object_correlation.object_id} not found")

    def get_tracks(self, track_ids: list[str]) -> list[Track]:
        """
        Retrieves multiple tracks and their correlations in a single query.

        Args:
            track_ids (list[str]): List of track IDs to retrieve

        Returns:
            list[Track]: List of Track objects, in the same order as the input track_ids.
                         If a track ID doesn't exist, it will be skipped.
        """
        if not track_ids:
            return []

        with self.driver.session() as session:
            result = session.run(
                """
                UNWIND $track_ids as track_id
                MATCH (t:Track {id: track_id})
                MATCH (t)-[:INGESTED_FROM]->(s:Source)
                RETURN 
                    t.id as id,
                    s.name as source,
                    t.format as format,
                    t.data as data,
                    t.metadata as metadata,
                    [(t)-[r:CORRELATED_WITH]->(o:Object) | {
                        object_id: o.id,
                        validity_time: r.validity_time,
                        confidence: r.confidence,
                        metadata: apoc.convert.fromJsonMap(r.metadata)
                    }] as object_correlations
                """,
                track_ids=track_ids
            )

            # Create a mapping of track_id to Track object
            tracks_by_id = {}
            for record in result:
                tracks_by_id[record['id']] = _create_track_from_record(record)

            # Return only tracks that were found, in the same order as track_ids
            return [tracks_by_id[track_id] for track_id in track_ids if track_id in tracks_by_id]

    def get_track(self, track_id: str) -> Track | None:
        """
        Retrieves a single track by ID.

        Args:
            track_id (str): The ID of the track to retrieve

        Returns:
            Track | None: The Track object if found, None otherwise
        """
        tracks = self.get_tracks([track_id])
        return tracks[0] if tracks else None

    def get_all_objects(self) -> list[Object]:
        """
        Returns a list of all objects in the database.

        Returns:
            list[Object]: A list of all objects
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (o:Object)
                OPTIONAL MATCH (o)-[:BELONGS_TO]->(c:Country)
                OPTIONAL MATCH (c)-[:HAS_ALLEGIANCE]->(a:Allegiance)
                OPTIONAL MATCH (o)-[:INGESTED_FROM]->(s:Source)
                OPTIONAL MATCH (o)-[:IS_A]->(t:ObjectType)
                RETURN 
                    o.id as id, 
                    o.data_uris as data_uris, 
                    o.metadata as metadata,
                    o.common_name as common_name,
                    s.name as source,
                    t.name as type,
                    c.code as country_code,
                    a.color as allegiance
                """
            )
            return [_create_object_from_record(record) for record in result]

    def get_all_object_ids(self) -> list[str]:
        """
        Retrieves all object IDs from the database.

        Returns:
            list[str]: List of all object IDs in the database
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (o:Object)
                RETURN o.id as id
                """
            )
            return [record["id"] for record in result]

    def get_tracks_for_object(self, object_id: str) -> list[Track]:
        """
        Retrieves all tracks correlated with a specific object.

        Args:
            object_id: The ID of the object to retrieve tracks for

        Returns:
            list[Track]: List of Track objects correlated with the specified object
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (t:Track)-[r:CORRELATED_WITH {type: 'object'}]->(o:Object {id: $object_id})
                RETURN t.id as track_id
                """,
                object_id=object_id
            )

            track_ids = [record["track_id"] for record in result]

            if not track_ids:
                return []

            return self.get_tracks(track_ids)

    def get_track_ids_for_object(self, object_id: str) -> list[str]:
        """
        Retrieves all track IDs correlated with a specific object.
        This is more efficient than get_tracks_for_object when only IDs are needed.

        Args:
            object_id: The ID of the object to retrieve track IDs for

        Returns:
            list[str]: List of track IDs correlated with the specified object
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (t:Track)-[r:CORRELATED_WITH {type: 'object'}]->(o:Object {id: $object_id})
                RETURN t.id as track_id
                """,
                object_id=object_id
            )

            return [record["track_id"] for record in result]

    def get_filtered_tracks(self, filter_params: TrackFilter) -> tuple[list[str], list[Track]]:
        """
        Retrieves tracks based on filter criteria in the TrackFilter model.
        Uses a single transaction for better efficiency.

        Args:
            filter_params: A TrackFilter instance with filter criteria

        Returns:
            tuple[list[str], list[Track]]: Tuple containing (list of track IDs, list of Track objects) matching the criteria
        """
        # Determine which types of tracks to retrieve based on correlation status
        get_correlated, get_uncorrelated = filter_params.determine_correlation_status()

        # Collect tracks in a single transaction
        with self.driver.session() as session:
            with session.begin_transaction() as tx:
                track_ids = []

                # Get correlated tracks if requested
                if get_correlated:
                    track_ids.extend(
                        _collect_correlated_track_ids(tx, filter_params))

                # Get uncorrelated tracks if requested
                if get_uncorrelated:
                    track_ids.extend(_collect_uncorrelated_track_ids(
                        tx, filter_params.sources))

                # Remove duplicates
                track_ids = list(set(track_ids))

                # If no tracks found, return empty results
                if not track_ids:
                    return ([], [])

                # Fetch complete track data in the same transaction
                track_records = _fetch_tracks_by_ids(tx, track_ids)

                # Create tracks and ensure they're in the same order as track_ids
                # Database results may not preserve the input order, so we create a mapping
                tracks_by_id = {}
                for record in track_records:
                    track = _create_track_from_record(record)
                    tracks_by_id[record['id']] = track

                # Build tracks list in the same order as track_ids
                tracks = [tracks_by_id[track_id]
                          for track_id in track_ids if track_id in tracks_by_id]

                # Update track_ids to only include tracks that were actually found
                found_track_ids = [
                    track_id for track_id in track_ids if track_id in tracks_by_id]

                return (found_track_ids, tracks)

    def get_track_ids_by_filter(self, filter_params: TrackFilter) -> list[str]:
        """
        Retrieves track IDs based on filter criteria in the TrackFilter model.
        This is more efficient than get_filtered_tracks when only IDs are needed.

        Args:
            filter_params: A TrackFilter instance with filter criteria

        Returns:
            list[str]: List of track IDs matching the criteria
        """
        # Get track IDs using the efficient get_filtered_tracks implementation
        track_ids, _ = self.get_filtered_tracks(filter_params)
        return track_ids

    def get_track_filter_options(self) -> dict[str, list[str]]:
        """
        Returns available filter options for tracks based on what exists in the database.

        Returns:
            dict[str, list[str]]: Dictionary containing lists of:
                - "sources": Available source names
                - "object_types": Available object type names
                - "country_codes": Available country codes
                - "allegiances": Available allegiance colors
        """
        with self.driver.session() as session:
            with session.begin_transaction() as tx:
                # Get all available sources
                sources_result = tx.run(
                    "MATCH (s:Source) RETURN s.name AS name ORDER BY name")
                sources = [record["name"] for record in sources_result]

                # Get all available object types
                object_types_result = tx.run(
                    "MATCH (ot:ObjectType) RETURN ot.name AS name ORDER BY name")
                object_types = [record["name"]
                                for record in object_types_result]

                # Get all available country codes
                country_codes_result = tx.run(
                    "MATCH (c:Country) RETURN c.code AS code ORDER BY code")
                country_codes = [record["code"]
                                 for record in country_codes_result]

                # Get all available allegiances by color
                allegiances_result = tx.run(
                    "MATCH (a:Allegiance) RETURN a.color AS color ORDER BY color")
                allegiances = [record["color"]
                               for record in allegiances_result]

                return {
                    "sources": sources,
                    "object_types": object_types,
                    "country_codes": country_codes,
                    "allegiances": allegiances
                }
