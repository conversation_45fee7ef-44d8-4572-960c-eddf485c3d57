from typing import Any
from neo4j import Record
from omnicat.models import Track, Object
from omnicat.models.track_models import Correlated<PERSON><PERSON>s<PERSON><PERSON>er, TrackFilter, CorrelationStatus
import uuid
from datetime import datetime
from omnicat.db.neo4j_utils import (_json_dumps_or_none, _prepare_correlation_params,
                                    _prepare_track_params, _prepare_object_params)
from omnicat.db.neo4j_constants import (COUNTRY_CODES, ALLEGIANCE_DEFINITIONS,
                                        COUNTRY_ALLEGIANCE_MAPPINGS)


def _create_source_nodes(tx: Any, source_names: set[str]) -> None:
    """
    Create Source nodes for all unique sources in a single operation.

    Args:
        tx: Neo4j transaction
        source_names: Set of source names to create nodes for
    """
    tx.run(
        """
        UNWIND $sources AS source_name
        MERGE (s:Source {name: source_name})
        ON CREATE SET s.creation_time = datetime()
        """,
        sources=list(source_names)
    )


def _create_object_type_nodes(tx: Any, object_types: set[str]) -> None:
    """
    Create ObjectType nodes for all unique object types in a single operation.

    Args:
        tx: Neo4j transaction
        object_types: Set of object type names to create nodes for
    """
    tx.run(
        """
        UNWIND $types AS type_name
        MERGE (ot:ObjectType {name: type_name})
        ON CREATE SET ot.creation_time = datetime()
        """,
        types=list(object_types)
    )


def _ensure_country_nodes_exist(tx: Any, country_codes: set[str]) -> None:
    """
    Ensure Country nodes exist for all country codes, creating any missing ones.
    Also creates associated Allegiance nodes and HAS_ALLEGIANCE relationships.

    Args:
        tx: Neo4j transaction
        country_codes: Set of country codes to ensure exist
    """
    if not country_codes:
        return

    # Create country nodes with proper names from COUNTRY_CODES
    country_data = []
    for code in country_codes:
        # Find country data by code
        country_info = next((c for c in COUNTRY_CODES if c["code"] == code), {
                            "code": code, "name": "UNKNOWN"})
        country_data.append(country_info)

    # Create country nodes
    tx.run(
        """
        UNWIND $countries AS country
        MERGE (c:Country {code: country.code})
        ON CREATE SET
            c.name = country.name,
            c.creation_time = datetime()
        """,
        countries=country_data
    )

    # Get allegiances needed for these countries
    allegiance_names = set()
    country_allegiance_pairs = []

    for code in country_codes:
        # Find the allegiance for this country
        allegiance_name = COUNTRY_ALLEGIANCE_MAPPINGS.get(code, "Unknown")
        allegiance_names.add(allegiance_name)
        country_allegiance_pairs.append(
            {"country_code": code, "allegiance_name": allegiance_name})

    # Create allegiance nodes
    if allegiance_names:
        allegiance_data = [
            a for a in ALLEGIANCE_DEFINITIONS if a["name"] in allegiance_names]
        tx.run(
            """
            UNWIND $allegiances AS allegiance
            MERGE (a:Allegiance {name: allegiance.name})
            ON CREATE SET
                a.color = allegiance.color,
                a.description = allegiance.description,
                a.creation_time = datetime()
            """,
            allegiances=allegiance_data
        )

    # Create HAS_ALLEGIANCE relationships
    if country_allegiance_pairs:
        tx.run(
            """
            UNWIND $mappings AS mapping
            MATCH (c:Country {code: mapping.country_code})
            MATCH (a:Allegiance {name: mapping.allegiance_name})
            MERGE (c)-[:HAS_ALLEGIANCE]->(a)
            """,
            mappings=country_allegiance_pairs
        )


def _create_objects_with_relationships(tx: Any, obj_params: list[dict[str, Any]]) -> list[str]:
    """
    Create Object nodes and establish all relationships in a single operation.

    Args:
        tx: Neo4j transaction
        obj_params: List of object parameter dictionaries

    Returns:
        list: Object IDs of created or existing objects
    """
    # First create the objects themselves
    create_result = tx.run(
        """
        UNWIND $objects AS object
        MERGE (o:Object {data_uris: object.data_uris, metadata: object.metadata})
        ON CREATE SET
            o.id = object.id,
            o.common_name = object.common_name,
            o.name = object.common_name,
            o.creation_time = datetime()
        RETURN o.id as id
        """,
        objects=obj_params
    )

    # Collect the created object IDs
    object_ids = [record["id"] for record in create_result]

    # Now create source relationships - verify they are all created successfully
    # Use the actual returned object IDs, not the original parameter IDs
    source_rel_result = tx.run(
        """
        UNWIND $object_data AS item
        MATCH (o:Object {id: item.actual_id})
        MATCH (s:Source {name: item.source})
        MERGE (o)-[:INGESTED_FROM]->(s)
        RETURN item.actual_id as object_id, item.source as source_name
        """,
        object_data=[{
            "actual_id": actual_id,
            "source": obj_param["source"]
        } for actual_id, obj_param in zip(object_ids, obj_params)]
    )

    # Verify that all objects got their source relationships
    source_rels_created = list(source_rel_result)
    if len(source_rels_created) != len(obj_params):
        created_ids = {rel["object_id"] for rel in source_rels_created}
        expected_ids = set(object_ids)
        missing_ids = expected_ids - created_ids
        raise RuntimeError(f"Failed to create INGESTED_FROM relationships for objects: {missing_ids}. "
                           f"This indicates source nodes were not created properly.")

    # Create object type relationships for objects with non-null types
    tx.run(
        """
        UNWIND $objects AS object
        MATCH (o:Object {id: object.id})
        WHERE object.type IS NOT NULL
        MATCH (t:ObjectType {name: object.type})
        MERGE (o)-[:IS_A]->(t)
        """,
        objects=obj_params
    )

    # Create IS_A relationships to UNKNOWN for objects with null type
    tx.run("""
        MATCH (o:Object)
        WHERE o.id IN $object_ids
        AND NOT EXISTS((o)-[:IS_A]->())
        MATCH (ot:ObjectType {name: 'UNKNOWN'})
        MERGE (o)-[:IS_A]->(ot)
    """, object_ids=object_ids)

    # Make sure the UNK country exists for objects with no country code
    _ensure_country_nodes_exist(tx, {"UNK"})

    # Create country relationships for all objects
    # Objects with country_code=None will get the UNK country
    for obj in obj_params:
        if obj.get("country_code") is None:
            obj["country_code"] = "UNK"

    tx.run(
        """
        UNWIND $objects AS object
        MATCH (o:Object {id: object.id})
        MATCH (c:Country {code: object.country_code})
        MERGE (o)-[:BELONGS_TO]->(c)
        """,
        objects=obj_params
    )

    return object_ids


def _create_tracks_with_source_relationships(tx: Any, track_params: list[dict[str, Any]]) -> list[str]:
    """
    Create Track nodes and establish Source relationships in a single operation.

    Args:
        tx: Neo4j transaction
        track_params: List of track parameter dictionaries

    Returns:
        list: Track IDs of created or existing tracks
    """
    result = tx.run(
        """
        // First match or create source nodes
        UNWIND $tracks AS track
        MATCH (s:Source {name: track.source})

        // Then find or create track nodes with source relationships to maintain uniqueness
        MERGE (t:Track {data: track.data})-[:INGESTED_FROM]->(s)
        ON CREATE SET
            t.id = track.id,
            t.validity_time = track.validity_time,
            t.format = track.format,
            t.name = track.format,
            t.creation_time = datetime(),
            t.metadata = track.metadata

        RETURN t.id as id, track.data as data
        """,
        tracks=track_params
    )

    # Collect track IDs
    return [record["id"] for record in result]


def _create_object_correlations(tx: Any, object_correlations: list[dict[str, Any]]) -> None:
    """
    Create object correlations for tracks in a single operation.

    Args:
        tx: Neo4j transaction
        object_correlations: List of correlation parameter dictionaries
    """
    if object_correlations:
        tx.run(
            """
            UNWIND $correlations AS corr
            MATCH (t:Track {data: corr.track_data})-[:INGESTED_FROM]->(s:Source {name: corr.track_source})
            MATCH (o:Object {id: corr.object_id})
            MERGE
                (t)-[r:CORRELATED_WITH {
                    type: 'object',
                    object_id: corr.object_id,
                    validity_time: corr.validity_time,
                    confidence: corr.confidence
                }]->(o)
            ON CREATE SET
                r.creation_time = datetime(),
                r.metadata = corr.metadata
            """,
            correlations=object_correlations
        )


def _fetch_tracks_by_ids(tx: Any, track_ids: list[str]) -> list[Record]:
    """
    Fetches complete track data given a list of track IDs.

    Args:
        tx: Neo4j transaction
        track_ids: List of track IDs to fetch

    Returns:
        list: List of Neo4j records containing track data
    """
    tracks_query = """
    MATCH (t:Track)
    WHERE t.id IN $track_ids
    // Get source from relationship instead of property
    MATCH (t)-[:INGESTED_FROM]->(s:Source)
    RETURN
        t.id as id,
        s.name as source,
        t.format as format,
        t.data as data,
        t.metadata as metadata,
        [(t)-[r:CORRELATED_WITH]->(o:Object) | {
            object_id: o.id,
            validity_time: r.validity_time,
            confidence: r.confidence,
            metadata: apoc.convert.fromJsonMap(r.metadata)
        }] as object_correlations
    """

    result = tx.run(tracks_query, track_ids=track_ids)
    return list(result)


def _build_unified_correlated_filter_query(
    country_codes: list[str] = None,
    object_types: list[str] = None,
    allegiances: list[str] = None,
    sources: list[str] = None,
    skip: int = None,
    limit: int = None,
    count_only: bool = False
) -> tuple[str, dict]:
    """
    Build a unified query that combines all filter types with proper AND/OR logic.

    Logic:
    - Between different filter types: AND (intersection)
    - Within each filter type: OR (union)

    Args:
        country_codes: Optional list of country codes to filter by
        object_types: Optional list of object types to filter by
        allegiances: Optional list of allegiance names or colors to filter by
        sources: Optional list of source names to filter by
        skip: Optional number of records to skip for pagination
        limit: Optional maximum number of records to return for pagination
        count_only: If True, return count instead of track IDs

    Returns:
        tuple: (query_string, parameters)
    """
    query = "MATCH (t:Track)-[:CORRELATED_WITH {type: 'object'}]->(o:Object)\n"
    where_clauses = []
    params = {}

    # Add country filtering if specified
    if country_codes:
        query += "MATCH (o)-[:BELONGS_TO]->(c:Country)\n"
        where_clauses.append("c.code IN $country_codes")
        params["country_codes"] = country_codes

    # Add object type filtering if specified
    if object_types:
        query += "MATCH (o)-[:IS_A]->(ot:ObjectType)\n"
        where_clauses.append("ot.name IN $object_types")
        params["object_types"] = object_types

    # Add allegiance filtering if specified
    if allegiances:
        # Ensure we have country relationship (may be duplicate MATCH but that's OK)
        if not country_codes:
            query += "MATCH (o)-[:BELONGS_TO]->(c:Country)\n"
        query += "MATCH (c)-[:HAS_ALLEGIANCE]->(a:Allegiance)\n"
        where_clauses.append(
            "(a.name IN $allegiances OR a.color IN $allegiances)")
        params["allegiances"] = allegiances

    # Add source filtering if specified
    if sources:
        query += "MATCH (t)-[:INGESTED_FROM]->(s:Source)\n"
        where_clauses.append("s.name IN $sources")
        params["sources"] = sources

    # Add WHERE clause if we have any conditions
    if where_clauses:
        query += f"WHERE {' AND '.join(where_clauses)}\n"

    # Return count or track IDs
    if count_only:
        query += "RETURN COUNT(DISTINCT t.id) as count"
    else:
        query += "RETURN DISTINCT t.id as track_id"

        # Add pagination if specified
        if skip is not None:
            query += f"\nSKIP {skip}"

        if limit is not None:
            query += f"\nLIMIT {limit}"

    return query, params


def _build_uncorrelated_tracks_query(
    sources: list[str] = None,
    skip: int = None,
    limit: int = None,
    count_only: bool = False
) -> tuple[str, dict]:
    """
    Build a query for tracks that have no correlations.

    Args:
        sources: Optional list of source names to filter by
        skip: Optional number of records to skip for pagination
        limit: Optional maximum number of records to return for pagination
        count_only: If True, return count instead of track IDs

    Returns:
        tuple: (query_string, parameters)
    """
    query = """
    MATCH (t:Track)
    WHERE NOT EXISTS((t)-[:CORRELATED_WITH]->())
    """

    params = {}

    # Add source filter if provided
    if sources:
        query += """
        MATCH (t)-[:INGESTED_FROM]->(s:Source)
        WHERE s.name IN $sources
        """
        params["sources"] = sources

    # Return count or track IDs
    if count_only:
        query += " RETURN COUNT(t.id) as count"
    else:
        query += " RETURN t.id as track_id"

        # Add pagination if specified
        if skip is not None:
            query += f"\nSKIP {skip}"

        if limit is not None:
            query += f"\nLIMIT {limit}"

    return query, params


def _collect_correlated_track_ids(
    tx: Any,
    filter_params: TrackFilter,
    skip: int = None,
    limit: int = None,
    count_only: bool = False
) -> list[str] | int:
    """
    Collect track IDs for correlated tracks based on filter criteria.

    Uses unified filtering logic where:
    - All specified filter fields must be satisfied (AND logic between fields)
    - Within each filter field, any matching value is accepted (OR logic within fields)

    Args:
        tx: Neo4j transaction
        filter_params: A TrackFilter instance with filter criteria
        skip: Optional number of records to skip for pagination
        limit: Optional maximum number of records to return for pagination
        count_only: If True, return count instead of track IDs

    Returns:
        list[str] | int: List of track IDs for correlated tracks matching the criteria, or count if count_only=True
    """
    if not filter_params.correlated_tracks_filter:
        # No correlated filter specified, but we might have sources filter
        if filter_params.sources:
            query, params = _build_unified_correlated_filter_query(
                sources=filter_params.sources, skip=skip, limit=limit, count_only=count_only)
            result = tx.run(query, **params)
            if count_only:
                return result.single()["count"]
            return [record["track_id"] for record in result]
        else:
            return 0 if count_only else []

    correlated_filter = filter_params.correlated_tracks_filter

    # Check if any filters are specified
    has_filters = (correlated_filter.country_codes or
                   correlated_filter.object_types or
                   correlated_filter.allegiances or
                   filter_params.sources)

    if not has_filters:
        # No specific filters, return all correlated tracks
        query, params = _build_unified_correlated_filter_query(
            skip=skip, limit=limit, count_only=count_only)
        result = tx.run(query, **params)
        if count_only:
            return result.single()["count"]
        return [record["track_id"] for record in result]
    else:
        # Build unified query with all specified filters
        query, params = _build_unified_correlated_filter_query(
            country_codes=correlated_filter.country_codes,
            object_types=correlated_filter.object_types,
            allegiances=correlated_filter.allegiances,
            sources=filter_params.sources,
            skip=skip,
            limit=limit,
            count_only=count_only
        )
        result = tx.run(query, **params)
        if count_only:
            return result.single()["count"]
        return [record["track_id"] for record in result]


def _collect_uncorrelated_track_ids(
    tx: Any,
    sources: list[str] = None,
    skip: int = None,
    limit: int = None,
    count_only: bool = False
) -> list[str] | int:
    """
    Collect track IDs for uncorrelated tracks.

    Args:
        tx: Neo4j transaction
        sources: Optional list of source names to filter by
        skip: Optional number of records to skip for pagination
        limit: Optional maximum number of records to return for pagination
        count_only: If True, return count instead of track IDs

    Returns:
        list[str] | int: List of track IDs for uncorrelated tracks, or count if count_only=True
    """
    # Build and execute uncorrelated tracks query
    uncorrelated_query, uncorrelated_params = _build_uncorrelated_tracks_query(
        sources, skip=skip, limit=limit, count_only=count_only)
    result = tx.run(uncorrelated_query, **uncorrelated_params)
    if count_only:
        return result.single()["count"]
    return [record["track_id"] for record in result]
