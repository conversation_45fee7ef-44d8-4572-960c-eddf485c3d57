import json
import uuid
from neo4j import Record
from omnicat.models import Object, Track, ObjectCorrelation, StateVector, TLE
from datetime import datetime
from typing import Any


def _json_dumps_or_none(data: dict | None) -> str | None:
    """Convert dictionary to JSON string or return None if data is None."""
    return json.dumps(data) if data is not None else None


def _json_loads_or_none(json_str: str | None) -> dict | None:
    """Convert JSON string to dictionary or return None if json_str is None."""
    return json.loads(json_str) if json_str is not None else None


def _create_track_from_record(record: Record) -> Track:
    """
    Creates a Track object from a Neo4j database record.

    Args:
        record: A Neo4j record containing track data

    Returns:
        Track: A fully populated Track object
    """
    # Parse the track data based on its format
    if record['format'] == "state_vector":
        track_data = StateVector.model_validate_json(record['data'])
    else:  # format == "tle"
        track_data = TLE.model_validate_json(record['data'])

    track_metadata = _json_loads_or_none(record['metadata'])

    # Create object correlation objects
    object_corrs = [
        ObjectCorrelation(
            object_id=corr['object_id'],
            validity_time=datetime.fromisoformat(corr['validity_time']),
            confidence=corr['confidence'],
            metadata=corr['metadata']
        ) for corr in record['object_correlations']
    ]

    # Create Track object
    return Track(
        source=record['source'],
        format=record['format'],
        data=track_data,
        metadata=track_metadata,
        object_correlations=object_corrs if object_corrs else None
    )


def _create_object_from_record(record: Record) -> Object:
    """
    Creates an Object from a Neo4j database record.

    Args:
        record: A Neo4j record containing object data

    Returns:
        Object: A fully populated Object instance

    Raises:
        ValueError: If the record has no source (indicating a database integrity issue)
    """
    obj_id = record["id"]

    # Get properties from relationships when available
    source = record.get("source")
    if source is None:
        raise ValueError(
            f"Object {obj_id} has no source relationship. This indicates a database integrity issue.")

    object_type = record.get("type", "UNKNOWN")
    country_code = record.get("country_code")
    common_name = record.get("common_name")
    allegiance = record.get("allegiance")

    data_uris_json = record["data_uris"]
    metadata_json = record["metadata"]

    data_uris = _json_loads_or_none(data_uris_json)
    metadata = _json_loads_or_none(metadata_json)

    return Object(
        internal_id=obj_id,
        source=source,
        type=object_type,
        data_uris=data_uris,
        metadata=metadata,
        country_code=country_code,
        common_name=common_name,
        allegiance=allegiance
    )


def _prepare_track_params(track: Track) -> dict[str, Any]:
    """
    Prepare a dictionary of track parameters for Neo4j queries.

    Args:
        track: Track object to convert to parameters

    Returns:
        dict: Dictionary with Neo4j parameters
    """
    return {
        'id': f"TRK-{uuid.uuid4()}",
        'validity_time': track.data.validity_time.isoformat(),
        'source': track.source,
        'format': track.format,
        'data': track.data.model_dump_json(),
        'metadata': _json_dumps_or_none(track.metadata)
    }


def _prepare_object_params(objects: list[Object]) -> list[dict[str, Any]]:
    """
    Prepare object parameters for Neo4j queries.

    Args:
        objects: List of Object instances to prepare parameters for

    Returns:
        list[dict]: List of dictionaries with Neo4j parameters
    """
    return [{
        'id': f"OBJ-{uuid.uuid4()}",
        'source': obj.source,
        'type': obj.type,
        'data_uris': _json_dumps_or_none(obj.data_uris),
        'metadata': _json_dumps_or_none(obj.metadata),
        'country_code': obj.country_code if obj.country_code is not None else 'UNK',
        'common_name': obj.common_name
    } for obj in objects]


def _prepare_correlation_params(track_params: list[dict[str, Any]], tracks: list[Track]) -> list[dict[str, Any]]:
    """
    Prepare object correlation parameters for tracks.

    Args:
        track_params: List of track parameter dictionaries
        tracks: List of Track objects

    Returns:
        list: List of correlation parameter dictionaries
    """
    object_correlations = []
    for track_idx, track in enumerate(tracks):
        if track.object_correlations:
            for correlation in track.object_correlations:
                object_correlations.append({
                    'track_id': track_params[track_idx]['id'],
                    'track_data': track_params[track_idx]['data'],
                    'track_source': track.source,
                    'object_id': correlation.object_id,
                    'validity_time': correlation.validity_time.isoformat(),
                    'confidence': correlation.confidence,
                    'metadata': _json_dumps_or_none(correlation.metadata)
                })
    return object_correlations
