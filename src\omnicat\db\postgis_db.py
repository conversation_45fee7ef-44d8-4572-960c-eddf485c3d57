from sqlalchemy import create_engine, text
from geoalchemy2 import Geometry
from omnicat.models import AreaOfInterest
from datetime import datetime, timezone
from typing import List, Tuple, Dict, Any, Optional
from omnicat.utils import ensure_utc
import numpy as np
from sqlalchemy.dialects.postgresql import insert
import time
import io
import csv
import statistics
from contextlib import contextmanager
from omnicat.settings import PostgisSettings


class PostGisDb(object):
    def __init__(self, db_url, prop_table_name: str = "orbital_tracks", batch_size: int = 10000):
        self.engine = create_engine(db_url)
        self.prop_table_name = prop_table_name
        self.create_propagation_table(prop_table_name)
        self.batch_size = batch_size  # Batch size from settings

    @staticmethod
    def from_settings(settings: PostgisSettings) -> "PostGisDb":
        """
        Create a PostGisDb instance from PostgisSettings.

        Args:
            settings: PostgisSettings containing database configuration

        Returns:
            Configured PostGisDb instance
        """
        return PostGisDb(
            db_url=settings.uri,
            prop_table_name=settings.prop_table_name,
            batch_size=settings.batch_size
        )

    def create_propagation_table(self, table_name: str):
        create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            id SERIAL PRIMARY KEY,
            track_id VARCHAR(40),
            segment geometry(LINESTRINGZ, 0),
            start_time TIMESTAMP,
            end_time TIMESTAMP,
            UNIQUE(track_id, start_time, end_time)
        );
        """

        # Create indexes to improve query performance
        create_indexes_sql = f"""
        -- Index for track_id lookups
        CREATE INDEX IF NOT EXISTS idx_{table_name}_track_id ON {table_name} (track_id);
        
        -- Index for time range queries
        CREATE INDEX IF NOT EXISTS idx_{table_name}_time_range ON {table_name} (start_time, end_time);
        
        -- Spatial index for geometry operations
        CREATE INDEX IF NOT EXISTS idx_{table_name}_segment ON {table_name} USING GIST (segment);
        """

        with self.engine.connect() as conn:
            conn.execute(text(create_table_sql))
            conn.execute(text(create_indexes_sql))
            conn.commit()

    def get_propagated_data_for_tracks(self, track_ids: List[str], time_start: datetime, time_end: datetime) -> List[Dict[str, Any]]:
        """
        Retrieves propagated track data for the specified track IDs within the given time window.

        Args:
            track_ids: List of track IDs to retrieve data for
            time_start: Start time of the time window
            time_end: End time of the time window

        Returns:
            List of dictionaries containing timestamped ECEF points with track IDs
        """
        if not track_ids:
            return []

        # Prepare for SQL query
        time_start = ensure_utc(time_start)
        time_end = ensure_utc(time_end)

        # Format track_ids for SQL IN clause
        placeholders = [f":track_id_{i}" for i in range(len(track_ids))]
        track_id_params = {f"track_id_{i}": track_id for i,
                           track_id in enumerate(track_ids)}

        # Additional parameters
        params = {
            **track_id_params,
            "time_start": time_start,
            "time_end": time_end
        }

        # SQL query to extract data points from LineString segments
        sql = f"""
        WITH segments_in_window AS (
            SELECT 
                id,
                track_id,
                segment,
                start_time,
                end_time
            FROM {self.prop_table_name}
            WHERE track_id IN ({', '.join(placeholders)})
            AND (start_time, end_time) OVERLAPS (:time_start, :time_end)
        ),
        segment_points AS (
            -- Extract start and end points of each segment with their times
            SELECT
                track_id,
                ST_X(ST_StartPoint(segment)) as x,
                ST_Y(ST_StartPoint(segment)) as y,
                ST_Z(ST_StartPoint(segment)) as z,
                start_time as time
            FROM segments_in_window
            
            UNION ALL
            
            SELECT
                track_id,
                ST_X(ST_EndPoint(segment)) as x,
                ST_Y(ST_EndPoint(segment)) as y,
                ST_Z(ST_EndPoint(segment)) as z,
                end_time as time
            FROM segments_in_window
        )
        SELECT
            track_id,
            x, y, z,
            time
        FROM segment_points
        WHERE time BETWEEN :time_start AND :time_end
        ORDER BY track_id, time
        """

        with self.engine.connect() as conn:
            result = conn.execute(text(sql), params)

            # Process the raw data from the database
            points_data = []

            for row in result:
                track_id = row[0]
                x, y, z = float(row[1]), float(row[2]), float(row[3])
                timestamp = row[4]

                # Ensure timestamp is in UTC
                timestamp = ensure_utc(timestamp)

                # Return only the raw ECEF points with timestamps and track IDs
                point_data = {
                    "track_id": track_id,
                    "x": x,
                    "y": y,
                    "z": z,
                    "time": timestamp
                }

                points_data.append(point_data)

            return points_data

    def insert_propagated_points(self, track_id: str, points: List[Tuple[float, float, float, datetime]]) -> None:
        if len(points) < 2:
            return

        with self.engine.connect() as conn:
            for i in range(len(points) - 1):
                x1, y1, z1, t1 = points[i]
                x2, y2, z2, t2 = points[i + 1]

                # Ensure UTC
                t1 = ensure_utc(t1)
                t2 = ensure_utc(t2)

                # Create a LineString from two consecutive points
                linestring_wkt = f"ST_SetSRID(ST_MakeLine(ST_MakePoint({x1}, {y1}, {z1}), ST_MakePoint({x2}, {y2}, {z2})), 0)"
                sql = f"""
                INSERT INTO {self.prop_table_name} (track_id, segment, start_time, end_time)
                VALUES ('{track_id}', {linestring_wkt}, '{t1}', '{t2}')
                ON CONFLICT (track_id, start_time, end_time) DO NOTHING;
                """
                conn.execute(text(sql))
            conn.commit()

    def insert_propagated_batch(self, batch, track_ids: List[str]) -> None:
        """
        Efficiently inserts a PropagatedTleBatch object into the database.

        Args:
            batch: A PropagatedTleBatch object containing propagated TLE data
            track_ids: List of track identifiers to correlate with each TLE in the batch

        The function works with the PropagatedTleBatch structure:
        - data: numpy array of shape (n_tles, n_times, 3) containing position vectors
        - tles: List of TLE objects corresponding to the first dimension
        - times: List of datetime objects corresponding to the second dimension

        Each track_id in the track_ids list corresponds to a TLE in the batch.tles list.
        """
        if not hasattr(batch, 'data') or batch.data.size == 0:
            return

        if not hasattr(batch, 'times') or len(batch.times) < 2:
            return

        if not track_ids:
            return

        records = self._prepare_batch_records(batch, track_ids)
        if not records:
            return

        # Use optimized bulk insertion method
        self._bulk_insert_records(records)

    def _prepare_batch_records(self, batch, track_ids: List[str]) -> List[Dict[str, Any]]:
        """
        Prepare records for batch insertion.

        Returns:
            List of dictionaries with data for each segment
        """
        records = []
        start_time = time.time()

        # Iterate through each TLE and its corresponding track_id
        for tle_idx, track_id in enumerate(track_ids):
            # Skip if we're out of data
            if tle_idx >= len(batch.data):
                continue

            # Get position data for this TLE
            positions = batch.data[tle_idx]

            # Skip TLEs with insufficient position data
            if len(positions) < 2:
                continue

            # Process consecutive points into line segments
            for i in range(len(batch.times) - 1):
                # Skip if we're beyond the available positions
                if i + 1 >= len(positions):
                    continue

                x1, y1, z1 = positions[i]
                x2, y2, z2 = positions[i + 1]
                t1 = ensure_utc(batch.times[i])
                t2 = ensure_utc(batch.times[i + 1])

                # Create record with raw data - more efficient than building SQL strings
                records.append({
                    'track_id': str(track_id),
                    'x1': float(x1), 'y1': float(y1), 'z1': float(z1),
                    'x2': float(x2), 'y2': float(y2), 'z2': float(z2),
                    't1': t1, 't2': t2
                })

        return records

    def _bulk_insert_records(self, records: List[Dict[str, Any]]) -> None:
        """
        High-performance bulk insertion method using temporary tables and COPY.
        This method is significantly faster than individual SQL inserts by:
        1. Creating a temporary table
        2. Using PostgreSQL's COPY command for fast data loading
        3. Transforming the data into spatial objects in a single SQL operation
        4. Handling the operation in a single transaction
        """
        if not records:
            return

        # Create a unique temporary table name
        temp_table = f"temp_{self.prop_table_name}_{int(time.time())}"

        try:
            with self.engine.connect() as conn:
                # Create temp table for staging the data
                conn.execute(text(f"""
                CREATE TEMP TABLE {temp_table} (
                    track_id VARCHAR(40),
                    x1 FLOAT, y1 FLOAT, z1 FLOAT,
                    x2 FLOAT, y2 FLOAT, z2 FLOAT, 
                    t1 TIMESTAMP WITH TIME ZONE, 
                    t2 TIMESTAMP WITH TIME ZONE
                )"""))

                # Prepare data for COPY
                csv_data = io.StringIO()
                for r in records:
                    row = [
                        r['track_id'],
                        r['x1'], r['y1'], r['z1'],
                        r['x2'], r['y2'], r['z2'],
                        r['t1'].isoformat(), r['t2'].isoformat()
                    ]
                    csv_data.write('\t'.join(str(v) for v in row) + '\n')

                csv_data.seek(0)

                # Use raw connection to perform COPY
                raw_conn = conn.connection.driver_connection
                with raw_conn.cursor() as cursor:
                    cursor.copy_expert(
                        f"COPY {temp_table} FROM STDIN", csv_data)

                # Insert from temp table with spatial transformations
                sql = f"""
                INSERT INTO {self.prop_table_name} (track_id, segment, start_time, end_time)
                SELECT 
                    track_id, 
                    ST_SetSRID(ST_MakeLine(ST_MakePoint(x1, y1, z1), ST_MakePoint(x2, y2, z2)), 0),
                    t1, t2
                FROM {temp_table}
                ON CONFLICT (track_id, start_time, end_time) DO NOTHING
                """
                conn.execute(text(sql))

                # Clean up the temporary table
                conn.execute(text(f"DROP TABLE {temp_table}"))
                conn.commit()
        except Exception:
            # Simply re-raise without logging
            raise

    def query_aoi(self, aoi: AreaOfInterest, track_ids: list[str] = None) -> list[str]:
        """
        Query for tracks that pass through an area of interest.

        Uses a two-step filtering approach for better performance with large datasets:
        1. First filter by time range (fast, uses time index)
        2. Then apply spatial filter only to time-filtered candidates (reduces expensive spatial operations)

        Args:
            aoi: Area of interest with center, radius, and time window
            track_ids: Optional list of track IDs to limit the search (default None)

        Returns:
            List of track IDs that pass through the AOI during the time window
        """
        track_ids = track_ids or []

        with self.engine.connect() as conn:
            x, y, z = aoi.center
            time_start = ensure_utc(aoi.time_start)
            time_end = ensure_utc(aoi.time_end)

            # Step 1: Fast time-based filtering first
            time_filter_sql = f"""
            SELECT DISTINCT track_id
            FROM {self.prop_table_name}
            WHERE (start_time, end_time) OVERLAPS ('{time_start}', '{time_end}')
            """

            # Add track_ids filter if provided
            if track_ids:
                # Create proper SQL parameter placeholders for the IN clause
                placeholders = ', '.join(
                    f"'{track_id}'" for track_id in track_ids)
                time_filter_sql += f" AND track_id IN ({placeholders})"

            result = conn.execute(text(time_filter_sql))
            time_filtered_tracks = [row[0] for row in result]

            # If no tracks in the time window, return empty list immediately
            if not time_filtered_tracks:
                return []

            # Prepare tracks list for SQL IN clause
            # For very large lists, we might need to chunk this
            max_chunk_size = 1000  # PostgreSQL has limits on clause size
            all_matching_tracks = []

            # Process in chunks if needed
            for i in range(0, len(time_filtered_tracks), max_chunk_size):
                chunk = time_filtered_tracks[i:i + max_chunk_size]
                # Create proper SQL parameter placeholders for the IN clause
                placeholders = ', '.join(f"'{track_id}'" for track_id in chunk)

                # Step 2: Apply spatial filter only to time-filtered tracks
                # Use ST_3DDistance for proper distance calculation between geometries
                # Both segment coordinates and AOI center are in kilometers
                spatial_filter_sql = f"""
                SELECT DISTINCT track_id
                FROM {self.prop_table_name}
                WHERE track_id IN ({placeholders})
                  AND (start_time, end_time) OVERLAPS ('{time_start}', '{time_end}')
                  AND ST_3DDistance(
                      segment,
                      ST_SetSRID(ST_MakePoint({x}, {y}, {z}), 0)
                  ) <= {aoi.radius}
                """

                result = conn.execute(text(spatial_filter_sql))
                matching_tracks = [row[0] for row in result]
                all_matching_tracks.extend(matching_tracks)

            return all_matching_tracks

    def query_conjunctions(self, time_start: datetime, time_end: datetime, max_distance: float) -> list[tuple[str, str, float, datetime]]:
        """
        Query for conjunctions between tracks within a time window.

        Uses a two-step filtering approach for better performance with large datasets:
        1. First get tracks within the time window (fast, uses time index)
        2. Then check for conjunctions only between time-filtered tracks

        Args:
            time_start: Start of time window
            time_end: End of time window
            max_distance: Maximum distance in kilometers for conjunction detection

        Returns:
            List of tuples containing (track_a_id, track_b_id, closest_approach_distance, time_of_closest_approach)
        """
        with self.engine.connect() as conn:
            # Convert input times to UTC
            time_start = ensure_utc(time_start)
            time_end = ensure_utc(time_end)

            # Step 1: Get tracks that exist in the time window
            time_filter_sql = f"""
            SELECT DISTINCT track_id
            FROM {self.prop_table_name}
            WHERE (start_time, end_time) OVERLAPS ('{time_start}', '{time_end}')
            """

            result = conn.execute(text(time_filter_sql))
            time_filtered_tracks = [row[0] for row in result]

            # If fewer than 2 tracks in the time window, return empty list (no conjunctions possible)
            if len(time_filtered_tracks) < 2:
                return []

            # Prepare tracks list for SQL IN clause
            placeholders = ', '.join(
                f"'{track_id}'" for track_id in time_filtered_tracks)

            # Step 2: Check for conjunctions only among time-filtered tracks
            # This query finds the closest points between line segments of different tracks
            # and returns only those within the specified distance
            conjunction_sql = f"""
            WITH relevant_tracks AS (
                -- Get all track segments in the time window
                SELECT track_id, segment, start_time, end_time
                FROM {self.prop_table_name}
                WHERE track_id IN ({placeholders})
                AND (start_time, end_time) OVERLAPS ('{time_start}', '{time_end}')
            ),
            track_pairs AS (
                -- Get all pairs of tracks that have segments in the time window
                SELECT DISTINCT
                    LEAST(t1.track_id, t2.track_id) as track_a_id,
                    GREATEST(t1.track_id, t2.track_id) as track_b_id,
                    t1.segment as seg1,
                    t2.segment as seg2,
                    t1.start_time as t1_start,
                    t1.end_time as t1_end,
                    t2.start_time as t2_start,
                    t2.end_time as t2_end
                FROM relevant_tracks t1
                JOIN relevant_tracks t2
                    ON t1.track_id < t2.track_id  -- Avoid self-joins and duplicates
                    AND (t1.start_time, t1.end_time) OVERLAPS (t2.start_time, t2.end_time)
            ),
            closest_approaches AS (
                -- Calculate closest approach for each track pair
                SELECT
                    track_a_id,
                    track_b_id,
                    ST_3DDistance(
                        ST_ClosestPoint(seg1, seg2),
                        ST_ClosestPoint(seg2, seg1)
                    ) as distance,
                    -- Estimate time of closest approach as midpoint between segment times
                    GREATEST(t1_start, t2_start) +
                        (EXTRACT(EPOCH FROM (LEAST(t1_end, t2_end) - GREATEST(t1_start, t2_start))) / 2) * INTERVAL '1 second' as approach_time
                FROM track_pairs
                WHERE ST_3DDWithin(seg1, seg2, {max_distance})
            )
            SELECT DISTINCT ON (track_a_id, track_b_id)
                track_a_id,
                track_b_id,
                distance,
                approach_time
            FROM closest_approaches
            WHERE distance <= {max_distance}
            ORDER BY track_a_id, track_b_id, distance ASC;
            """

            result = conn.execute(text(conjunction_sql))
            return [(row[0], row[1], float(row[2]), ensure_utc(row[3])) for row in result]

    def get_unpropagated_tracks_for_time_window(
        self,
        time_start: datetime,
        time_end: datetime,
        track_ids: List[str]
    ) -> List[str]:
        """
        Returns a combined list of:
        1. All track IDs from the database that have NO segments overlapping the time window
        2. Any track IDs from track_ids that don't exist in the database

        Args:
            time_start: Start of time window
            time_end: End of time window
            track_ids: List of track IDs to check for existence in database

        Returns:
            List of track IDs that need propagation
        """
        base_sql = f"""
        SELECT DISTINCT track_id
        FROM {self.prop_table_name} t1
        WHERE NOT EXISTS (
            SELECT 1
            FROM {self.prop_table_name} t2
            WHERE t2.track_id = t1.track_id
            AND (t2.start_time, t2.end_time) OVERLAPS ('{ensure_utc(time_start)}', '{ensure_utc(time_end)}')
        )
        """

        if track_ids:
            # Create proper SQL parameter placeholders for the IN clause
            placeholders = ', '.join(f"'{track_id}'" for track_id in track_ids)
            sql = f"""
            -- Get existing tracks with no segments in window
            ({base_sql})
            UNION
            -- Get tracks from input that don't exist in database
            SELECT track_id
            FROM unnest(ARRAY[{placeholders}]) AS track_id
            WHERE track_id NOT IN (
                SELECT DISTINCT track_id
                FROM {self.prop_table_name}
            )
            """
        else:
            sql = base_sql

        with self.engine.connect() as conn:
            result = conn.execute(text(sql))
            unpropagated = [row[0] for row in result.fetchall()]
            return unpropagated

    def delete_tracks(self, track_ids: List[str]) -> None:
        """
        Deletes all data correlated with the specified track IDs from the database.

        Args:
            track_ids: List of track IDs whose data should be deleted
        """
        if not track_ids:
            return

        # Create placeholders for the SQL query
        placeholders = [f":track_id_{i}" for i in range(len(track_ids))]
        track_id_params = {f"track_id_{i}": track_id for i,
                           track_id in enumerate(track_ids)}

        # SQL for deleting tracks
        sql = f"""
        DELETE FROM {self.prop_table_name}
        WHERE track_id IN ({', '.join(placeholders)})
        """

        with self.engine.connect() as conn:
            result = conn.execute(text(sql), track_id_params)
            conn.commit()

    def delete_data_outside_time_window(self, window_start: datetime, window_end: datetime) -> None:
        """
        Deletes all data in the database that is before window_start or after window_end.

        This allows for keeping only data within a specific time range and clearing older
        or future data to maintain database performance and storage efficiency.

        Args:
            window_start: Start of the time window to keep (datetime)
            window_end: End of the time window to keep (datetime)
        """
        # Parameter dictionary for the SQL query
        params = {
            "window_start": window_start,
            "window_end": window_end
        }

        # SQL to delete data outside the specified time window
        sql = f"""
        DELETE FROM {self.prop_table_name}
        WHERE start_time < :window_start OR end_time > :window_end
        """

        with self.engine.connect() as conn:
            try:
                conn.execute(text(sql), params)
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise Exception(
                    f"Failed to delete data outside time window: {e}")
