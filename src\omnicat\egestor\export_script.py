#!/usr/bin/env python3
"""
Example script to demonstrate the usage of OmnicatEgestor for exporting data
to the Visualizer-compatible format.
"""

import argparse
from datetime import datetime, timedelta, timezone

from omnicat import Settings
from omnicat.egestor import OmnicatEgestor
from omnicat.utils import ensure_utc


def parse_args():
    parser = argparse.ArgumentParser(
        description="Export OmniCat data to Visualizer format"
    )
    parser.add_argument(
        "--scenario", "-s",
        type=str,
        default="default",
        help="Scenario name (default: 'default')"
    )
    parser.add_argument(
        "--output", "-o",
        type=str,
        default="export",
        help="Output directory (default: 'export')"
    )
    parser.add_argument(
        "--minutes", "-m",
        type=int,
        default=60,
        help="Number of minutes to export (default: 60)"
    )
    parser.add_argument(
        "--start-date",
        type=str,
        default=None,
        help="Start date in ISO format (default: current time)"
    )

    return parser.parse_args()


def main():
    args = parse_args()

    # Initialize settings
    settings = Settings()

    # Create egestor directly from settings
    egestor = OmnicatEgestor.from_settings(settings)

    # Set time range
    if args.start_date:
        time_start = ensure_utc(datetime.fromisoformat(args.start_date))
    else:
        time_start = ensure_utc(datetime.now())

    time_end = time_start + timedelta(minutes=args.minutes)

    print(
        f"Exporting scenario '{args.scenario}' from {time_start} to {time_end}")

    # Export data
    scenario_dir = egestor.export_scenario(
        scenario_name=args.scenario,
        time_start=time_start,
        time_end=time_end,
        output_dir=args.output
    )
    print(f"Export completed. Data available at: {scenario_dir}")


if __name__ == "__main__":
    main()
