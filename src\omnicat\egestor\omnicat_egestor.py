import os
import json
import csv
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple

from omnicat.db import Neo4jDb, PostGisDb
from omnicat.models import Object, Track
from omnicat import Settings


def get_satellite_id_from_object(obj: Object) -> str | None:
    """
    Extract a satellite ID from an object, checking metadata first.

    Args:
        obj: Object instance

    Returns:
        Satellite ID string, or None if no ID could be found
    """
    sat_id = None
    if obj and obj.metadata:
        # Check for common ID fields in metadata
        sat_id = (obj.metadata.get('satellite_id') or 
                 obj.metadata.get('id') or 
                 obj.metadata.get('catalog_id'))

    # If no ID found in metadata, use the object's ID
    return sat_id or obj.internal_id


def create_time_series_row(point: Dict[str, Any], sat_id: str, time_start: datetime) -> Dict[str, Any]:
    """
    Create a time series data row from a point.

    Args:
        point: Point data from database
        sat_id: Satellite ID for this point
        time_start: Start time for calculating relative timestamp

    Returns:
        Dictionary containing the time series row data
    """
    time_seconds = (point['time'] - time_start).total_seconds()
    
    x = point['x']
    y = point['y']
    z = point['z']
    
    row = {
        'SatNo': sat_id,
        'timestamp': time_seconds,
        'x': x,
        'y': y,
        'z': z,
        'vx': point.get('vx', 0.0),
        'vy': point.get('vy', 0.0),
        'vz': point.get('vz', 0.0),
        'rot_x': point.get('rot_x', ''),
        'rot_y': point.get('rot_y', ''),
        'rot_z': point.get('rot_z', ''),
        'sensors': '[]',
        'fused_x': point.get('fused_x', x),
        'fused_y': point.get('fused_y', y),
        'fused_z': point.get('fused_z', z),
        'entry_type': 'Propagated'
    }
    
    return row


def generate_csv_header(time_start: datetime, time_end: datetime, num_timesteps: int, timestep_length: int) -> str:
    """
    Generate CSV header content.

    Args:
        time_start: Start time for the data
        time_end: End time for the data
        num_timesteps: Number of unique timesteps
        timestep_length: Length of each timestep in seconds

    Returns:
        String containing the formatted header content
    """
    header_content = "HEADER_START\n"
    header_content += "variable_name,value\n"
    header_content += f"time_start,{time_start.strftime('%Y-%m-%d %H:%M:%S')}\n"
    header_content += f"time_end,{time_end.strftime('%Y-%m-%d %H:%M:%S')}\n"
    header_content += f"number_of_timesteps,{num_timesteps}\n"
    header_content += f"timestep_length_seconds,{timestep_length}\n"
    header_content += "HEADER_END\n\n"  # Add empty line after header section
    return header_content


def generate_sensor_section() -> str:
    """
    Generate sensor section content.

    Returns:
        String containing the formatted sensor section and CSV column headers
    """
    sensor_content = "SENSOR_START\nSENSOR_END\n\n"  # Sensor tags and an empty line after
    sensor_content += "SatNo,timestamp,x,y,z,vx,vy,vz,rot_x,rot_y,rot_z,sensors,fused_x,fused_y,fused_z,entry_type\n"
    return sensor_content


def set_default_values_for_row(row: Dict[str, Any]) -> None:
    """
    Set default values for optional fields in a time series data row.

    Args:
        row: Time series data row to update with default values
    """
    row.setdefault('vx', 0.0)
    row.setdefault('vy', 0.0)
    row.setdefault('vz', 0.0)
    row.setdefault('rot_x', '')
    row.setdefault('rot_y', '')
    row.setdefault('rot_z', '')
    row.setdefault('sensors', '[]')
    row.setdefault('fused_x', '')
    row.setdefault('fused_y', '')
    row.setdefault('fused_z', '')
    row.setdefault('entry_type', 'Propagated')


def write_json_file(file_path: str, data: Any) -> None:
    """
    Write data to a JSON file.

    Args:
        file_path: Path to the JSON file
        data: Data to write (must be JSON serializable)
    """
    with open(file_path, 'w') as f:
        json.dump(data, f, indent=2)


def create_json_file(file_path: str, default_data: Any) -> None:
    """
    Create a JSON file with default data.

    Args:
        file_path: Path to the JSON file
        default_data: Default data structure to write to the file
    """
    write_json_file(file_path, default_data)


class OmnicatEgestor:
    """
    OmnicatEgestor handles exporting data from OmniCat's databases to files
    following the format specified in the OmniCat-Visualizer Interface.

    This class extracts data from Neo4j and PostGIS databases and writes
    it to a structured file format for consumption by visualization tools.
    """

    def __init__(self, neo4j_db: Neo4jDb, postgis_db: PostGisDb):
        """
        Initialize the OmnicatEgestor.

        Args:
            neo4j_db: Neo4jDb instance for accessing object and track data
            postgis_db: PostGisDb instance for accessing spatial data
        """
        self.neo4j_db = neo4j_db
        self.postgis_db = postgis_db

    @classmethod
    def from_settings(cls, settings: Settings) -> 'OmnicatEgestor':
        """
        Create an OmnicatEgestor instance directly from Settings.

        Args:
            settings: Application settings

        Returns:
            OmnicatEgestor instance with database connections
        """
        return cls(
            neo4j_db=Neo4jDb(**settings.neo4j.model_dump()),
            postgis_db=PostGisDb.from_settings(settings.postgis)
        )

    def export_scenario(self, scenario_name: str, time_start: datetime, time_end: datetime, output_dir: str = "visualizer_output") -> str:
        """
        Export data for a specific scenario to the file structure defined in the interface.

        Args:
            scenario_name: Name of the scenario
            time_start: Start time for the data export
            time_end: End time for the data export
            output_dir: Base directory for output files (default: "visualizer_output")

        Returns:
            Path to the scenario directory
        """
        scenario_dir = os.path.join(output_dir, scenario_name)
        os.makedirs(scenario_dir, exist_ok=True)

        csv_file_path = os.path.join(scenario_dir, f"{scenario_name}.csv")
        
        object_ids = self.neo4j_db.get_all_object_ids()
        time_series_data = self._get_time_series_data(object_ids, time_start, time_end)
        
        self._write_csv_file(csv_file_path, time_start, time_end, time_series_data)
        
        self._create_required_json_files(scenario_dir)

        return scenario_dir

    def _get_track_ids_for_objects(self, object_ids: List[str]) -> List[str]:
        """
        Get all track IDs correlated with the given object IDs.

        Args:
            object_ids: List of object IDs

        Returns:
            List of track IDs correlated with the objects
        """
        all_track_ids = []
        for object_id in object_ids:
            track_ids = self.neo4j_db.get_track_ids_for_object(object_id)
            all_track_ids.extend(track_ids)
        return all_track_ids

    def _build_track_to_object_map(self, object_ids: List[str]) -> Dict[str, str]:
        """
        Build a mapping of track IDs to their correlated object IDs.

        Args:
            object_ids: List of object IDs

        Returns:
            Dictionary mapping track IDs to object IDs
        """
        track_to_object_map = {}
        for object_id in object_ids:
            obj = self.neo4j_db.get_object(object_id)
            if not obj:
                continue
                
            track_ids = self.neo4j_db.get_track_ids_for_object(object_id)
            
            if not track_ids:
                continue
                
            sat_id = get_satellite_id_from_object(obj)
            
            for track_id in track_ids:
                track_to_object_map[track_id] = sat_id
                
        return track_to_object_map

    def _get_time_series_data(self, object_ids: List[str], time_start: datetime, time_end: datetime) -> List[Dict[str, Any]]:
        """
        Get time series data for all objects within the specified time window.

        Args:
            object_ids: List of object IDs
            time_start: Start time for the data
            time_end: End time for the data

        Returns:
            List of dictionaries containing time series data
        """
        time_series_data = []
        
        all_track_ids = self._get_track_ids_for_objects(object_ids)
            
        if not all_track_ids:
            return []
            
        propagated_data = self.postgis_db.get_propagated_data_for_tracks(
            all_track_ids, time_start, time_end
        )
        
        if not propagated_data:
            return []
            
        track_to_object_map = self._build_track_to_object_map(object_ids)
                
        for point in propagated_data:
            if 'track_id' not in point or point['track_id'] not in track_to_object_map:
                continue
                
            sat_id = track_to_object_map[point['track_id']]
            
            row = create_time_series_row(point, sat_id, time_start)
            time_series_data.append(row)
                
        time_series_data.sort(key=lambda x: x['timestamp'])
        return time_series_data

    def _write_csv_file(self, file_path: str, time_start: datetime, time_end: datetime, 
                       time_series_data: List[Dict[str, Any]]) -> None:
        """
        Write the CSV file with header, sensors, and time series data.

        Args:
            file_path: Path to the output CSV file
            time_start: Start time for the data
            time_end: End time for the data
            time_series_data: List of dictionaries containing time series data
        """
        if not time_series_data:
            print(f"No time series data available for {file_path}")
            # Write an empty file with just headers and placeholders
            with open(file_path, 'w', newline='') as f:
                header_content = generate_csv_header(time_start, time_end, 0, 60)
                sensor_content = generate_sensor_section()
                
                f.write(header_content)
                f.write(sensor_content)
            return
            
        # Calculate number of timesteps
        unique_timestamps = sorted(set(item['timestamp'] for item in time_series_data))
        if len(unique_timestamps) > 1:
            # Calculate average timestep length 
            timestep_lengths = [unique_timestamps[i+1] - unique_timestamps[i] 
                               for i in range(len(unique_timestamps)-1)]
            avg_timestep = sum(timestep_lengths) / len(timestep_lengths)
        else:
            avg_timestep = 60  # Default to 60 seconds if only one timestamp
        
        with open(file_path, 'w', newline='') as f:
            header_content = generate_csv_header(
                time_start, time_end, len(unique_timestamps), int(avg_timestep))
            sensor_content = generate_sensor_section()
            
            f.write(header_content)
            f.write(sensor_content)
            
            if time_series_data:
                # Define column order to match the spec
                fieldnames = [
                    'SatNo', 'timestamp', 'x', 'y', 'z', 
                    'vx', 'vy', 'vz', 
                    'rot_x', 'rot_y', 'rot_z',
                    'sensors', 
                    'fused_x', 'fused_y', 'fused_z',
                    'entry_type'
                ]
                
                for row in time_series_data:
                    set_default_values_for_row(row)
                
                writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
                writer.writerows(time_series_data)

    def _create_required_json_files(self, scenario_dir: str) -> None:
        """
        Create all required JSON files for a scenario.

        Args:
            scenario_dir: Path to the scenario directory
        """
        events_file_path = os.path.join(scenario_dir, "Events.json")
        events_data = {
            "collisions": []
        }
        create_json_file(events_file_path, events_data)
        
        rockets_file_path = os.path.join(scenario_dir, "Rockets.json")
        rockets_data = []
        create_json_file(rockets_file_path, rockets_data)
        
        gs_file_path = os.path.join(scenario_dir, "GroundStations.json")
        ground_stations_data = {
            "radar_sensors": [],
            "optical_sensors": []
        }
        create_json_file(gs_file_path, ground_stations_data)
