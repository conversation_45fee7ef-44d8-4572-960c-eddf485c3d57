from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.exceptions import RequestValidationError
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, FileResponse, PlainTextResponse
import yaml
from pathlib import Path
import json
import os

from omnicat.http.middleware.cors import configure_cors
from omnicat.http.exceptions.handlers import http_exception_handler, validation_exception_handler
from omnicat.http.routes import (
    create_object_router, create_track_router,
    create_correlation_router, create_conjunction_router, create_export_router
)
from omnicat.omnicat import OmniCat, get_omnicat
from omnicat import Settings


def add_exception_handlers(app: FastAPI) -> None:
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError,
                              validation_exception_handler)


def add_web_routes(app: FastAPI) -> None:
    BASE_PATH = Path(__file__).parent

    app.mount("/static", StaticFiles(directory=BASE_PATH / "static"), name="static")
    templates = Jinja2Templates(directory=BASE_PATH / "templates")

    @app.get("/", response_class=HTMLResponse, tags=["Home"])
    def home(request: Request) -> HTMLResponse:
        """
        Home Page
        """
        return templates.TemplateResponse(request, "index.html")

    @app.get("/favicon.ico", include_in_schema=False, tags=["Home"])
    async def favicon() -> FileResponse:
        """
        favicon
        """
        return FileResponse(BASE_PATH / "static" / "images" / "favicon.png")

    @app.get("/aoi-query-gui", response_class=HTMLResponse, tags=["GUI"])
    def aoi_query_gui(request: Request) -> HTMLResponse:
        """
        AOI Query GUI
        """
        return templates.TemplateResponse(request, "aoi-query-gui.html")

    @app.get("/conjunction-query-gui", response_class=HTMLResponse, tags=["GUI"])
    def conjunction_query_gui(request: Request) -> HTMLResponse:
        """
        Conjunction Query GUI
        """
        return templates.TemplateResponse(request, "conjunction-query-gui.html")
    
    @app.get("/export/gui", response_class=HTMLResponse, tags=["GUI"])
    def export_gui(request: Request) -> HTMLResponse:
        """
        Export Data GUI
        """
        return templates.TemplateResponse(request, "export-gui.html")


def add_openapi_yaml_routes(app: FastAPI) -> None:
    @app.get("/openapi.yaml", include_in_schema=False)
    @app.get("/openapi.yml", include_in_schema=False)
    async def get_openapi_yaml():
        openapi_json = app.openapi()
        yaml_content = yaml.dump(openapi_json, sort_keys=False)
        return PlainTextResponse(yaml_content, media_type="text/yaml")


def add_routes(app: FastAPI, omnicat: OmniCat) -> None:
    add_web_routes(app)
    add_openapi_yaml_routes(app)
    app.include_router(create_object_router(omnicat))
    app.include_router(create_track_router(omnicat))
    app.include_router(create_correlation_router(omnicat))
    app.include_router(create_conjunction_router(omnicat))
    app.include_router(create_export_router(omnicat))


def create_app(settings: Settings) -> FastAPI:
    app = FastAPI(title="OmniCat Service API")
    configure_cors(app)
    add_exception_handlers(app)

    omnicat = get_omnicat(settings)
    add_routes(app, omnicat)

    return app
