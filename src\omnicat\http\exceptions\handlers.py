from fastapi import Request, HTTPException, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from omnicat.models import <PERSON>rrorResponse


def http_exception_handler(request: Request, exc: HTTPException) -> ErrorResponse:
    """
    Handles http errors.
    """
    error_response = ErrorResponse(
        code=exc.status_code,
        message=exc.detail
    )
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump()
    )


def validation_exception_handler(request: Request, exc: RequestValidationError) -> ErrorResponse:
    """
    Handles validation errors, such as Pydantic validation exceptions.
    """
    error_response = ErrorResponse(
        code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        message="invalid request"
    )
    return JSONResponse(
        status_code=error_response.code,
        content=error_response.model_dump()
    )
