from fastapi import APIRouter, Query, Path
from datetime import datetime
from typing import Optional
from omnicat.models import QueryConjunctionsResponse, Conjunction, Pagination
from omnicat.http.routes.route_responses import get_data_responses
from omnicat import OmniCat

conjunctions_tags = ["Conjunctions"]


def create_conjunction_router(omnicat: OmniCat) -> APIRouter:
    conjunction_router = APIRouter()

    @conjunction_router.get(
        "/conjunctions/query",
        responses=get_data_responses(
            "Conjunctions", QueryConjunctionsResponse),
        tags=conjunctions_tags
    )
    def query_conjunctions(
        time_start: datetime = Query(...,
                                     description="Start time of the interval."),
        time_end: datetime = Query(...,
                                   description="End time of the interval."),
        max_distance: Optional[float] = Query(
            10.0, description="Maximum distance for conjunctions in kilometers."),
        page: int = Query(1, description="Page number for pagination.", gt=0),
        page_size: int = Query(
            50, description="Number of records per page.", gt=0),
    ) -> QueryConjunctionsResponse:
        """
        Retrieves conjunctions (close approaches) within a specified time interval.
        """
        # Query for conjunctions
        conjunction_results = omnicat.query_conjunctions(
            time_start, time_end, max_distance)

        # Calculate pagination
        total_records = len(conjunction_results)
        total_pages = (total_records + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = min(start_idx + page_size, total_records)

        # Convert results to Conjunction objects
        conjunctions = [
            Conjunction(
                track_a_id=track_a_id,
                track_b_id=track_b_id,
                closest_approach_distance=distance,
                time_of_closest_approach=approach_time
            )
            for track_a_id, track_b_id, distance, approach_time in conjunction_results[start_idx:end_idx]
        ]

        return QueryConjunctionsResponse(
            conjunctions=conjunctions,
            pagination=Pagination(
                page=page,
                page_size=page_size,
                total_pages=total_pages,
                total_records=total_records
            )
        )

    return conjunction_router
