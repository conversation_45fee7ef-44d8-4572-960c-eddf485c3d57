from fastapi import APIRouter, status, HTTPException
from omnicat.models import CorrelateTrackRequest, StatusMessage
from omnicat.http.routes.route_responses import add_data_responses
from omnicat import OmniCat


correlation_tags = ["Correlations"]


def create_correlation_router(omnicat: OmniCat) -> APIRouter:

    correlation_router = APIRouter()

    @correlation_router.post(
        "/correlate",
        responses=add_data_responses("Correlation", StatusMessage),
        status_code=status.HTTP_201_CREATED,
        tags=correlation_tags
    )
    def correlate_track(request: CorrelateTrackRequest) -> StatusMessage:
        """
        Correlates a track with an object, optionally specifying a confidence level and metadata.
        """
        try:
            if request.correlation_type == "object":
                omnicat.correlate_track_with_object(
                    request.track_id, request.correlation)
            else:
                raise HTTPException(
                    status_code=422, detail="Invalid correlation type.")
        except ValueError as e:
            raise HTTPException(status_code=404, detail=str(e))
        except HTTPException as e:
            raise e
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

        return StatusMessage(message="Correlation created successfully.")

    return correlation_router
