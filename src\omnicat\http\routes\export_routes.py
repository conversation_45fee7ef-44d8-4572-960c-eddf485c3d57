from fastapi import APIRouter, Form, HTTPException, status
from fastapi.responses import Response, FileResponse
from pydantic import BaseModel, ValidationError
from datetime import datetime, timedelta
import os
import atexit
from typing import Dict, Any, Optional, Callable, List

from omnicat import OmniCat
from omnicat.utils import ensure_utc
from omnicat.logger import logger

export_tags = ["Export"]

# Keep track of temporary files to clean up at exit
TEMP_FILES: List[str] = []

# Register cleanup function to run at program exit
def cleanup_temp_files():
    """Clean up any temporary files at program exit."""
    for path in TEMP_FILES:
        if os.path.exists(path):
            try:
                os.unlink(path)
            except Exception:
                pass

# Register the cleanup function
atexit.register(cleanup_temp_files)


class ExportResponse(BaseModel):
    """Response model for export operations"""
    message: str
    scenario_dir: str


def create_export_router(omnicat: OmniCat) -> APIRouter:
    """Create FastAPI router for export endpoints"""
    export_router = APIRouter()
    
    @export_router.post(
        "/export/generate",
        responses={
            200: {"model": ExportResponse},
            status.HTTP_422_UNPROCESSABLE_ENTITY: {"description": "Validation Error"},
            status.HTTP_500_INTERNAL_SERVER_ERROR: {"description": "Internal Server Error"},
        },
        tags=export_tags
    )
    async def generate_export(
        scenario: str = Form(..., description="Scenario name"),
        start_time: str = Form(..., description="Start time in ISO format"),
        minutes: int = Form(60, description="Duration in minutes")
    ) -> Response:
        """
        Generate an export of OmniCat data to Visualizer format and return it as a downloadable tar.gz file
        """
        try:
            # Validate start_time format before proceeding
            try:
                time_start = ensure_utc(datetime.fromisoformat(start_time.replace('Z', '+00:00')))
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, 
                    detail="Invalid start_time format. Use ISO format (e.g., 2023-01-01T12:00:00Z)"
                )
                
            # Calculate end time
            time_end = time_start + timedelta(minutes=minutes)
            
            # Generate the export using OmniCat
            # output_dir is None so a temporary directory will be created
            tar_path = omnicat.generate_export(
                scenario_name=scenario,
                time_start=time_start,
                time_end=time_end,
                output_dir=None
            )
            
            # Track the file for cleanup
            TEMP_FILES.append(tar_path)
            
            # Create a response with the file content
            with open(tar_path, "rb") as file:
                content = file.read()
            
            headers = {
                "Content-Disposition": f'attachment; filename="{os.path.basename(tar_path)}"',
                "Content-Type": "application/octet-stream"
            }
            return Response(content=content, headers=headers)
            
        except HTTPException:
            # Re-raise original HTTP exception
            raise
        except Exception as e:
            # Log the error
            logger.error(f"Export failed: {str(e)}")
            
            # Re-raise as HTTP exception
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error"
            )
    
    return export_router 