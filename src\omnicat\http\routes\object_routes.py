from fastapi import APIRouter, Path, Body, status, HTTPException
import uuid
from omnicat.models import Object, AddObjectResponse, GetObjectResponse, object_id_description, StatusMessage, Metadata, GetObjectMetadataResponse
from omnicat.http.routes.route_responses import add_data_responses, get_data_responses, get_metadata_responses, add_metadata_responses, update_metadata_responses
from omnicat import OmniCat

object_tags = ["Objects"]


def create_object_router(omnicat: OmniCat) -> APIRouter:

    object_router = APIRouter()

    @object_router.post(
        "/objects",
        status_code=status.HTTP_201_CREATED,
        responses=add_data_responses("Object", AddObjectResponse),
        tags=object_tags
    )
    def add_object(
        obj: Object = Body(...,
                           description="The object to add to the catalog."),
    ) -> AddObjectResponse:
        """
        Creates a new object, giving it with a UUID in the database. Metadata may
        optionally include an "International Designator," but the object will always
        be stored under a UUID in the database.
        """
        try:
            object_id = omnicat.add_object(obj)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
        return AddObjectResponse(object_id=object_id, message="Object created successfully.")

    @object_router.get(
        "/objects/{identifier}",
        responses=get_data_responses("Object", GetObjectResponse),
        tags=object_tags
    )
    def get_objects(
        identifier: str = Path(..., description=object_id_description),
    ) -> GetObjectResponse | list[GetObjectResponse]:
        """
        Retrieves objects by their `object_id` or `International Designator`.
        If the `object_id` is a UUID, it directly retrieves the object.
        If the `object_id` is an "International Designator," it searches the database.

        Supports comma-separated identifiers (e.g., "id1,id2,id3") to retrieve multiple objects.
        Returns a single GetObjectResponse for single identifiers (backward compatibility),
        or a list of GetObjectResponse for comma-separated identifiers.
        """
        # Split the comma-separated identifiers and strip whitespace
        identifiers = [id.strip() for id in identifier.split(',')]
        objects = omnicat.get_objects(identifiers)

        if not objects:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="No objects found.")

        objects_by_id = {
            **{obj.internal_id: obj for obj in objects},
            **{obj.metadata["International Designator"]: obj
               for obj in objects
               if obj.metadata and "International Designator" in obj.metadata}
        }

        responses = [
            GetObjectResponse(
                object_id=orig_id,
                object=objects_by_id[orig_id],
                correlations=[]
            )
            for orig_id in identifiers
            if orig_id in objects_by_id
        ]

        # For backward compatibility: return single object if no comma in original identifier
        if ',' not in identifier and len(responses) == 1:
            return responses[0]
        else:
            return responses

    return object_router
