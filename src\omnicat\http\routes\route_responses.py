from typing import Dict, TypeAlias
from fastapi import status
from omnicat.models import ErrorResponse, StatusMessage


def add_data_responses(name: str, model: TypeAlias) -> Dict[int, Dict[str, TypeAlias]]:
    return {
        status.HTTP_201_CREATED: {"description": f"{name} created successfully.", "model": model},
        status.HTTP_400_BAD_REQUEST: {"description": "Invalid request body.", "model": ErrorResponse},
        status.HTTP_422_UNPROCESSABLE_ENTITY: {"description": f"Invalid {name} data.", "model": ErrorResponse},
        status.HTTP_500_INTERNAL_SERVER_ERROR: {"description": "Internal server error.", "model": ErrorResponse},
    }


def get_data_responses(name: str, model: TypeAlias) -> Dict[int, Dict[str, TypeAlias]]:
    return {
        status.HTTP_200_OK: {"description": f"{name} retrieved successfully.", "model": model},
        status.HTTP_400_BAD_REQUEST: {"description": "Invalid request body.", "model": ErrorResponse},
        status.HTTP_404_NOT_FOUND: {"description": f"{name} not found.", "model": ErrorResponse},
        status.HTTP_422_UNPROCESSABLE_ENTITY: {"description": "Invalid request body.", "model": ErrorResponse},
        status.HTTP_500_INTERNAL_SERVER_ERROR: {"description": "Internal server error.", "model": ErrorResponse},
    }


def add_metadata_responses(data_name: str, model: TypeAlias) -> Dict[int, Dict[str, TypeAlias]]:
    return {
        status.HTTP_201_CREATED: {"description": f"{data_name} metadata created successfully.", "model": StatusMessage},
        status.HTTP_400_BAD_REQUEST: {"description": "Invalid request body.", "model": ErrorResponse},
        status.HTTP_404_NOT_FOUND: {"description": f"{data_name} not found.", "model": ErrorResponse},
        status.HTTP_422_UNPROCESSABLE_ENTITY: {"description": "Invalid metadata.", "model": ErrorResponse},
        status.HTTP_500_INTERNAL_SERVER_ERROR: {"description": "Internal server error.", "model": ErrorResponse},
    }


def get_metadata_responses(data_name: str, model: TypeAlias) -> Dict[int, Dict[str, TypeAlias]]:
    return {
        status.HTTP_200_OK: {"description": f"{data_name} metadata retrieved successfully.", "model": model},
        status.HTTP_400_BAD_REQUEST: {"description": "Invalid request body.", "model": ErrorResponse},
        status.HTTP_404_NOT_FOUND: {"description": f"{data_name} not found.", "model": ErrorResponse},
        status.HTTP_422_UNPROCESSABLE_ENTITY: {"description": "Invalid metadata.", "model": ErrorResponse},
        status.HTTP_500_INTERNAL_SERVER_ERROR: {"description": "Internal server error.", "model": ErrorResponse},
    }


def update_metadata_responses(data_name: str, model: TypeAlias) -> Dict[int, Dict[str, TypeAlias]]:
    return {
        status.HTTP_200_OK: {"description": f"{data_name} metadata updated successfully.", "model": model},
        status.HTTP_400_BAD_REQUEST: {"description": "Invalid request body.", "model": ErrorResponse},
        status.HTTP_404_NOT_FOUND: {"description": f"{data_name} not found.", "model": ErrorResponse},
        status.HTTP_422_UNPROCESSABLE_ENTITY: {"description": "Invalid metadata.", "model": ErrorResponse},
        status.HTTP_500_INTERNAL_SERVER_ERROR: {"description": "Internal server error.", "model": ErrorResponse},
    }
