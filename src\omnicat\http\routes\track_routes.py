from fastapi import APIRouter, Body, Query, Path, status, HTTPException
from omnicat.models.track_models import Track, AddTrackResponse, GetTrackResponse, GetTrackMetadataResponse, QueryTracksResponse, TrackFilterOptionsResponse, CorrelatedTracksFilter, TrackFilter, CorrelationStatus, get_track_filter
from omnicat.models import StatusMessage, Metadata, Vector3D, StateVector, Pagination, AreaOfInterest
from omnicat import OmniCat, logger
from omnicat.http.routes.route_responses import add_data_responses, get_data_responses, add_metadata_responses, get_metadata_responses, update_metadata_responses
from datetime import datetime
from typing import Optional
import uuid
import math
from pydantic import ValidationError

track_tags = ["Tracks"]


def create_track_router(omnicat: OmniCat) -> APIRouter:

    track_router = APIRouter()

    @track_router.get("/tracks/query", responses=get_data_responses("Tracks", QueryTracksResponse), tags=track_tags)
    def query_tracks_within_aoi(
        center: Vector3D = Query(
            ..., description="Geodetic center of aoi (latitude degrees, longitude degrees, altitude kilometers)."),
        radius: float = Query(...,
                              description="Radius of aoi in kilometers.", gt=0),
        time_start: datetime = Query(...,
                                     description="Start time of the interval."),
        time_end: datetime = Query(...,
                                   description="End time of the interval."),
        format: str = Query(
            "state_vector", description="Format of track data (state_vector or tle)."),
        page: int = Query(0, description="Page number for pagination.", ge=0),
        page_size: int = Query(
            100, description="Number of records per page.", gt=0),
        sources: list[str] = Query(
            None, description="Filter by track sources."),
        object_types: list[str] = Query(
            None, description="Filter by object types."),
        country_codes: list[str] = Query(
            None, description="Filter by country codes."),
        allegiances: list[str] = Query(
            None, description="Filter by allegiance colors."),
        correlation_statuses: list[str] = Query(
            None, description="Filter by correlation status. Possible values: 'Correlated', 'Uncorrelated'. If not provided, both statuses are included."),
    ) -> QueryTracksResponse:
        """
        Retrieves all tracks within a specified aoi.
        Optionally filter tracks based on sources, object types, country codes, and allegiances.
        """
        aoi = AreaOfInterest(
            center=center, radius=radius, time_start=time_start, time_end=time_end)
        logger.info(f"Querying tracks within aoi: {aoi}")

        start_time = datetime.now()

        try:
            filter_params = get_track_filter(
                sources=sources,
                object_types=object_types,
                country_codes=country_codes,
                allegiances=allegiances,
                correlation_statuses=correlation_statuses
            )
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))

        track_ids, tracks = omnicat.query_filtered_tracks_within_aoi(
            aoi, filter_params)

        query_duration = (datetime.now() - start_time).total_seconds()
        logger.info(f"Track query completed in {
                    query_duration:.3f} seconds. Found {len(tracks)} tracks.")

        track_responses = [GetTrackResponse(
            track_id=track_id, track=track.as_format(format)) for (track_id, track) in zip(track_ids, tracks)]

        pagination, paginated_tracks = Pagination.paginate(
            records=track_responses, page=page, page_size=page_size)

        return QueryTracksResponse(tracks=paginated_tracks, pagination=pagination)

    @track_router.post(
        "/tracks",
        responses=add_data_responses("Track", AddTrackResponse),
        status_code=status.HTTP_201_CREATED,
        tags=track_tags
    )
    def add_track(
        track: Track = Body(...,
                            description="The track to add to the catalog.")
    ) -> AddTrackResponse:
        """
        Adds a new track to the system. A track can be provided in either state vector format
        or as a Two-Line Element (TLE) set. Regardless of the input format, the underlying
        storage converts and stores the track as a state vector in the ECI frame.
        """
        track_id = omnicat.add_track(track)
        return AddTrackResponse(track_id=track_id, message="Track created successfully.")

    @track_router.get("/tracks/filter-options", responses=get_data_responses("Track filter options", TrackFilterOptionsResponse), tags=track_tags)
    def get_track_filter_options() -> TrackFilterOptionsResponse:
        """
        Retrieves available filter options for tracks based on what exists in the database.
        Returns lists of available sources, object types, country codes, and allegiances
        that can be used for filtering tracks.
        """
        filter_options = omnicat.get_track_filter_options()
        return TrackFilterOptionsResponse(**filter_options)

    @track_router.get("/tracks/{track_id}", responses=get_data_responses("Track", GetTrackResponse), tags=track_tags)
    def get_track(
        track_id: str = Path(...,
                             description="unique id of track to retrieve"),
        format: str = Query(
            "state_vector", description="format of track data.", json_schema_extra={"enum": ["state_vector", "tle"]})
    ) -> GetTrackResponse:
        """
        Retrieves detailed information about a specific track, including metadata and
        correlations. The track data can be returned in either `state_vector` (default)
        or `tle` format.
        """
        track = omnicat.get_track(track_id)
        if not track:
            raise HTTPException(status_code=404, detail="Track not found.")

        return GetTrackResponse(track_id=track_id, track=track.as_format(format))

    return track_router
