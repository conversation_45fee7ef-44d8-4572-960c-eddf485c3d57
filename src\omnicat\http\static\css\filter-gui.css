/* Filter Box Styles */
.filter-box {
    position: fixed;
    top: 150px;
    left: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-width: 300px;
    overflow-y: auto;
    max-height: 70vh;
}

.filter-box h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    text-align: center;
    border-bottom: 1px solid #ddd;
    padding-bottom: 8px;
}

.filter-section {
    margin-bottom: 15px;
}

.filter-section h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
}

.filter-input-container {
    position: relative;
    margin-bottom: 5px;
}

.filter-input {
    width: 100%;
    padding: 6px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 100%;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
    max-height: 200px;
    overflow-y: auto;
    border-radius: 0 0 4px 4px;
}

.dropdown-content.show {
    display: block;
}

.dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
}

.dropdown-item:hover {
    background-color: #f1f1f1;
}

.selected-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
}

.filter-tag {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 2px 8px;
    display: flex;
    align-items: center;
    font-size: 12px;
}

.filter-tag-remove {
    margin-left: 5px;
    cursor: pointer;
    color: #777;
    font-weight: bold;
}

.filter-tag-remove:hover {
    color: #ff3b30;
} 