// Common UI helper functions
function closePersistentLabel() {
    document.getElementById('persistentLabel').style.display = 'none';
}

function showPersistentLabel(content) {
    const label = document.getElementById('persistentLabel');
    const labelContent = document.getElementById('persistentLabelContent');
    labelContent.innerHTML = content;
    label.style.display = 'block';
}

// Setup click handlers for persistent label
function setupPersistentLabelHandlers() {
    document.addEventListener('click', function (event) {
        const persistentLabel = document.getElementById('persistentLabel');
        const isClickInside = persistentLabel.contains(event.target);

        if (persistentLabel.style.display === 'block' && !isClickInside) {
            closePersistentLabel();
        }
    });

    document.getElementById('persistentLabel').addEventListener('click', function (event) {
        event.stopPropagation();
    });
}

// Set default times for datetime inputs
function setDefaultTimes() {
    const now = new Date();
    const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);

    document.getElementById('startTime').value = now.toISOString().slice(0, 16);
    document.getElementById('endTime').value = oneHourLater.toISOString().slice(0, 16);
}