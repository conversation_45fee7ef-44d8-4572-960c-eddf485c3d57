// Constants
import * as THREE from 'https://cdn.jsdelivr.net/npm/three@0.158.0/build/three.module.js';
export const EARTH_RADIUS_KM = 6371; // km

// Utility functions
export function eciToLatLngAlt(eciCoords, gmst) {
    const geodetic = satellite.eciToGeodetic(eciCoords, gmst);
    return {
        lat: satellite.radiansToDegrees(geodetic.latitude),
        lng: satellite.radiansToDegrees(geodetic.longitude),
        alt: geodetic.height / EARTH_RADIUS_KM
    };
}

// Globe visualization module
export class GlobeVisualizer {
    constructor(elementId, options = {}) {
        this.elementId = elementId;
        this.world = null;
        this.trackSpheres = [];
        this.options = {
            initialLat: 0,
            initialLng: 0,
            initialAltitude: 2.5,
            ...options
        };
    }

    init() {
        return fetch('/static/data/ne_110m_admin_0_countries.geojson')
            .then(res => res.json())
            .then(countries => {
                this.world = new Globe(document.getElementById(this.elementId))
                    .globeImageUrl('//unpkg.com/three-globe/example/img/earth-day.jpg')
                    .backgroundColor('rgba(0,0,0,0)')
                    .backgroundImageUrl('//unpkg.com/three-globe/example/img/night-sky.png')
                    .lineHoverPrecision(0)
                    .objectLat('lat')
                    .objectLng('lng')
                    .objectAltitude('alt')
                    .objectFacesSurface(false)
                    .polygonsData(countries.features.filter(d => d.properties.ISO_A2 !== 'AQ'))
                    .polygonAltitude(0.005)
                    .polygonCapColor(() => 'rgba(0, 0, 0, 0)')
                    .polygonSideColor(() => 'rgba(0, 100, 0, 0.15)')
                    .polygonStrokeColor(() => '#000000')
                    .polygonLabel(this._createPolygonLabel);

                // Set initial point of view
                this.world.pointOfView({
                    lat: this.options.initialLat,
                    lng: this.options.initialLng,
                    altitude: this.options.initialAltitude
                });

                return this.world;
            });
    }

    _createPolygonLabel({ properties: d, geometry }) {
        let coords;
        if (geometry.type === 'Polygon') {
            coords = geometry.coordinates[0];  // First linear ring
        } else if (geometry.type === 'MultiPolygon') {
            coords = geometry.coordinates[0][0];  // First linear ring of first polygon
        }

        if (!coords) return `<div>${d.ADMIN}</div>`;  // Fallback if no coordinates

        const centroid = coords.reduce((acc, curr) => [acc[0] + curr[0], acc[1] + curr[1]], [0, 0])
            .map(sum => sum / coords.length);

        return `
            <div style="background: rgba(0,0,0,0.7); color: white; padding: 5px; border-radius: 3px;">
                <b>${d.ADMIN}</b><br/>
                Lat: ${centroid[1].toFixed(2)}°, Lon: ${centroid[0].toFixed(2)}°
            </div>
        `;
    }

    clearPaths() {
        const existingPaths = this.world.scene().children.filter(child => child.isSatPath);
        existingPaths.forEach(path => this.world.scene().remove(path));
    }

    addPath(points, color = 'red', lineWidth = 2) {
        const linePoints = points.map(p => this.world.getCoords(p.lat, p.lng, p.alt));
        const geometry = new THREE.BufferGeometry().setFromPoints(linePoints);
        const material = new THREE.LineBasicMaterial({
            color: color,
            linewidth: lineWidth
        });
        const line = new THREE.Line(geometry, material);
        line.isSatPath = true;
        this.world.scene().add(line);
    }

    updateObjects(objects) {
        this.world.objectsData(objects);
    }

    // Helper method to get coordinates in the globe's coordinate system
    getCoords(lat, lng, alt) {
        return this.world.getCoords(lat, lng, alt);
    }

    // Method to customize object appearance
    setObjectThreeObject(callback) {
        this.world.objectThreeObject(callback);
        return this;
    }

    // Method to customize object labels
    setObjectLabel(callback) {
        this.world.objectLabel(callback);
        return this;
    }

    // Method to set click handler
    setObjectClickHandler(callback) {
        this.world.onObjectClick(callback);
        return this;
    }
} 