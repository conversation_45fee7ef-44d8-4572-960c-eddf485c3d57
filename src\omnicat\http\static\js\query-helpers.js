import { eciToLatLngAlt, EARTH_RADIUS_KM } from './globe-visualizer.js';

export async function fetchObjectData(objectId) {
    try {
        const objectResponse = await fetch(`/objects/${objectId}`);
        if (objectResponse.ok) {
            return await objectResponse.json();
        }
    } catch (error) {
        console.warn('Error fetching object data:', error);
    }
    return null;
}

export async function fetchMultipleObjects(objectIds) {
    if (!objectIds || objectIds.length === 0) {
        return {};
    }
    
    try {
        // Remove duplicates and filter out null/undefined values
        const uniqueObjectIds = [...new Set(objectIds.filter(id => id))];
        
        if (uniqueObjectIds.length === 0) {
            return {};
        }
        
        // Use comma-separated list for multiple objects
        const idsParam = uniqueObjectIds.join(',');
        const objectResponse = await fetch(`/objects/${idsParam}`);
        
        if (objectResponse.ok) {
            const responseData = await objectResponse.json();
            
            // Handle both single object response and array response
            const objects = Array.isArray(responseData) ? responseData : [responseData];
            
            // Create a mapping of object_id to object data
            const objectMap = {};
            objects.forEach(objResponse => {
                if (objResponse && objResponse.object) {
                    objectMap[objResponse.object_id] = objResponse;
                }
            });
            
            return objectMap;
        }
    } catch (error) {
        console.warn('Error fetching multiple objects:', error);
    }
    return {};
}

export function buildQueryParams(results, filters = {}) {
    const params = {
        radius: results.radius,
        time_start: results.startTime,
        time_end: results.endTime,
        format: 'tle',
        page: results.page,
        page_size: results.pageSize
    };

    const queryParams = new URLSearchParams(params);
    queryParams.append('center', results.latitude);
    queryParams.append('center', results.longitude);
    queryParams.append('center', results.altitude);
    
    // Add filter parameters if they exist
    if (filters) {
        // Add sources filter
        if (filters.sources && filters.sources.length > 0) {
            filters.sources.forEach(source => {
                queryParams.append('sources', source);
            });
        }
        
        // Add object_types filter
        if (filters.object_types && filters.object_types.length > 0) {
            filters.object_types.forEach(objectType => {
                queryParams.append('object_types', objectType);
            });
        }
        
        // Add country_codes filter
        if (filters.country_codes && filters.country_codes.length > 0) {
            filters.country_codes.forEach(countryCode => {
                queryParams.append('country_codes', countryCode);
            });
        }
        
        // Add allegiances filter
        if (filters.allegiances && filters.allegiances.length > 0) {
            filters.allegiances.forEach(allegiance => {
                queryParams.append('allegiances', allegiance);
            });
        }
        
        // Add correlation_statuses filter
        if (filters.correlation_statuses && filters.correlation_statuses.length > 0) {
            filters.correlation_statuses.forEach(status => {
                queryParams.append('correlation_statuses', status);
            });
        }
    }
    
    return queryParams;
}

export function createQuerySphereObject(results, earthRadiusKm) {
    return [{
        lat: results.latitude,
        lng: results.longitude,
        alt: results.altitude / earthRadiusKm,
        radius: results.radius,
        startTime: results.startTime,
        endTime: results.endTime,
        page: results.page,
        pageSize: results.pageSize,
        isQuerySphere: true
    }];
}

export function createQuerySphereLabel(d) {
    return `
        <div style="background: rgba(0,0,0,0.7); color: white; padding: 5px; border-radius: 3px;">
            <b>Query Parameters</b><br/>
            Lat: ${d.lat.toFixed(2)}°<br/>
            Lon: ${d.lng.toFixed(2)}°<br/>
            Alt: ${(d.alt * EARTH_RADIUS_KM).toFixed(2)} km<br/>
            Radius: ${d.radius.toFixed(2)} km<br/>
            Start: ${d.startTime}<br/>
            End: ${d.endTime}<br/>
            Page: ${d.page}<br/>
            Page Size: ${d.pageSize}
        </div>
    `;
}

export function getFormResults() {
    return {
        latitude: parseFloat(document.getElementById('lat').value),
        longitude: parseFloat(document.getElementById('lng').value),
        altitude: parseFloat(document.getElementById('alt').value),
        radius: parseFloat(document.getElementById('radius').value),
        startTime: document.getElementById('startTime').value + ':00Z',
        endTime: document.getElementById('endTime').value + ':00Z',
        page: parseInt(document.getElementById('page').value),
        pageSize: parseInt(document.getElementById('pageSize').value)
    };
}

export async function processTrackData(track, trackId, startTime, endTime, objectData) {
    const satrec = satellite.twoline2satrec(track.data.line1, track.data.line2);
    const numPoints = 100;
    const timeStep = (new Date(endTime) - new Date(startTime)) / (numPoints - 1);
    const points = [];

    for (let i = 0; i < numPoints; i++) {
        const time = new Date(new Date(startTime).getTime() + (i * timeStep));
        const gmst = satellite.gstime(time);
        const eci = satellite.propagate(satrec, time);

        if (!eci.position) continue;

        const { lat, lng, alt } = eciToLatLngAlt(eci.position, gmst);
        points.push({ lat, lng, alt });
    }

    // Create trackSphere using the last point
    const lastPoint = points[points.length - 1];
    const trackSphere = lastPoint ? {
        lat: lastPoint.lat,
        lng: lastPoint.lng,
        alt: lastPoint.alt,
        isTrackSphere: true,
        trackId: trackId,
        source: track.source,
        format: track.format,
        data: track.data,
        metadata: track.metadata,
        objectData: objectData,
        correlationStatus: objectData ? 'Correlated' : 'Uncorrelated Track'
    } : null;

    return { points, trackSphere };
}

export async function processTrackResponse(trackResponse, startTime, endTime, objectDataMap = null) {
    const track = trackResponse.track;
    if (track.format !== 'tle') {
        console.warn('Unexpected track format:', track.format);
        return null;
    }

    try {
        let objectData = null;
        if (track.object_correlations?.length > 0) {
            const objectId = track.object_correlations[0].object_id;
            if (objectDataMap && objectId) {
                // Use pre-fetched object data if available
                objectData = objectDataMap[objectId];
            } else {
                // Fall back to individual fetch if no bulk data provided
                objectData = await fetchObjectData(objectId);
            }
        }

        const { points, trackSphere } = await processTrackData(
            track,
            trackResponse.track_id,
            startTime,
            endTime,
            objectData
        );

        return { points, trackSphere, objectData };
    } catch (error) {
        console.warn('Error processing track:', trackResponse.track_id, error);
        return null;
    }
}

export async function processAllTrackResponses(trackResponses, startTime, endTime) {
    // Collect all object IDs from all track responses
    const objectIds = [];
    trackResponses.forEach(trackResponse => {
        if (trackResponse.track.object_correlations?.length > 0) {
            objectIds.push(trackResponse.track.object_correlations[0].object_id);
        }
    });

    // Fetch all objects at once
    const objectDataMap = await fetchMultipleObjects(objectIds);

    // Process all tracks with the pre-fetched object data
    const results = [];
    for (const trackResponse of trackResponses) {
        const result = await processTrackResponse(trackResponse, startTime, endTime, objectDataMap);
        if (result) {
            results.push(result);
        }
    }

    return results;
}

export async function fetchTrackData(trackId, objectDataMap = null) {
    const params = new URLSearchParams({
        format: 'tle',
    });

    const response = await fetch(`/tracks/${trackId}?${params}`);
    const data = await response.json();

    // If track has object correlations, fetch the object data
    if (data.track.object_correlations?.length > 0) {
        const objectId = data.track.object_correlations[0].object_id;
        if (objectId) {
            if (objectDataMap && objectDataMap[objectId]) {
                // Use pre-fetched object data
                data.track.object_correlations[0].object = objectDataMap[objectId].object;
            } else {
                // Fall back to individual fetch
                const objectResponse = await fetch(`/objects/${objectId}`);
                const objectData = await objectResponse.json();
                data.track.object_correlations[0].object = objectData.object;
            }
        }
    }

    return {
        track: data.track,
        trackId: data.track_id
    };
}

export async function getTrackPointsAroundTime(track, targetTime, queryStartTime, queryEndTime, numPoints = 10) {
    const startTimeUtc = new Date(queryStartTime);
    const endTimeUtc = new Date(queryEndTime);
    const totalTimeWindow = endTimeUtc - startTimeUtc;
    const timeWindow = totalTimeWindow / 100; // 1/100th of the total query window
    const points = [];

    try {
        if (!track || !track.data || !track.data.line1 || !track.data.line2) {
            return points;
        }

        const satrec = satellite.twoline2satrec(track.data.line1, track.data.line2);
        if (!satrec) {
            return points;
        }

        const startTime = new Date(targetTime.getTime() - timeWindow * (numPoints - 1));

        for (let i = 0; i < numPoints; i++) {
            const time = new Date(startTime.getTime() + timeWindow * i);
            const gmst = satellite.gstime(time);
            const eci = satellite.propagate(satrec, time);

            if (!eci.position) continue;

            const geodetic = satellite.eciToGeodetic(eci.position, gmst);
            points.push({
                lat: satellite.radiansToDegrees(geodetic.latitude),
                lng: satellite.radiansToDegrees(geodetic.longitude),
                alt: geodetic.height / EARTH_RADIUS_KM,
                time: time.toISOString(),
                eci: eci.position
            });
        }
    } catch (error) {
        console.error('Error calculating track points:', error);
        return points;
    }

    return points;
}

export function createConjunctionSphereData(points, track, trackId, conjunction, otherTrackId) {
    const lastPoint = points[points.length - 1];
    const objectData = track.object_correlations?.[0];

    return {
        lat: lastPoint.lat,
        lng: lastPoint.lng,
        alt: lastPoint.alt,
        isTrackSphere: true,
        trackId: trackId,
        source: track.source,
        format: track.format,
        track: track,
        objectData: objectData,
        correlationStatus: track.object_correlations?.length > 0 ? 'Correlated' : 'Uncorrelated Track',
        conjunctionInfo: {
            time: conjunction.time_of_closest_approach,
            distance: conjunction.closest_approach_distance,
            otherTrackId: otherTrackId
        }
    };
}

export async function processConjunction(conjunction, params) {
    const conjunctionTime = new Date(conjunction.time_of_closest_approach);

    // Fetch track data for both tracks
    const [trackA, trackB] = await Promise.all([
        fetchTrackData(conjunction.track_a_id),
        fetchTrackData(conjunction.track_b_id)
    ]);

    // Collect object IDs from both tracks for bulk fetching
    const objectIds = [];
    if (trackA.track.object_correlations?.length > 0) {
        objectIds.push(trackA.track.object_correlations[0].object_id);
    }
    if (trackB.track.object_correlations?.length > 0) {
        objectIds.push(trackB.track.object_correlations[0].object_id);
    }

    // Fetch all objects at once if there are any to fetch
    const objectDataMap = await fetchMultipleObjects(objectIds);

    // Update track data with fetched objects
    if (trackA.track.object_correlations?.length > 0) {
        const objectId = trackA.track.object_correlations[0].object_id;
        if (objectDataMap[objectId]) {
            trackA.track.object_correlations[0].object = objectDataMap[objectId].object;
        }
    }
    if (trackB.track.object_correlations?.length > 0) {
        const objectId = trackB.track.object_correlations[0].object_id;
        if (objectDataMap[objectId]) {
            trackB.track.object_correlations[0].object = objectDataMap[objectId].object;
        }
    }

    // Get points for both tracks
    const [trackAPoints, trackBPoints] = await Promise.all([
        getTrackPointsAroundTime(trackA.track, conjunctionTime, params.get('time_start'), params.get('time_end'), 10),
        getTrackPointsAroundTime(trackB.track, conjunctionTime, params.get('time_start'), params.get('time_end'), 10)
    ]);

    if (trackAPoints.length === 0 || trackBPoints.length === 0) return [];

    return [
        { points: trackAPoints, track: trackA.track, trackId: trackA.trackId },
        { points: trackBPoints, track: trackB.track, trackId: trackB.trackId }
    ];
} 