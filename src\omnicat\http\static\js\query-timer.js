export class QueryTimer {
    constructor(elementId) {
        this.elementId = elementId;
        this.startTime = null;
        this.timerInterval = null;
    }

    start() {
        this.startTime = Date.now();
        if (this.timerInterval) clearInterval(this.timerInterval);

        this.timerInterval = setInterval(() => this.updateDisplay(), 100);
        this.updateElement('Starting query...');
    }

    stop(success = true) {
        if (!this.startTime) return;

        const duration = ((Date.now() - this.startTime) / 1000).toFixed(1);
        clearInterval(this.timerInterval);

        const message = success ?
            `Query completed in ${duration}s` :
            'Query failed';

        this.updateElement(message);

        this.startTime = null;
        this.timerInterval = null;
    }

    updateDisplay() {
        if (!this.startTime) return;

        const elapsed = Date.now() - this.startTime;
        const seconds = (elapsed / 1000).toFixed(1);
        this.updateElement(`Query in progress...<br>Time elapsed: ${seconds}s`);
    }

    updateElement(text) {
        const element = document.getElementById(this.elementId);
        if (element) element.innerHTML = text;
    }
} 