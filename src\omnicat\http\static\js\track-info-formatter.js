export function formatObjectInfo(objectData) {
    if (!objectData?.object) return '';

    const objectJson = JSON.stringify(objectData.object, null, 2)
        .replace(/\n/g, '<br/>')
        .replace(/ /g, '&nbsp;');

    return `
        <hr style="margin: 5px 0;">
        <b>Correlated Object Information</b><br/>
        ${objectJson}
    `;
}

export function formatTrackData(data, metadata) {
    const trackDataEntries = Object.entries(data || {})
        .map(([key, value]) => `${key}: ${value}`).join('<br/>');
    const trackMetadataJson = JSON.stringify(metadata || {}, null, 2)
        .replace(/\n/g, '<br/>')
        .replace(/ /g, '&nbsp;');

    return { trackDataEntries, trackMetadataJson };
}

export function createTrackInfoContent(obj) {
    // Format track data and metadata
    const trackDataEntries = Object.entries(obj.data || {})
        .map(([key, value]) => `${key}: ${value}`).join('<br/>');
    const trackMetadataJson = JSON.stringify(obj.metadata || {}, null, 2)
        .replace(/\n/g, '<br/>')
        .replace(/ /g, '&nbsp;');

    // Format object data if it exists
    let objectInfo = '';
    if (obj.objectData?.object) {
        const objectJson = JSON.stringify(obj.objectData.object, null, 2)
            .replace(/\n/g, '<br/>')
            .replace(/ /g, '&nbsp;');
        objectInfo = `
            <hr style="margin: 5px 0;">
            <b>Correlated Object Information</b><br/>
            ${objectJson}
        `;
    }

    return `
        <b>Track Information</b><br/>
        Track ID: ${obj.trackId}<br/>
        Source: ${obj.source || 'N/A'}<br/>
        Format: ${obj.format}<br/>
        Status: ${obj.correlationStatus}<br/>
        <hr style="margin: 5px 0;">
        <b>Track Data</b><br/>
        ${trackDataEntries}
        <hr style="margin: 5px 0;">
        <b>Track Metadata</b><br/>
        ${trackMetadataJson}
        ${objectInfo}
    `;
}

export function createTrackSphereLabel(d) {
    return `
        <div style="background: rgba(0,0,0,0.7); color: white; padding: 5px; border-radius: 3px; max-width: 600px; font-family: monospace;">
            ${createTrackInfoContent(d)}
        </div>
    `;
}

export function formatConjunctionLabel(trackData) {
    // Format track data and metadata
    const trackDataEntries = Object.entries(trackData.track.data || {})
        .map(([key, value]) => `${key}: ${value}`).join('<br/>');
    const trackMetadataJson = JSON.stringify(trackData.track.metadata || {}, null, 2)
        .replace(/\n/g, '<br/>')
        .replace(/ /g, '&nbsp;');

    // Format object data if it exists
    let objectInfo = '';
    if (trackData.objectData?.object) {
        const objectJson = JSON.stringify(trackData.objectData.object, null, 2)
            .replace(/\n/g, '<br/>')
            .replace(/ /g, '&nbsp;');
        objectInfo = `
            <hr style="margin: 5px 0;">
            <b>Correlated Object Information</b><br/>
            ${objectJson}
        `;
    }

    // Add conjunction information
    const conjunctionInfo = trackData.conjunctionInfo ? `
        <hr style="margin: 5px 0;">
        <b>Conjunction Information</b><br/>
        Time (UTC): ${trackData.conjunctionInfo.time}<br/>
        Distance: ${trackData.conjunctionInfo.distance.toFixed(2)} km<br/>
        Other Track: ${trackData.conjunctionInfo.otherTrackId}
    ` : '';

    return `
        <div style="background: rgba(0,0,0,0.7); color: white; padding: 5px; border-radius: 3px; max-width: 600px; font-family: monospace;">
            <b>Track Information</b><br/>
            Track ID: ${trackData.trackId}<br/>
            Source: ${trackData.source || 'N/A'}<br/>
            Format: ${trackData.format}<br/>
            Status: ${trackData.correlationStatus}<br/>
            <hr style="margin: 5px 0;">
            <b>Track Data</b><br/>
            ${trackDataEntries}
            <hr style="margin: 5px 0;">
            <b>Track Metadata</b><br/>
            ${trackMetadataJson}
            ${objectInfo}
            ${conjunctionInfo}
        </div>
    `;
} 