<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AOI Query GUI</title>
    <link rel="stylesheet" href="/static/css/globe-gui.css">
    <link rel="stylesheet" href="/static/css/filter-gui.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/satellite.js/4.0.0/satellite.min.js"></script>
    <script src="//unpkg.com/globe.gl"></script>
    <script src="/static/js/globe-gui-common.js"></script>
</head>

<body>
    <div id="loading" class="loading-spinner">
        <div class="spinner"></div>
        <p>Loading globe...</p>
    </div>

    <div id="persistentLabel" class="persistent-label">
        <button class="close-button" onclick="closePersistentLabel()">×</button>
        <div id="persistentLabelContent"></div>
    </div>

    <div class="query-form">
        <form id="queryForm" onsubmit="handleSubmit(event)">
            <label>Latitude (deg): <input type="text" id="lat" step="any" pattern="-?\d*\.?\d*" required></label>
            <label>Longitude (deg): <input type="text" id="lng" step="any" pattern="-?\d*\.?\d*" required></label>
            <label>Altitude (km): <input type="text" id="alt" step="any" pattern="\d*\.?\d*" required value="1500"></label>
            <label>Radius (km): <input type="text" id="radius" step="any" pattern="\d*\.?\d*" required value="1500"></label>
            <label>Start Time (UTC): <input type="datetime-local" id="startTime" required></label>
            <label>End Time (UTC): <input type="datetime-local" id="endTime" required></label>
            <label>Page: <input type="text" id="page" pattern="\d*" value="0"></label>
            <label>Page Size: <input type="text" id="pageSize" pattern="\d*" value="10"></label>
            <button type="submit">Query</button>
        </form>
    </div>

    <div class="filter-box">
        <h3>Filter Options</h3>
        
        <div class="filter-section">
            <h4>Sources</h4>
            <div class="filter-input-container">
                <input type="text" id="sourceInput" class="filter-input" placeholder="Type to search sources">
                <div id="sourceDropdown" class="dropdown-content"></div>
            </div>
            <div id="selectedSources" class="selected-filters"></div>
        </div>
        
        <div class="filter-section">
            <h4>Object Types</h4>
            <div class="filter-input-container">
                <input type="text" id="objectTypeInput" class="filter-input" placeholder="Type to search object types">
                <div id="objectTypeDropdown" class="dropdown-content"></div>
            </div>
            <div id="selectedObjectTypes" class="selected-filters"></div>
        </div>
        
        <div class="filter-section">
            <h4>Country Codes</h4>
            <div class="filter-input-container">
                <input type="text" id="countryCodeInput" class="filter-input" placeholder="Type to search country codes">
                <div id="countryCodeDropdown" class="dropdown-content"></div>
            </div>
            <div id="selectedCountryCodes" class="selected-filters"></div>
        </div>
        
        <div class="filter-section">
            <h4>Allegiances</h4>
            <div class="filter-input-container">
                <input type="text" id="allegianceInput" class="filter-input" placeholder="Type to search allegiances">
                <div id="allegianceDropdown" class="dropdown-content"></div>
            </div>
            <div id="selectedAllegiances" class="selected-filters"></div>
        </div>
        
        <div class="filter-section">
            <h4>Correlation Status</h4>
            <div class="filter-input-container">
                <input type="text" id="correlationStatusInput" class="filter-input" placeholder="Type to search correlation status">
                <div id="correlationStatusDropdown" class="dropdown-content">
                    <div class="dropdown-item" onclick="addFilter('correlation_statuses', 'Correlated')">Correlated</div>
                    <div class="dropdown-item" onclick="addFilter('correlation_statuses', 'Uncorrelated')">Uncorrelated</div>
                </div>
            </div>
            <div id="selectedCorrelationStatuses" class="selected-filters"></div>
        </div>
    </div>

    <div id="markerInfo"></div>
    <div id="queryTiming">No queries executed yet</div>
    <div id="globeViz"></div>

    <script type="module">
        import { GlobeVisualizer, EARTH_RADIUS_KM } from '/static/js/globe-visualizer.js';
        import { QueryTimer } from '/static/js/query-timer.js';
        import { createTrackInfoContent, createTrackSphereLabel } from '/static/js/track-info-formatter.js';
        import {
            getFormResults,
            buildQueryParams,
            createQuerySphereObject,
            createQuerySphereLabel,
            processTrackResponse,
            processAllTrackResponses
        } from '/static/js/query-helpers.js';
        import * as THREE from 'https://cdn.jsdelivr.net/npm/three@0.158.0/build/three.module.js';

        const queryTimer = new QueryTimer('queryTiming');

        let markers = [];
        let filterOptions = {
            sources: [],
            object_types: [],
            country_codes: [],
            allegiances: []
        };
        
        let selectedFilters = {
            sources: [],
            object_types: [],
            country_codes: [],
            allegiances: [],
            correlation_statuses: []
        };

        window.addEventListener('load', () => {
            setDefaultTimes();
            fetchFilterOptions();
            setupFilterDropdowns();
        });
        
        async function fetchFilterOptions() {
            try {
                const response = await fetch('/tracks/filter-options');
                if (!response.ok) {
                    throw new Error(`Failed to fetch filter options: ${response.status} ${response.statusText}`);
                }
                filterOptions = await response.json();
                console.log('Loaded filter options:', filterOptions);
            } catch (error) {
                console.error('Error fetching filter options:', error);
            }
        }
        
        function setupFilterDropdowns() {
            setupFilterInput('source', 'sources');
            setupFilterInput('objectType', 'object_types');
            setupFilterInput('countryCode', 'country_codes');
            setupFilterInput('allegiance', 'allegiances');
            
            // Setup correlation status dropdown separately since it has static options
            const correlationInput = document.getElementById('correlationStatusInput');
            const correlationDropdown = document.getElementById('correlationStatusDropdown');
            
            correlationInput.addEventListener('focus', () => {
                correlationDropdown.classList.add('show');
            });
            
            correlationInput.addEventListener('input', () => {
                const query = correlationInput.value.toLowerCase();
                
                // Filter the static options
                const options = correlationDropdown.querySelectorAll('.dropdown-item');
                let hasVisibleItems = false;
                
                options.forEach(option => {
                    const text = option.textContent.toLowerCase();
                    if (text.includes(query) && !selectedFilters.correlation_statuses.includes(option.textContent)) {
                        option.style.display = 'block';
                        hasVisibleItems = true;
                    } else {
                        option.style.display = 'none';
                    }
                });
                
                correlationDropdown.classList.toggle('show', hasVisibleItems);
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', (event) => {
                if (!event.target.closest('#correlationStatusInput') && !event.target.closest('#correlationStatusDropdown')) {
                    correlationDropdown.classList.remove('show');
                }
            });
        }
        
        function setupFilterInput(inputPrefix, optionsKey) {
            const inputId = `${inputPrefix}Input`;
            const dropdownId = `${inputPrefix}Dropdown`;
            const selectedContainerId = `selected${inputPrefix.charAt(0).toUpperCase() + inputPrefix.slice(1)}s`;
            
            const input = document.getElementById(inputId);
            const dropdown = document.getElementById(dropdownId);
            
            input.addEventListener('input', () => {
                updateDropdown(input.value, optionsKey, dropdown);
            });
            
            input.addEventListener('focus', () => {
                updateDropdown(input.value, optionsKey, dropdown);
            });
            
            // Close dropdown when clicking outside the input or dropdown
            document.addEventListener('click', (event) => {
                if (!event.target.closest(`#${inputId}`) && !event.target.closest(`#${dropdownId}`)) {
                    dropdown.classList.remove('show');
                }
            });
        }
        
        function updateDropdown(query, optionsKey, dropdown) {
            const lowercaseQuery = query.toLowerCase();
            const matchingOptions = filterOptions[optionsKey]
                .filter(option => option.toLowerCase().includes(lowercaseQuery))
                .filter(option => !selectedFilters[optionsKey].includes(option));
            
            dropdown.innerHTML = '';
            
            if (matchingOptions.length > 0) {
                matchingOptions.forEach(option => {
                    const item = document.createElement('div');
                    item.classList.add('dropdown-item');
                    item.textContent = option;
                    item.addEventListener('click', () => {
                        addFilter(optionsKey, option);
                        dropdown.classList.remove('show');
                        document.getElementById(dropdown.id.replace('Dropdown', 'Input')).value = '';
                    });
                    dropdown.appendChild(item);
                });
                dropdown.classList.add('show');
            } else {
                dropdown.classList.remove('show');
            }
        }
        
        window.addFilter = function(category, value) {
            if (!selectedFilters[category].includes(value)) {
                selectedFilters[category].push(value);
                updateSelectedFiltersDisplay(category);
            }
        };
        
        window.removeFilter = function(category, value) {
            const index = selectedFilters[category].indexOf(value);
            if (index > -1) {
                selectedFilters[category].splice(index, 1);
                updateSelectedFiltersDisplay(category);
            }
        };
        
        function updateSelectedFiltersDisplay(category) {
            const containerMapping = {
                'sources': 'selectedSources',
                'object_types': 'selectedObjectTypes',
                'country_codes': 'selectedCountryCodes',
                'allegiances': 'selectedAllegiances',
                'correlation_statuses': 'selectedCorrelationStatuses'
            };
            
            const container = document.getElementById(containerMapping[category]);
            container.innerHTML = '';
            
            selectedFilters[category].forEach(value => {
                const tag = document.createElement('div');
                tag.classList.add('filter-tag');
                tag.innerHTML = `
                    ${value}
                    <span class="filter-tag-remove" onclick="removeFilterTag('${category}', '${value}')">&times;</span>
                `;
                container.appendChild(tag);
            });
        }
        
        window.removeFilterTag = function(category, value) {
            removeFilter(category, value);
        };

        const markerSvg = `<svg viewBox="-4 0 36 36">
            <path fill="currentColor" d="M14,0 C21.732,0 28,5.641 28,12.6 C28,23.963 14,36 14,36 C14,36 0,24.064 0,12.6 C0,5.641 6.268,0 14,0 Z"></path>
            <circle fill="black" cx="14" cy="14" r="7"></circle>
        </svg>`;

        const globeViz = new GlobeVisualizer('globeViz');

        globeViz.init().then(() => {
            // Hide loading spinner once globe is ready
            document.getElementById('loading').style.display = 'none';

            globeViz.setObjectThreeObject(d => {
                if (d.isQuerySphere) {
                    const sphereGeometry = new THREE.SphereGeometry(d.radius * globeViz.world.getGlobeRadius() / EARTH_RADIUS_KM, 32, 32);
                    const sphereMaterial = new THREE.MeshLambertMaterial({
                        color: 'palegreen',
                        transparent: true,
                        opacity: 0.2
                    });
                    return new THREE.Mesh(sphereGeometry, sphereMaterial);
                } else if (d.isTrackSphere) {
                    const sphereGeometry = new THREE.SphereGeometry(2, 16, 16);
                    const sphereMaterial = new THREE.MeshLambertMaterial({
                        color: getAllegianceColor(d),
                        transparent: true,
                        opacity: 0.6
                    });
                    return new THREE.Mesh(sphereGeometry, sphereMaterial);
                }
                return null;
            });

            globeViz.world.htmlElementsData(markers)
                .htmlElement(d => {
                    const el = document.createElement('div');
                    el.innerHTML = markerSvg;
                    el.style.color = d.color;
                    el.style.width = '20px';
                    el.style['pointer-events'] = 'auto';
                    el.style.cursor = 'pointer';
                    el.onclick = () => {
                        document.getElementById('markerInfo').style.display = 'block';
                        document.getElementById('markerInfo').innerHTML = `
                            <strong>Marker Location</strong><br>
                            Lat: ${d.lat.toFixed(2)}°<br>
                            Lon: ${d.lng.toFixed(2)}°
                        `;
                    };
                    return el;
                });

            globeViz.world.onPolygonClick((polygon, event, coords) => {
                const { lat, lng } = coords;
                const { properties: d } = polygon;

                // Replace any existing marker
                markers = [{
                    lat: lat,
                    lng: lng,
                    color: 'red'
                }];
                globeViz.world.htmlElementsData(markers);

                // Update info box
                const markerInfo = document.getElementById('markerInfo');
                markerInfo.style.display = 'block';
                markerInfo.innerHTML = `
                    <strong>${d.ADMIN}</strong><br>
                    Lat: ${lat.toFixed(2)}°<br>
                    Lon: ${lng.toFixed(2)}°
                `;

                // Populate form inputs
                document.getElementById('lat').value = lat.toFixed(2);
                document.getElementById('lng').value = lng.toFixed(2);
            });

            globeViz.world.onGlobeClick(({ lat, lng }) => {
                // Replace any existing marker
                markers = [{
                    lat: lat,
                    lng: lng,
                    color: 'blue'
                }];
                globeViz.world.htmlElementsData(markers);

                // Update info box
                const markerInfo = document.getElementById('markerInfo');
                markerInfo.style.display = 'block';
                markerInfo.innerHTML = `
                    <strong>Clicked Location</strong><br>
                    Lat: ${lat.toFixed(2)}°<br>
                    Lon: ${lng.toFixed(2)}°
                `;

                // Populate form inputs
                document.getElementById('lat').value = lat.toFixed(2);
                document.getElementById('lng').value = lng.toFixed(2);
            });

            globeViz.setObjectLabel(d => {
                if (d.isQuerySphere) {
                    return createQuerySphereLabel(d);
                } else if (d.isTrackSphere) {
                    return createTrackSphereLabel(d);
                }
                return null;
            });

            // Modify the click listener to check for form clicks too
            document.addEventListener('click', (event) => {
                // Close marker info if clicking outside globe and form
                if (!event.target.closest('#globeViz') && !event.target.closest('#queryForm')) {
                    document.getElementById('markerInfo').style.display = 'none';
                }

                // Close persistent label if clicking outside the label itself
                if (!event.target.closest('#persistentLabel')) {
                    document.getElementById('persistentLabel').style.display = 'none';
                }
            });

            window.handleSubmit = async function (event) {
                event.preventDefault();

                document.getElementById('loading').style.display = 'block';
                document.getElementById('loading').querySelector('p').textContent = 'Querying tracks...';
                globeViz.clearPaths();

                const results = getFormResults();
                
                const queryParams = buildQueryParams(results, selectedFilters);
                const querySphere = createQuerySphereObject(results, EARTH_RADIUS_KM);
                queryTimer.start();

                try {
                    const response = await fetch(`/tracks/query?${queryParams}`);
                    const data = await response.json();
                    queryTimer.stop(true);

                    const trackSpheres = [];

                    // Use bulk processing instead of individual track processing
                    const trackResults = await processAllTrackResponses(
                        data.tracks,
                        results.startTime,
                        results.endTime
                    );

                    for (const result of trackResults) {
                        const { points, trackSphere, objectData } = result;
                        if (trackSphere) trackSpheres.push(trackSphere);
                        globeViz.addPath(points, getAllegianceColor(trackSphere), 2);
                    }

                    globeViz.updateObjects([...querySphere, ...trackSpheres]);

                } catch (error) {
                    queryTimer.stop(false);
                    console.error('Error querying tracks:', error);
                    alert('Error querying tracks. Check console for details.');
                } finally {
                    document.getElementById('loading').style.display = 'none';
                }
            };

            // Add this after setting the object label
            globeViz.setObjectClickHandler(obj => {
                if (obj.isTrackSphere) {
                    showPersistentLabel(createTrackInfoContent(obj));
                }
            });
        });

        function getAllegianceColor(track) {
            // For uncorrelated tracks (no objectData), return Yellow
            if (!track.objectData) {
                return 'yellow';
            }
            
            // For correlated tracks, check the object property of objectData
            // The allegiance is in track.objectData.object, not directly in track.objectData
            if (track.objectData.object && track.objectData.object.allegiance) {
                // Just convert the allegiance name directly to lowercase
                return track.objectData.object.allegiance.toLowerCase();
            }
            
            return 'yellow';
        }
    </script>
</body>

</html>