<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conjunction Query GUI</title>
    <link rel="stylesheet" href="/static/css/globe-gui.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/satellite.js/4.0.0/satellite.min.js"></script>
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="//unpkg.com/globe.gl"></script>
    <script src="/static/js/globe-gui-common.js"></script>
</head>

<body>
    <div id="loading" class="loading-spinner">
        <div class="spinner"></div>
        <p>Loading globe...</p>
    </div>

    <div id="persistentLabel" class="persistent-label">
        <button class="close-button" onclick="closePersistentLabel()">×</button>
        <div id="persistentLabelContent"></div>
    </div>

    <div class="query-form">
        <form id="queryForm" onsubmit="handleSubmit(event)">
            <label>Start Time (UTC): <input type="datetime-local" id="startTime" required></label>
            <label>End Time (UTC): <input type="datetime-local" id="endTime" required></label>
            <label>Max Distance (km): <input type="number" id="maxDistance" step="any" required value="10"></label>
            <button type="submit">Query Conjunctions</button>
        </form>
    </div>

    <div id="queryTiming">No queries executed yet</div>
    <div id="globeViz"></div>

    <script type="module">
        import { GlobeVisualizer, EARTH_RADIUS_KM } from '/static/js/globe-visualizer.js';
        import { QueryTimer } from '/static/js/query-timer.js';
        import { formatConjunctionLabel } from '/static/js/track-info-formatter.js';
        import {
            fetchTrackData,
            getTrackPointsAroundTime,
            createConjunctionSphereData,
            processConjunction
        } from '/static/js/query-helpers.js';

        window.addEventListener('load', setDefaultTimes);

        const globeViz = new GlobeVisualizer('globeViz');

        globeViz.init().then(() => {
            // Hide loading spinner once globe is ready
            document.getElementById('loading').style.display = 'none';

            globeViz.setObjectThreeObject(d => {
                if (d.isTrackSphere) {
                    const sphereGeometry = new THREE.SphereGeometry(2, 16, 16);
                    const sphereMaterial = new THREE.MeshLambertMaterial({
                        color: d.objectData ? 'green' : 'red',
                        transparent: true,
                        opacity: 0.6
                    });
                    return new THREE.Mesh(sphereGeometry, sphereMaterial);
                }
                return null;
            });

            globeViz.setObjectLabel(d => {
                if (d.isTrackSphere) {
                    return formatConjunctionLabel(d);
                }
                return null;
            });

            globeViz.setObjectClickHandler(obj => {
                if (obj.isTrackSphere) {
                    const content = formatConjunctionLabel(obj).replace(/<div[^>]*>|<\/div>/g, '');
                    showPersistentLabel(content);
                }
            });

            document.addEventListener('click', (event) => {
                // Close persistent label if clicking outside the label itself
                if (!event.target.closest('#persistentLabel')) {
                    document.getElementById('persistentLabel').style.display = 'none';
                }
            });

            const queryTimer = new QueryTimer('queryTiming');

            window.handleSubmit = async function (event) {
                event.preventDefault();

                document.getElementById('loading').style.display = 'block';
                document.getElementById('loading').querySelector('p').textContent = 'Querying conjunctions...';
                globeViz.clearPaths();

                const params = new URLSearchParams({
                    time_start: document.getElementById('startTime').value + ':00Z',
                    time_end: document.getElementById('endTime').value + ':00Z',
                    max_distance: document.getElementById('maxDistance').value,
                    page: 1,
                    page_size: 1000
                });

                queryTimer.start();

                try {
                    const response = await fetch(`/conjunctions/query?${params}`);
                    const data = await response.json();
                    queryTimer.stop(true);

                    const trackSpheres = [];

                    for (const conjunction of data.conjunctions) {
                        const tracks = await processConjunction(conjunction, params);

                        for (const { points, track, trackId } of tracks) {
                            if (track.format !== 'tle') continue;

                            const otherTrackId = trackId === conjunction.track_a_id ?
                                conjunction.track_b_id : conjunction.track_a_id;

                            const sphereData = createConjunctionSphereData(
                                points, track, trackId, conjunction, otherTrackId
                            );

                            trackSpheres.push(sphereData);
                            globeViz.addPath(points, track.object_correlations?.length > 0 ? 'green' : 'red', 2);
                        }
                    }

                    globeViz.updateObjects(trackSpheres);
                } catch (error) {
                    queryTimer.stop(false);
                    console.error('Error querying conjunctions:', error);
                    alert('Error querying conjunctions. Check console for details.');
                } finally {
                    document.getElementById('loading').style.display = 'none';
                }
            };
        });
    </script>
</body>

</html>