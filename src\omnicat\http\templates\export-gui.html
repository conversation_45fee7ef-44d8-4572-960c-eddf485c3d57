<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export Data GUI</title>
    <link rel="stylesheet" href="/static/css/globe-gui.css">
    <script src="/static/js/globe-gui-common.js"></script>
    <style>
        /* Initially hide the loading spinner */
        #loading {
            display: none;
        }
    </style>
</head>

<body>
    <div id="loading" class="loading-spinner">
        <div class="spinner"></div>
        <p>Processing export...</p>
    </div>

    <div id="persistentLabel" class="persistent-label">
        <button class="close-button" onclick="closePersistentLabel()">×</button>
        <div id="persistentLabelContent"></div>
    </div>

    <div class="query-form">
        <h1>OmniCat Data Export</h1>
        <form id="exportForm" onsubmit="handleSubmit(event)">
            <label>Scenario Name: <input type="text" id="scenario" name="scenario" required value="default"></label>
            <label>Start Time (UTC): <input type="datetime-local" id="startTime" name="start_time" required></label>
            <label>Duration (minutes): <input type="number" id="minutes" name="minutes" step="1" min="1" required value="60"></label>
            <button type="submit">Export Data</button>
        </form>
    </div>

    <div id="exportStatus" class="status-container">No exports executed yet</div>

    <div class="image-container" style="text-align: center; margin-top: 20px;">
        <img src="/static/images/omnicat.webp" alt="OmniCat" style="max-width: 100%;">
    </div>

    <script>
        // Custom setDefaultTimes function for export GUI
        function setDefaultTimes() {
            const now = new Date();
            document.getElementById('startTime').value = now.toISOString().slice(0, 16);
        }

        window.addEventListener('load', setDefaultTimes);

        function showPersistentLabel(content) {
            document.getElementById('persistentLabelContent').innerHTML = content;
            document.getElementById('persistentLabel').style.display = 'block';
        }

        function closePersistentLabel() {
            document.getElementById('persistentLabel').style.display = 'none';
        }

        window.handleSubmit = async function (event) {
            event.preventDefault();
            showLoadingState();
            
            const formData = prepareFormData();
            
            try {
                const response = await fetchExportData(formData);
                await handleExportResponse(response);
            } catch (error) {
                handleExportError(error);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        };
        
        function prepareFormData() {
            const formData = new FormData();
            formData.append('scenario', document.getElementById('scenario').value);
            formData.append('start_time', document.getElementById('startTime').value + ':00Z');
            formData.append('minutes', document.getElementById('minutes').value);
            return formData;
        }
        
        function showLoadingState() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('exportStatus').textContent = 'Preparing export...';
        }
        
        async function fetchExportData(formData) {
            const response = await fetch('/export/generate', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw await createErrorFromResponse(response);
            }
            
            return response;
        }
        
        async function createErrorFromResponse(response) {
            let errorMessage = `Export failed with status: ${response.status}`;
            try {
                const errorData = await response.json();
                if (errorData && errorData.detail) {
                    errorMessage = `Export failed: ${errorData.detail}`;
                }
            } catch (e) {
                // Ignore parsing errors, use the default message
            }
            return new Error(errorMessage);
        }
        
        async function handleExportResponse(response) {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/octet-stream')) {
                await handleFileDownload(response);
            } else {
                await handleJsonResponse(response);
            }
        }
        
        async function handleFileDownload(response) {
            const filename = extractFilename(response.headers.get('content-disposition'));
            const blob = await response.blob();
            downloadBlob(blob, filename);
            document.getElementById('exportStatus').textContent = 
                `Export completed successfully. Downloaded file: ${filename}`;
        }
        
        function extractFilename(contentDisposition) {
            let filename = 'export.tar.gz';
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename="?([^"]*)"?/);
                if (filenameMatch && filenameMatch[1]) {
                    filename = filenameMatch[1];
                }
            }
            return filename;
        }
        
        function downloadBlob(blob, filename) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            a.remove();
        }
        
        async function handleJsonResponse(response) {
            const data = await response.json();
            document.getElementById('exportStatus').textContent = 
                `Export completed. ${data.message || 'Files available for download.'}`;
        }
        
        function handleExportError(error) {
            console.error('Error during export:', error);
            document.getElementById('exportStatus').textContent = 
                `Error during export: ${error.message}`;
        }
    </script>
</body>

</html> 