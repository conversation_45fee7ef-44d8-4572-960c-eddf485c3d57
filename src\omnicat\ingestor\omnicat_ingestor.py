from datetime import datetime, timedelta
from batchprop import PropagatedTleBatch

from omnicat.db import Neo4jDb, PostGisDb
from omnicat.models import Object, Track
from omnicat import processor, Settings, logger
from omnicat.utils import generate_timestamps


class OmnicatIngestor:
    def __init__(self, neo4j_db: Neo4jDb, postgis_db: PostGisDb, propagation_period_seconds: float = 300):
        self.neo4j_db = neo4j_db
        self.postgis_db = postgis_db
        self.propagation_period_seconds = propagation_period_seconds

    @staticmethod
    def from_settings(settings: Settings) -> "OmnicatIngestor":
        neo4j_db = Neo4jDb(**settings.neo4j.model_dump())
        postgis_db = PostGisDb.from_settings(settings.postgis)
        return OmnicatIngestor(neo4j_db, postgis_db, settings.propagation_period_seconds)

    def ingest_objects(self, objects: list[Object]) -> list[str]:
        object_ids = self.neo4j_db.add_objects(objects)
        logger.info(f"added {len(object_ids)} objects to neo4j")
        return object_ids

    def ingest_tracks(self, tracks: list[Track], window_start: datetime, window_end: datetime) -> list[str]:
        logger.info(
            f"ingesting {len(tracks)} tracks for time window {window_start} to {window_end}")

        track_ids = self.neo4j_db.add_tracks(tracks)
        logger.info(f"added {len(track_ids)} tracks to neo4j")

        timestamps = generate_timestamps(
            window_start, window_end, timedelta(seconds=self.propagation_period_seconds))

        propagated_states_ecef = get_propagated_ecef_states(
            tracks, timestamps)

        logger.info(
            f"adding {propagated_states_ecef.n_tles} propagated ecef tracks to postgis")
        self.postgis_db.insert_propagated_batch(
            propagated_states_ecef, track_ids)
            
        return track_ids

    def clear_stale_state_data(self, window_start: datetime, window_end: datetime) -> None:
        non_current_track_ids = self.neo4j_db.get_non_current_track_ids()
        logger.info(
            f"removing {len(non_current_track_ids)} non-current track ids from postgis")
        self.postgis_db.delete_tracks(non_current_track_ids)

        logger.info(
            f"deleting postgis data outside of time window {window_start} to {window_end}")
        self.postgis_db.delete_data_outside_time_window(
            window_start, window_end)


def get_propagated_ecef_states(tracks: list[Track], timestamps: list[datetime]) -> PropagatedTleBatch:
    tles = [track.data for track in tracks]

    logger.info(
        f"propagating {len(tles)} tles for {len(timestamps)} timestamps")
    prop_batch_eci = processor.propagate_tle_batch(tles, timestamps)

    logger.info(
        f"converting {prop_batch_eci.n_tles} propagated tracks to ecef")
    prop_batch_ecef = processor.eci_to_ecef_batch(prop_batch_eci)

    return prop_batch_ecef


def get_ingestion_time_window(now: datetime, window_days_back: int, window_days_forward: int) -> tuple[datetime, datetime]:
    window_start = now - \
        timedelta(days=window_days_back)
    window_end = now + \
        timedelta(days=window_days_forward)
    return window_start, window_end
