from omnicat.settings import get_settings
from omnicat.db import Neo4jDb
from omnicat.models import Track, TLE, Object, ObjectCorrelation
from omnicat.ingestor import UdlIngestor, get_ingestion_time_window
from omnicat import logger

import argparse
from datetime import datetime, timezone


def parse_args():
    parser = argparse.ArgumentParser(description='Ingest elset data from UDL')
    parser.add_argument('--max-correlated-elsets', type=int,
                        default=50,
                        help='Maximum number of correlated elsets to fetch')
    parser.add_argument('--max-uncorrelated-elsets', type=int,
                        default=50,
                        help='Maximum number of uncorrelated elsets to fetch')
    return parser.parse_args()


settings = get_settings()
args = parse_args()
print(f"running with settings:\n{settings}\nand args:\n{args}")

udl_ingestor = UdlIngestor.from_settings(settings)

window_start, window_end = get_ingestion_time_window(
    datetime.now(timezone.utc), settings.udl.ingestion_window_days_back, settings.udl.ingestion_window_days_forward)

logger.info(f"Fetching correlated elsets with window_start={window_start}, window_end={window_end}")
correlated_batch = udl_ingestor.ingest_elsets(
    window_start, window_end, args.max_correlated_elsets, uct=False)
logger.info(f"Found {len(correlated_batch) if correlated_batch else 0} correlated elsets")

logger.info(f"Fetching uncorrelated elsets with window_start={window_start}, window_end={window_end}")
uncorrelated_batch = udl_ingestor.ingest_elsets(
    window_start, window_end, args.max_uncorrelated_elsets, uct=True)
logger.info(f"Found {len(uncorrelated_batch) if uncorrelated_batch else 0} uncorrelated elsets")
