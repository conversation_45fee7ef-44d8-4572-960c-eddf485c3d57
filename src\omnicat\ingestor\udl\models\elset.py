from pydantic import BaseModel, Field, ConfigDict
from typing import Union, Annotated  # kept for Python <3.10 compatibility
from datetime import datetime
from enum import Enum

from omnicat.ingestor.udl.models.common import DataMode

# Field type aliases for reuse across models
IdElset = Annotated[str, Field(..., min_length=1, max_length=36,
                               description="Unique identifier of the record, auto-generated by the system")]
ClassificationMarking = Annotated[str, Field(..., min_length=1, max_length=128,
                                             description="Classification marking of the data in IC/CAPCO Portion-marked format")]
Line = Annotated[str | None, Field(
    None, max_length=69, description="Line of legacy TLE format")]
Origin = Annotated[str | None, Field(
    None, max_length=64, description="Originating system or organization which produced the data")]
Source = Annotated[str, Field(..., max_length=64,
                              min_length=1, description="Source of the data")]
OrigObjectId = Annotated[str | None, Field(
    None, max_length=64, description="Optional source-provided identifier for target onorbit object")]
Algorithm = Annotated[str | None, Field(
    None, max_length=64, description="Optional algorithm used to produce this record")]
OrigNetwork = Annotated[str | None, Field(
    None, max_length=32, description="Originating source network for this record")]


class Elset_Abridged(BaseModel):
    idElset: IdElset
    classificationMarking: ClassificationMarking
    satNo: int | None = Field(
        None,
        description="Satellite/catalog number of the target on-orbit object"
    )
    epoch: datetime = Field(
        ...,
        description="Elset epoch time in ISO 8601 UTC format, with microsecond precision"
    )
    meanMotion: float = Field(
        ...,
        description="Mean motion in revolutions per day"
    )
    idOnOrbit: str | None = Field(
        None,
        max_length=36,
        description="Unique identifier of the satellite on-orbit object, if correlated"
    )
    uct: bool = Field(
        False,
        description="Boolean indicating this Elset was unable to be correlated to a known object"
    )
    eccentricity: float = Field(
        ...,
        description="Orbital eccentricity determining deviation from perfect circle"
    )
    inclination: float = Field(
        ...,
        description="Angle between the equator and the orbit from Earth's center"
    )
    raan: float = Field(
        ...,
        description="Right ascension of the ascending node in degrees eastwards"
    )
    argOfPerigee: float = Field(
        ...,
        description="Angle in degrees between perigee and ascending node"
    )
    meanAnomaly: float = Field(
        ...,
        description="Position of satellite in orbital path, 0-360 degrees referenced to perigee"
    )
    revNo: int | None = Field(
        None,
        description="Current revolution number, incremented at ascending equator pass"
    )
    bStar: float = Field(
        ...,
        description="Drag term for SGP4 orbital model in inverse earth radii"
    )
    agom: float | None = Field(
        None,
        description="Area times solar radiation pressure coefficient over mass, in m^2/kg"
    )
    ballisticCoeff: float | None = Field(
        None,
        description="Ballistic coefficient in m^2/kg"
    )
    meanMotionDot: float | None = Field(
        None,
        description="1st derivative of mean motion in revolutions per day squared"
    )
    meanMotionDDot: float | None = Field(
        None,
        description="2nd derivative of mean motion in revolutions per day cubed"
    )
    semiMajorAxis: float | None = Field(
        None,
        description="Half the sum of periapsis and apoapsis distances in kilometers"
    )
    period: float | None = Field(
        None,
        description="Orbital period in minutes"
    )
    apogee: float | None = Field(
        None,
        description="Orbit point furthest from Earth's center in kilometers"
    )
    perigee: float | None = Field(
        None,
        description="Orbit point nearest to Earth's center in kilometers"
    )
    origObjectId: OrigObjectId
    idOrbitDetermination: str | None = Field(
        None,
        max_length=36,
        description="Unique identifier of the OD solution record"
    )
    line1: Line
    line2: Line
    descriptor: str | None = Field(
        None,
        max_length=64,
        description="Optional source-provided searchable metadata"
    )
    createdAt: datetime | None = Field(
        None,
        description="Time row was created in database"
    )
    createdBy: str | None = Field(
        None,
        max_length=64,
        description="Application user who created the database row"
    )
    origin: Origin
    sourceDL: str | None = Field(
        None,
        max_length=64,
        description="Source data library from which record was received"
    )
    source: Source
    dataMode: DataMode = Field(
        ...,
        description="Indicator of whether data is EXERCISE, REAL, SIMULATED, or TEST"
    )
    algorithm: Algorithm
    transactionId: str | None = Field(
        None,
        max_length=64,
        description="Optional identifier to track commercial transaction"
    )
    ephemType: int | None = Field(
        None,
        description="The ephemeris type associated with this TLE"
    )
    origNetwork: OrigNetwork

    model_config = ConfigDict(use_enum_values=True)


class SourcedDataType(str, Enum):
    EO = "EO"
    RADAR = "RADAR"
    RF = "RF"
    DOA = "DOA"
    ELSET = "ELSET"
    SV = "SV"


class Elset_Full(BaseModel):
    """
    An element set is a collection of Keplerian orbital elements describing an orbit 
    of a particular satellite. The data is used along with an orbit propagator in 
    order to predict the motion of a satellite. The element set, or elset for short, 
    consists of identification data, the classical elements and drag parameters.
    """
    idElset: IdElset
    classificationMarking: ClassificationMarking
    satNo: int | None = Field(
        None,
        description="Satellite/catalog number of the target on-orbit object."
    )
    epoch: datetime = Field(
        ...,
        description="Elset epoch time in ISO 8601 UTC format, with microsecond precision."
    )
    meanMotion: float | None = Field(
        None,
        description="Mean motion in revolutions per day."
    )
    idOnOrbit: str | None = Field(
        None,
        max_length=36,
        description="Unique identifier of the satellite on-orbit object, if correlated."
    )
    uct: bool | None = Field(
        None,
        description="Boolean indicating this Elset was unable to be correlated to a known object."
    )
    eccentricity: float | None = Field(
        None,
        description="Orbital eccentricity determining deviation from perfect circle."
    )
    inclination: float | None = Field(
        None,
        description="Angle between the equator and the orbit from Earth's center."
    )
    raan: float | None = Field(
        None,
        description="Right ascension of the ascending node in degrees eastwards."
    )
    argOfPerigee: float | None = Field(
        None,
        description="Angle in degrees between perigee and ascending node."
    )
    meanAnomaly: float | None = Field(
        None,
        description="Position of satellite in orbital path, 0-360 degrees referenced to perigee."
    )
    revNo: int | None = Field(
        None,
        description="Current revolution number, incremented at ascending equator pass."
    )
    bStar: float | None = Field(
        None,
        description="Drag term for SGP4 orbital model in inverse earth radii."
    )
    agom: float | None = Field(
        None,
        description="AGOM, expressed in m^2/kg, is the value of the (averaged) object Area times the solar radiation pressure coefficient(Gamma) over the object Mass."
    )
    ballisticCoeff: float | None = Field(
        None,
        description="Ballistic coefficient, in m^2/kg."
    )
    meanMotionDot: float | None = Field(
        None,
        description="1st derivative of mean motion in revolutions per day squared."
    )
    meanMotionDDot: float | None = Field(
        None,
        description="2nd derivative of mean motion in revolutions per day cubed."
    )
    semiMajorAxis: float | None = Field(
        None,
        description="Half the sum of periapsis and apoapsis distances in kilometers."
    )
    period: float | None = Field(
        None,
        description="Period of the orbit equal to inverse of mean motion, in minutes."
    )
    apogee: float | None = Field(
        None,
        description="Orbit point furthest from Earth's center in kilometers."
    )
    perigee: float | None = Field(
        None,
        description="Orbit point nearest to Earth's center in kilometers."
    )
    origObjectId: OrigObjectId
    idOrbitDetermination: str | None = Field(
        None,
        max_length=36,
        description="Unique identifier of the OD solution record that produced this elset."
    )
    effectiveFrom: datetime | None = Field(
        None,
        description="Read-only start time at which this elset was the 'current' elset for its satellite."
    )
    effectiveUntil: datetime | None = Field(
        None,
        description="Read-only end time at which this elset was no longer the 'current' elset for its satellite."
    )
    line1: Line
    line2: Line
    descriptor: str | None = Field(
        None,
        max_length=64,
        description="Optional source-provided and searchable metadata or descriptor of the data."
    )
    createdAt: datetime | None = Field(
        None,
        description="Time the row was created in the database, auto-populated by the system."
    )
    createdBy: str | None = Field(
        None,
        max_length=64,
        description="Application user who created the row in the database, auto-populated by the system."
    )
    rawFileURI: str | None = Field(
        None,
        max_length=256,
        description="Optional URI location in the document repository of the raw file parsed by the system to produce this record."
    )
    origin: Origin
    sourceDL: str | None = Field(
        None,
        max_length=64,
        description="The source data library from which this record was received."
    )
    source: Source
    dataMode: DataMode = Field(
        ...,
        description="Indicator of whether data is EXERCISE, REAL, SIMULATED, or TEST."
    )
    tags: list[str] | None = Field(
        None,
        description="Optional array of provider/source specific tags for this data."
    )
    algorithm: Algorithm
    sourcedData: list[str] | None = Field(
        None,
        description="Optional array of UDL data (observation) UUIDs used to build this element set."
    )
    sourcedDataTypes: list[SourcedDataType] | None = Field(
        None,
        description="Optional array of UDL observation data types used to build this element set."
    )
    transactionId: str | None = Field(
        None,
        max_length=64,
        description="Optional identifier to track a commercial or marketplace transaction executed to produce this data."
    )
    ephemType: int | None = Field(
        None,
        description="The ephemeris type associated with this TLE."
    )
    origNetwork: OrigNetwork

    # Note: The schema mentions onOrbit as a nested object, but doesn't fully define it
    # If needed, this could be added as a reference to another model
    # onOrbit: OnOrbit | None = None

    model_config = ConfigDict(
        use_enum_values=True,
        extra="forbid"  # Matches "additionalProperties": false in schema
    )
