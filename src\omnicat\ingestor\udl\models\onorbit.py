from pydantic import BaseModel, Field, ConfigDict
from enum import Enum
from datetime import date, datetime
from omnicat.ingestor.udl.models.common import DataMode

DataModeEnum = DataMode


class ObjectTypeEnum(str, Enum):
    ROCKET_BODY = "ROCKET BODY"
    DEBRIS = "DEBRIS"
    PAYLOAD = "PAYLOAD"
    PLATFORM = "PLATFORM"
    MANNED = "MANNED"
    UNKNOWN = "UNKNOWN"


class CategoryEnum(str, Enum):
    UNKNOWN = "Unknown"
    ON_ORBIT = "On-Orbit"
    DECAYED = "Decayed"
    CATALOGED_WITHOUT_STATE = "Cataloged Without State"
    LAUNCH_NOMINAL = "Launch Nominal"
    ANALYST_SATELLITE = "Analyst Satellite"
    CISLUNAR = "Cislunar"
    LUNAR = "Lunar"
    HYPERBOLIC = "Hyperbolic"
    HELIOCENTRIC = "Heliocentric"
    INTERPLANETARY = "Interplanetary"
    LAGRANGIAN = "Lagrangian"
    DOCKED = "Docked"


class OnOrbit_Abridged(BaseModel):
    """Model object representing on-orbit objects or satellites in the system."""

    idOnOrbit: str = Field(
        min_length=1,
        max_length=36,
        description="For the public catalog, the idOnOrbit is typically the satellite number as a string, but may be a UUID for analyst or other unknown or untracked satellites, auto-generated by the system."
    )
    classificationMarking: str = Field(
        ...,
        min_length=1,
        max_length=128,
        description="Classification marking of the data in IC/CAPCO Portion-marked format."
    )
    satNo: int = Field(
        ...,
        ge=0,
        description="Satellite/Catalog number of the target on-orbit object."
    )
    commonName: str | None = Field(
        None,
        max_length=128,
        description="Common name of the on-orbit object."
    )
    constellation: str | None = Field(
        None,
        max_length=128,
        description="Constellation to which this satellite belongs."
    )
    intlDes: str | None = Field(
        None,
        max_length=45,
        description="International Designator, typically of the format YYYYLLLAAA, where YYYY is the launch year, LLL is the sequential launch number of that year, and AAA is an optional launch piece designator for the launch."
    )
    launchDate: date | None = Field(
        None,
        description="Date of launch."
    )
    decayDate: datetime | None = Field(
        None,
        description="Date of decay."
    )
    objectType: ObjectTypeEnum | None = Field(
        None,
        description="Type of on-orbit object: ROCKET BODY, DEBRIS, PAYLOAD, PLATFORM, MANNED, UNKNOWN."
    )
    origin: str | None = Field(
        None,
        max_length=64,
        description="Originating system or organization which produced the data, if different from the source."
    )
    missionNumber: str | None = Field(
        None,
        max_length=128,
        description="Mission number of the on-orbit object."
    )
    category: CategoryEnum | None = Field(
        None,
        description="Category of the on-orbit object."
    )
    lifetimeYears: int | None = Field(
        None,
        description="Estimated lifetime of the on-orbit payload, if known."
    )
    altName: str | None = Field(
        None,
        max_length=64,
        description="Alternate name of the on-orbit object."
    )
    createdAt: datetime | None = Field(
        None,
        description="Time the row was created in the database, auto-populated by the system."
    )
    createdBy: str | None = Field(
        None,
        min_length=1,
        max_length=64,
        description="Application user who created the row in the database, auto-populated by the system."
    )
    source: str = Field(
        ...,
        min_length=1,
        max_length=64,
        description="Source of the data."
    )
    dataMode: DataModeEnum = Field(
        ...,
        description="Indicator of whether the data is EXERCISE, REAL, SIMULATED, or TEST data."
    )
    launchSiteId: str | None = Field(
        None,
        max_length=36,
        description="Id of the associated launchSite entity."
    )
    countryCode: str | None = Field(
        None,
        max_length=4,
        description="The country code. This value is typically the ISO 3166 Alpha-2 two-character country code."
    )
    origNetwork: str | None = Field(
        None,
        min_length=1,
        max_length=32,
        description="The originating source network on which this record was created, auto-populated by the system."
    )

    # Allows model to be created from ORM objects
    model_config = ConfigDict(from_attributes=True)
