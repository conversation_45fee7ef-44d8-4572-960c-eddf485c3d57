from datetime import datetime, timedelta, timezone
from time import sleep

from omnicat.ingestor.udl import Elset_Abridged, OnOrbit_Abridged, UdlReader
from omnicat.ingestor.omnicat_ingestor import OmnicatIngestor, get_propagated_ecef_states, get_ingestion_time_window
from omnicat import Settings, get_settings, logger
from omnicat.models import Object, Track, ObjectCorrelation, TLE


def main() -> None:
    try:
        settings = get_settings()
        logger.info(f"\n\nsettings: {settings}\n\n")

        udl_ingestor = UdlIngestor.from_settings(settings)

        while True:
            logger.info("starting ingestion cycle")

            now = datetime.now(timezone.utc)

            window_start, window_end = get_ingestion_time_window(
                now, settings.udl.ingestion_window_days_back, settings.udl.ingestion_window_days_forward)

            udl_ingestor.ingest(window_start, window_end)

            udl_ingestor.clear_stale_state_data(window_start, window_end)

            logger.info(
                f"completed ingestion cycle for time window {window_start} to {window_end}")

            next_ingestion_time = get_next_ingestion_time(
                now, settings.udl.ingestion_period_minutes)
            logger.info(f"next ingestion cycle at {next_ingestion_time}")

            sleep_until(next_ingestion_time)

    except Exception as e:
        logger.error(f"error: {e}")


UDL_CATALOG_NAME = "UDL"


class UdlIngestor:
    def __init__(self, batch_size: int, udl_reader: UdlReader, omnicat_ingestor: OmnicatIngestor):
        self.batch_size = batch_size
        self.udl_reader = udl_reader
        self.omnicat_ingestor = omnicat_ingestor

    @staticmethod
    def from_settings(settings: Settings) -> "UdlIngestor":
        udl_reader = UdlReader.from_settings(settings.udl)
        omnicat_ingestor = OmnicatIngestor.from_settings(settings)
        return UdlIngestor(settings.udl.ingestion_batch_size, udl_reader, omnicat_ingestor)

    def ingest_elset_batch(self, elset_batch: list[Elset_Abridged], window_start: datetime, window_end: datetime) -> None:
        objects = self.get_correlated_omnicat_objects(elset_batch)

        object_ids = self.omnicat_ingestor.ingest_objects(objects)

        tracks = get_tracks(elset_batch, object_ids)

        self.omnicat_ingestor.ingest_tracks(
            tracks, window_start, window_end)

    def ingest(self, window_start: datetime, window_end: datetime) -> None:
        for batch_count, elset_batch in enumerate(self.udl_reader.all_current_elsets(batch_size=self.batch_size, effective_from=window_start)):
            logger.info(
                f"starting ingestion batch {batch_count} for {len(elset_batch)} udl elsets")

            self.ingest_elset_batch(elset_batch, window_start, window_end)
            logger.info(f"completed ingestion batch {batch_count}")

    def ingest_elsets(self, window_start: datetime, window_end: datetime, max_elsets: int, uct: bool = False) -> list[Elset_Abridged]:
        elset_batch = self.udl_reader.get_current_elsets(
            max_results=max_elsets,
            first_result=0,
            effective_from=window_start,
            uct=uct
        )

        self.ingest_elset_batch(elset_batch, window_start, window_end)

        return elset_batch

    def get_correlated_omnicat_objects(self, elsets: list[Elset_Abridged]) -> list[Object]:
        onorbit_ids = [
            elset.idOnOrbit for elset in elsets if elset.idOnOrbit is not None]

        if len(onorbit_ids) == 0:  # all uncorrelated tracks
            return []

        onorbits = self.get_correlated_onorbits(onorbit_ids)
        objects = self.get_omnicat_objects(onorbits)
        return objects

    def get_correlated_onorbits(self, onorbit_ids: list[str]) -> list[OnOrbit_Abridged]:
        onorbits = self.udl_reader.get_onorbits(onorbit_ids)
        logger.info(f"got {len(onorbits)} onorbits")
        return onorbits

    def get_omnicat_objects(self, onorbits: list[OnOrbit_Abridged]) -> list[Object]:
        objects = [Object(
            source=UDL_CATALOG_NAME,
            type=onorbit.objectType,
            data_uris=[self.udl_reader.get_onorbit_uri(onorbit)],
            country_code=onorbit.countryCode,
            common_name=onorbit.commonName,
            metadata={
                "udl_onorbit": onorbit.model_dump(mode='json')
            }
        ) for onorbit in onorbits]

        logger.info(f"got {len(objects)} objects")

        return objects

    def clear_stale_state_data(self, window_start: datetime, window_end: datetime) -> None:
        self.omnicat_ingestor.clear_stale_state_data(window_start, window_end)


def get_next_ingestion_time(previous_ingestion_time: datetime, ingestion_period_minutes: int) -> datetime:
    return previous_ingestion_time + \
        timedelta(minutes=ingestion_period_minutes)


def sleep_until(next_ingestion_time: datetime) -> None:
    sleep_seconds = (next_ingestion_time -
                     datetime.now(timezone.utc)).total_seconds()
    if sleep_seconds > 0:
        logger.info(
            f"sleeping for {(next_ingestion_time - datetime.now(timezone.utc)).total_seconds()} seconds")
        sleep(sleep_seconds)


def get_tracks(elsets: list[Elset_Abridged], object_ids: list[str]) -> list[Track]:
    uncorrelated_tracks = get_uncorrelated_tracks(elsets)
    correlated_tracks = get_correlated_tracks(
        elsets, object_ids)
    all_tracks = uncorrelated_tracks + correlated_tracks
    logger.info(f"got {len(all_tracks)} total tracks")
    return all_tracks


def get_uncorrelated_tracks(elsets: list[Elset_Abridged]) -> list[Track]:
    uncorrelated_elsets = [
        elset for elset in elsets if elset.idOnOrbit is None]

    uncorrelated_tracks = [Track(
        source=UDL_CATALOG_NAME,
        format="tle",
        data=TLE(
            line1=elset.line1,
            line2=elset.line2
        ),
        metadata={
            "udl_elset": elset.model_dump(mode='json')
        }
    ) for elset in uncorrelated_elsets]

    logger.info(f"got {len(uncorrelated_tracks)} uncorrelated tracks")

    return uncorrelated_tracks


def get_correlated_tracks(elsets: list[Elset_Abridged], object_ids: list[str]) -> list[Track]:
    correlated_elsets = [
        elset for elset in elsets if elset.idOnOrbit is not None]

    correlated_tracks = [Track(
        source=UDL_CATALOG_NAME,
        format="tle",
        data=TLE(
            line1=elset.line1,
            line2=elset.line2
        ),
        metadata={
            "udl_elset": elset.model_dump(mode='json')
        },
        object_correlations=[ObjectCorrelation(
            object_id=object_id,
            validity_time=elset.epoch,
            confidence=1.0
        )]) for object_id, elset in zip(object_ids, correlated_elsets)]

    logger.info(f"got {len(correlated_tracks)} correlated tracks")

    return correlated_tracks


if __name__ == "__main__":
    main()
