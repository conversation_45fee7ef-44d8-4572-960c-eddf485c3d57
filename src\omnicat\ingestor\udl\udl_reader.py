import base64
from datetime import datetime
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from ratelimit import limits, sleep_and_retry
import requests
from omnicat.ingestor.udl.models import Elset_Abridged, OnOrbit_Abridged
from omnicat.settings import UdlSettings
from typing import Generator
from omnicat import logger
from dataclasses import dataclass

DEFAULT_BASE_URL = "https://unifieddatalibrary.com/udl"
DEFAULT_CURRENT_ELSETS_SUFFIX = "elset/current"
DEFAULT_ONORBIT_SUFFIX = "onorbit"


@dataclass
class RequestThrottleSettings:
    max_requests_per_second: int = 3
    request_period: int = 1
    max_attempts: int = 5
    wait_exponential_min: int = 4
    wait_exponential_max: int = 10
    wait_exponential_multiplier: float = 1

    @staticmethod
    def from_settings(settings: UdlSettings) -> "RequestThrottleSettings":
        return RequestThrottleSettings(
            max_requests_per_second=settings.max_requests_per_second,
            request_period=settings.request_period,
            max_attempts=settings.max_attempts,
            wait_exponential_min=settings.wait_exponential_min,
            wait_exponential_max=settings.wait_exponential_max,
            wait_exponential_multiplier=settings.wait_exponential_multiplier
        )


class UdlReader:
    def __init__(self,
                 user: str,
                 password: str,
                 base_url: str = DEFAULT_BASE_URL,
                 current_elsets_suffix: str = DEFAULT_CURRENT_ELSETS_SUFFIX,
                 onorbit_suffix: str = DEFAULT_ONORBIT_SUFFIX,
                 request_throttle_settings: RequestThrottleSettings = RequestThrottleSettings()):
        self.basic_auth = get_basic_auth(user, password)
        self.base_url = base_url
        self.current_elsets_suffix = current_elsets_suffix
        self.onorbit_suffix = onorbit_suffix
        self.fetch_data_func = get_data_fetcher(request_throttle_settings)

    @staticmethod
    def from_settings(settings: UdlSettings) -> "UdlReader":
        return UdlReader(
            user=settings.user,
            password=settings.password,
            request_throttle_settings=RequestThrottleSettings.from_settings(
                settings)
        )

    def all_current_elsets(self, effective_from: datetime = None, effective_until: datetime = None, batch_size: int = 1000) -> Generator[list[Elset_Abridged], None, None]:
        first_result = 0
        elset_batch = self.get_current_elsets(
            batch_size, first_result, effective_from, effective_until)

        yield elset_batch

        while len(elset_batch) != 0:
            first_result += len(elset_batch)
            elset_batch = self.get_current_elsets(
                batch_size, first_result, effective_from, effective_until)
            if len(elset_batch) == 0:
                break
            yield elset_batch

    def get_current_elsets(self,
                           max_results: int = 1000,
                           first_result: int = 0,
                           effective_from: datetime = None,
                           effective_until: datetime = None,
                           uct: bool = False
                           ) -> list[Elset_Abridged]:

        url = f"{self.current_elsets_endpoint()}?dataMode=REAL&maxResults={max_results}&firstResult={first_result}"

        if effective_from is not None:
            # Format datetime as ISO 8601 and URL-encode it
            formatted_date = effective_from.isoformat().replace("+00:00", "Z")
            url += f"&effectiveFrom={requests.utils.quote(f'>{formatted_date}')}"
        if effective_until is not None:
            # Format datetime as ISO 8601 and URL-encode it
            formatted_date = effective_until.isoformat().replace("+00:00", "Z")
            url += f"&effectiveUntil={requests.utils.quote(f'<{formatted_date}')}"
        if uct:
            url += f"&uct=true"

        logger.info(
            f"fetching current elsets from {url}")

        return [Elset_Abridged(**elset) for elset in self.fetch_data(url)]

    def get_onorbits(self, ids: list[str]) -> list[OnOrbit_Abridged]:
        if len(ids) == 0:
            return []

        url = f"{self.onorbit_endpoint()}?idOnOrbit={','.join(ids)}"
        logger.info(
            f"fetching {len(ids)} onorbits from {self.onorbit_endpoint()}")
        onorbits = [OnOrbit_Abridged(
            **onorbit) for onorbit in self.fetch_data(url)]

        return order_onorbits_by(onorbits, ids)

    def fetch_data(self, url: str):
        return self.fetch_data_func(url, self.basic_auth)

    def current_elsets_endpoint(self) -> str:
        return f"{self.base_url}/{self.current_elsets_suffix}"

    def onorbit_endpoint(self) -> str:
        return f"{self.base_url}/{self.onorbit_suffix}"

    def get_onorbit_uri(self, onorbit: OnOrbit_Abridged) -> str:
        return f"{self.onorbit_endpoint()}?idOnOrbit={onorbit.idOnOrbit}"


def get_data_fetcher(request_throttle_settings=RequestThrottleSettings()):
    @sleep_and_retry
    @limits(calls=request_throttle_settings.max_requests_per_second,
            period=request_throttle_settings.request_period)
    @retry(
        stop=stop_after_attempt(request_throttle_settings.max_attempts),
        wait=wait_exponential(multiplier=request_throttle_settings.wait_exponential_multiplier,
                              min=request_throttle_settings.wait_exponential_min,
                              max=request_throttle_settings.wait_exponential_max),
        retry=retry_if_exception_type(
            (requests.exceptions.RequestException, requests.exceptions.HTTPError))
    )
    def fetch_data(url, basic_auth):
        try:
            response = requests.get(
                url, headers={'Authorization': basic_auth}, verify=False)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error occurred while fetching data: {e}")
            return []
        except requests.exceptions.RequestException as e:
            logger.error(f"Error occurred while fetching data: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error in fetch_data: {e}")
            return []

    return fetch_data


def get_basic_auth(user: str, password: str):
    return "Basic " + \
        base64.b64encode((f"{user}:{password}").encode(
            'utf-8')).decode("ascii")


def order_onorbits_by(onorbits: list[OnOrbit_Abridged], ids: list[str]) -> list[OnOrbit_Abridged]:
    # Create a mapping of id to onorbit object for efficient lookup
    onorbit_map = {onorbit.idOnOrbit: onorbit for onorbit in onorbits}

    # Return objects in the exact order specified by ids list
    # Only include ids that actually exist in the onorbits list
    return [onorbit_map[id_] for id_ in ids if id_ in onorbit_map]
