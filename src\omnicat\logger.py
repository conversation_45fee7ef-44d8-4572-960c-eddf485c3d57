import logging
import json
import os
from colorama import init, Fore

# Initialize colorama for Windows compatibility
init()

# Custom formatter that adds colors and matches the UTC format in logging.json


class ColoredFormatter(logging.Formatter):
    def format(self, record):
        # Define color based on log level
        color = Fore.RESET
        if record.levelname == 'INFO':
            color = Fore.GREEN
        elif record.levelname == 'WARNING':
            color = Fore.YELLOW
        elif record.levelname == 'ERROR':
            color = Fore.RED
        elif record.levelname == 'CRITICAL':
            color = Fore.MAGENTA
        elif record.levelname == 'DEBUG':
            color = Fore.CYAN

        # Format the record with the default formatter
        formatted_message = super().format(record)

        # Insert color codes before timestamp and levelname
        # First, find the timestamp section which is between [ and ]
        start_bracket = formatted_message.find('[')
        end_bracket = formatted_message.find(']')

        if start_bracket != -1 and end_bracket != -1:
            # Split the message into parts
            prefix = formatted_message[:start_bracket]
            timestamp = formatted_message[start_bracket:end_bracket+1]
            rest = formatted_message[end_bracket+1:]

            # Apply color to timestamp
            colored_timestamp = f"{color}{timestamp}{Fore.RESET}"

            # Apply color to levelname if it exists in the rest part
            level_part = rest.split(':')[0].strip()
            if level_part:
                colored_rest = rest.replace(
                    level_part, f"{color}{level_part}{Fore.RESET}", 1)
                return prefix + colored_timestamp + colored_rest

        return formatted_message


# Create and configure logger
logger = logging.getLogger('omnicat')
handler = logging.StreamHandler()

# Create formatter based on the UTC format in logging.json
formatter = ColoredFormatter(
    '[%(asctime)s.%(msecs)03d+0000] %(levelname)s:  %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S')

handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)
