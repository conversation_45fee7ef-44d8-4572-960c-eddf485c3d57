from pydantic import BaseModel, Field
from datetime import datetime
from omnicat.models import Pagination


class Conjunction(BaseModel):
    track_a_id: str = Field(..., description="The ID of the first track.")
    track_b_id: str = Field(..., description="The ID of the second track.")
    closest_approach_distance: float = Field(
        ..., description="Closest approach distance in kilometers.")
    time_of_closest_approach: datetime = Field(
        ..., description="Time of closest approach in ISO 8601 format.")


class QueryConjunctionsResponse(BaseModel):
    conjunctions: list[Conjunction] = Field(...,
                                            description="List of conjunctions.")
    pagination: Pagination = Field(..., description="Pagination information.")
