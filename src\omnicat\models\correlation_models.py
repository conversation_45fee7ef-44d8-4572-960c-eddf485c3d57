from pydantic import BaseModel, <PERSON>
from datetime import datetime
from omnicat.models import Metadata


class ObjectCorrelation(BaseModel):
    """
    An object correlation represents a connection between a track and an
    object. It includes the confidence (probability) of the correlation and
    optional metadata. The validity_time specifies when the correlation was made.
    """
    object_id: str | None = Field(
        None, description="The ID of the correlated object.")
    validity_time: datetime = Field(...,
                                    description="Timestamp of the correlation in ISO 8601 format.")
    confidence: float = Field(..., ge=0.0, le=1.0,
                              description="Confidence level of the correlation.")
    metadata: Metadata | None = Field(
        None, description="Optional metadata for the correlation.")

class TrackCorrelation(BaseModel):
    """
    A track correlation represents a connection between an object or state and a track.
    It includes the confidence (probability) of the correlation and optional metadata. 
    The validity_time when the correlation was made.
    """
    track_id: str | None = Field(
        None, description="The ID of the correlated track.")
    validity_time: datetime = Field(...,
                                    description="Timestamp of the correlation in ISO 8601 format.")
    confidence: float = Field(..., ge=0.0, le=1.0,
                              description="Confidence level of the correlation.")
    metadata: Metadata | None = Field(
        None, description="Optional metadata for the correlation.")


class CorrelateTrackRequest(BaseModel):
    track_id: str = Field(..., description="unique id of track")
    correlation_type: str = Field(...,
                                  description="type of correlation", json_schema_extra={"enum": ["object", "state"]})
    correlation: ObjectCorrelation = Field(
        ..., description="correlation to create")
