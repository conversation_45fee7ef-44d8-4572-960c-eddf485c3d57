from pydantic import BaseModel, Field
from omnicat.models import Metadata, TrackCorrelation


class Object(BaseModel):
    """
    Represents an object in space. Includes optional data URIs describing the
    object and metadata (key-value pairs). Metadata can include fields such
    as "International Designator" or other descriptive entries.
    """
    source: str = Field(...,
                        description="Source or entity providing this object data.")
    type: str | None = Field(
        None, description="Type or category of the space object.")
    internal_id: str | None = Field(
        None, description="Unique identifier for the object.")
    data_uris: list[str] | None = Field(
        None, description="URIs defining the object.")
    metadata: Metadata | None = Field(
        None, description="Optional metadata for the object.")
    country_code: str | None = Field(
        None, description="Country code (typically ISO 3166 Alpha-2) of the object's country of origin.")
    common_name: str | None = Field(
        None, max_length=128, description="Common name of the object.")
    allegiance: str | None = Field(
        None, description="Allegiance color derived from the country's allegiance relationship.")


def get_international_designator(obj: Object) -> str:
    """
    Retrieves the "International Designator" from the object's metadata if it exists.
    Otherwise, returns None.
    """
    metadata = obj.metadata or {}
    return metadata.get("International Designator")


object_id_description = "The unique identifier of the object. This can be either the `object_id` (e.g., \"OBJ-456\") or the `International Designator` (e.g., \"2023-045A\")"


class AddObjectResponse(BaseModel):
    object_id: str = Field(...,
                           description="The unique identifier of the object.")
    message: str = Field(...,
                         description="A message indicating the status of the operation.")


class GetObjectResponse(BaseModel):
    object_id: str = Field(...,
                           description="The unique identifier of the object.")
    object: Object = Field(..., description="The object data.")
    correlations: list[TrackCorrelation] | None = Field(
        ..., description="Correlations with tracks.")


class GetObjectMetadataResponse(BaseModel):
    object_id: str = Field(...,
                           description="The unique identifier of the object.")
    metadata: Metadata = Field(...,
                               description="The metadata correlated with the object.")
