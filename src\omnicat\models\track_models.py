from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, field_validator
from omnicat.models import StateVector, TLE, Metadata, ObjectCorrelation, Pagination


class Track(BaseModel):
    """
    Information necessary to add a track to the catalog.
    A Track Represents information necessary to describe and propagate object motion.
    A track can be provided in state vector format or as a Two-Line Element (TLE) set.
    Regardless of the input format, the underlying storage converts and stores
    the track as a state vector in the Earth-Centered Inertial (ECI) frame. Metadata
    and correlations with objects or states are optional.
    """
    source: str = Field(...,
                        description="Sensor or entity reporting the track.")
    format: str = Field(..., description="Format of the track.", json_schema_extra={
                        "enum": ["state_vector", "tle"]})
    data: StateVector | TLE = Field(...,
                                    description="Track data.")
    metadata: Metadata | None = Field(
        None, description="Optional metadata.")
    object_correlations: list[ObjectCorrelation] | None = Field(
        None, description="Correlations with objects.")

    def as_state_vector(self) -> "Track":
        if self.format == "state_vector":
            return self
        elif self.format == "tle":
            if self.metadata is None:
                self.metadata = {}
            self.metadata["tle"] = self.data.model_dump(mode='json')

            state_vector = None
            if "state_vector" in self.metadata:
                state_vector = self.metadata["state_vector"]
            else:
                state_vector = self.data.to_state_vector()

            return Track(
                source=self.source,
                format="state_vector",
                data=state_vector,
                metadata=self.metadata,
                object_correlations=self.object_correlations
            )
        else:
            raise ValueError(f"Invalid Track format: {self.format}")

    def as_tle(self) -> "Track":
        if self.format == "tle":
            return self
        elif self.format == "state_vector":
            if self.metadata is None:
                self.metadata = {}
            self.metadata["state_vector"] = self.data.model_dump(mode='json')

            tle = None
            if self.metadata and "tle" in self.metadata:
                tle = self.metadata["tle"]
            else:
                tle = TLE.from_state_vector(self.data)

            return Track(
                source=self.source,
                format="tle",
                data=tle,
                metadata=self.metadata,
                object_correlations=self.object_correlations
            )
        else:
            raise ValueError(f"Invalid Track format: {self.format}")

    def as_format(self, format: str) -> "Track":
        if format == "state_vector":
            return self.as_state_vector()
        elif format == "tle":
            return self.as_tle()
        else:
            raise ValueError(f"Invalid Track format: {format}")


class AddTrackResponse(BaseModel):
    track_id: str = Field(...,
                          description="The unique identifier of the track.")
    message: str = Field(...,
                         description="A message indicating the status of the operation.")


class GetTrackResponse(BaseModel):
    track_id: str = Field(...,
                          description="The unique identifier of the track.")
    track: Track = Field(..., description="The track data.")


class GetTrackMetadataResponse(BaseModel):
    track_id: str = Field(...,
                          description="The unique identifier of the track.")
    metadata: Metadata = Field(...,
                               description="The metadata correlated with the track.")


class QueryTracksResponse(BaseModel):
    tracks: list[GetTrackResponse] = Field(..., description="List of tracks.")
    pagination: Pagination = Field(..., description="Pagination information.")


class TrackFilterOptionsResponse(BaseModel):
    sources: list[str] = Field(..., description="Available track sources.")
    object_types: list[str] = Field(..., description="Available object types.")
    country_codes: list[str] = Field(...,
                                     description="Available country codes.")
    allegiances: list[str] = Field(...,
                                   description="Available allegiance colors.")


class CorrelationStatus(str, Enum):
    """
    Enumeration of possible correlation statuses for tracks.
    """
    CORRELATED = "Correlated"
    UNCORRELATED = "Uncorrelated"


class CorrelatedTracksFilter(BaseModel):
    """
    Filter model for correlated tracks queries.

    All fields are optional. If a field is not provided, no filtering is done on that dimension.
    """
    sources: list[str] | None = Field(
        default=None, description="Filter by source names")
    object_types: list[str] | None = Field(
        default=None, description="Filter by object types")
    country_codes: list[str] | None = Field(
        default=None, description="Filter by country codes")
    allegiances: list[str] | None = Field(
        default=None, description="Filter by allegiance names or colors")


class TrackFilter(BaseModel):
    """
    Filter model for tracks queries.

    All fields are optional. If a field is not provided, no filtering is done on that dimension.
    """
    sources: list[str] | None = Field(
        default=None, description="Filter by source names")
    correlation_statuses: list[CorrelationStatus] | None = Field(
        default=None, description="Filter by correlation status")
    correlated_tracks_filter: CorrelatedTracksFilter | None = Field(
        default=None, description="Filter for correlated tracks")

    @field_validator('correlation_statuses')
    @classmethod
    def validate_correlation_statuses(cls, v):
        if v is None or len(v) == 0:
            return v

        valid_statuses = set(e.value for e in CorrelationStatus)
        statuses_set = set(status.value if isinstance(
            status, CorrelationStatus) else status for status in v)

        if not statuses_set.issubset(valid_statuses):
            raise ValueError(
                f"Invalid correlation statuses. Must be one of: {valid_statuses}")

        return v

    def determine_correlation_status(self) -> tuple[bool, bool]:
        """
        Determine whether to get correlated tracks, uncorrelated tracks, or both based on this filter.

        Returns:
            tuple: (get_correlated, get_uncorrelated)
        """
        # Default to getting both correlated and uncorrelated tracks
        get_correlated = True
        get_uncorrelated = True

        if self.correlation_statuses:
            correlated_status = "Correlated" in [status.value if hasattr(status, 'value') else status
                                                 for status in self.correlation_statuses]
            uncorrelated_status = "Uncorrelated" in [status.value if hasattr(status, 'value') else status
                                                     for status in self.correlation_statuses]
            get_correlated = correlated_status
            get_uncorrelated = uncorrelated_status

        return get_correlated, get_uncorrelated


def convert_correlation_statuses(correlation_statuses: list[str] | None) -> list[CorrelationStatus] | None:
    """
    Helper function to convert string correlation statuses to enum values.

    Args:
        correlation_statuses: List of correlation status strings or None

    Returns:
        List of CorrelationStatus enum values or None if input is None

    Raises:
        ValueError: If invalid correlation status values are provided
    """
    if not correlation_statuses:
        return None

    try:
        return [CorrelationStatus(status) for status in correlation_statuses]
    except ValueError:
        valid_values = [status.value for status in CorrelationStatus]
        raise ValueError(
            f"Invalid correlation status. Must be one of: {valid_values}"
        )


def get_track_filter(
    sources: list[str] | None = None,
    object_types: list[str] | None = None,
    country_codes: list[str] | None = None,
    allegiances: list[str] | None = None,
    correlation_statuses: list[str] | None = None
) -> TrackFilter:
    """
    Helper function to create a TrackFilter from query parameters.

    This function handles the conversion of string correlation statuses to enum values
    and creates the appropriate CorrelatedTracksFilter and TrackFilter objects.

    Args:
        sources: Filter by track sources
        object_types: Filter by object types  
        country_codes: Filter by country codes
        allegiances: Filter by allegiance colors
        correlation_statuses: Filter by correlation status strings

    Returns:
        TrackFilter: Configured filter object

    Raises:
        ValueError: If invalid correlation status values are provided
    """
    correlated_filter = CorrelatedTracksFilter(
        sources=sources,
        object_types=object_types,
        country_codes=country_codes,
        allegiances=allegiances
    )

    enum_correlation_statuses = convert_correlation_statuses(
        correlation_statuses)

    return TrackFilter(
        sources=sources,
        correlation_statuses=enum_correlation_statuses,
        correlated_tracks_filter=correlated_filter
    )
