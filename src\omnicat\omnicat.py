from datetime import datetime, timedelta, timezone
import numpy as np
import tempfile
import os
import tarfile
from omnicat.db import Neo4jDb, PostGisDb
from omnicat.models import (
    Object,
    StateVector,
    Track,
    TrackCorrelation,
    ObjectCorrelation,
    AreaOfInterest,
    CorrelatedTracksFilter
)
from omnicat.models.track_models import TrackFilter, CorrelationStatus
from omnicat import Settings
from omnicat.egestor import OmnicatEgestor
from omnicat.logger import logger

from omnicat import processor


def generate_timestamps(start_time: datetime, end_time: datetime, n_times: int = 100) -> list[datetime]:
    """
    Generate n_times evenly spaced timestamps between start_time and end_time.

    Args:
        start_time: Start of the time range
        end_time: End of the time range
        n_times: Number of timestamps to generate (default: 100)

    Returns:
        List of datetime objects evenly spaced between start_time and end_time
    """
    total_duration = (end_time - start_time).total_seconds()
    time_step = total_duration / (n_times - 1)
    return [start_time + timedelta(seconds=i * time_step) for i in range(n_times)]


class OmniCat(object):
    """
    Main interface for the OmniCat service. This class wraps the database and implements
    core business logic for the service.
    """

    def __init__(self, neo4j_db: Neo4jDb, postgis_db: PostGisDb) -> None:
        self.neo4j_db = neo4j_db
        self.postgis_db = postgis_db

    def add_object(self, obj: Object) -> str:
        return self.neo4j_db.add_object(obj)

    def get_object(self, id: str) -> Object | None:
        return self.neo4j_db.get_object(id)

    def get_objects(self, identifiers: list[str]) -> list[Object]:
        return self.neo4j_db.get_objects(identifiers)

    def add_track(self, track: Track) -> str:
        return self.neo4j_db.add_track(track)

    def get_track(self, track_id: str) -> Track | None:
        return self.neo4j_db.get_track(track_id)

    def correlate_track_with_object(self, track_id: str, object_correlation: ObjectCorrelation) -> None:
        self.neo4j_db.correlate_track_with_object(track_id, object_correlation)

    def query_tracks_within_aoi(self, aoi: AreaOfInterest) -> tuple[list[str], list[Track]]:
        # Convert aoi center to ecef
        aoi_center_ecef = processor.geodetic_to_ecef(
            aoi.center)

        # Do postgis query for tracks within radius in time window with ecef center to get track ids
        tracks_ids_in_aoi = self.postgis_db.query_aoi(
            AreaOfInterest(
                center=aoi_center_ecef,
                radius=aoi.radius,
                time_start=aoi.time_start,
                time_end=aoi.time_end
            )
        )

        return (tracks_ids_in_aoi, self.neo4j_db.get_tracks(tracks_ids_in_aoi))

    def query_filtered_tracks_within_aoi(self, aoi: AreaOfInterest, filter_params: TrackFilter) -> tuple[list[str], list[Track]]:
        """
        Query for tracks that match the filter criteria and are within the specified Area of Interest (AOI).

        Args:
            aoi: The Area of Interest to query within
            filter_params: Filter criteria for tracks

        Returns:
            Tuple containing (list of track IDs, list of Track objects) that match the filter criteria and are within the AOI
        """
        # First get tracks matching the filter criteria from Neo4j
        filtered_track_ids, filtered_tracks = self.neo4j_db.get_filtered_tracks(
            filter_params)

        # If no tracks match the filter, return empty results
        if not filtered_track_ids:
            return ([], [])

        # Convert aoi center to ecef
        aoi_center_ecef = processor.geodetic_to_ecef(
            aoi.center)

        # Query PostGIS for tracks within the AOI that are also in our filtered set
        tracks_ids_in_aoi = self.postgis_db.query_aoi(
            AreaOfInterest(
                center=aoi_center_ecef,
                radius=aoi.radius,
                time_start=aoi.time_start,
                time_end=aoi.time_end
            ),
            track_ids=filtered_track_ids
        )

        # If no tracks are within the AOI, return empty results
        if not tracks_ids_in_aoi:
            return ([], [])

        # Create a mapping of track_id to Track object for efficient lookup
        tracks_map = {track_id: track for track_id,
                      track in zip(filtered_track_ids, filtered_tracks)}

        # Get Track objects for the tracks within AOI (with defensive filtering)
        tracks_in_aoi = [tracks_map[track_id]
                         for track_id in tracks_ids_in_aoi if track_id in tracks_map]

        # Filter track IDs to only include those we have Track objects for
        final_track_ids = [
            track_id for track_id in tracks_ids_in_aoi if track_id in tracks_map]

        return (final_track_ids, tracks_in_aoi)

    def query_conjunctions(self, time_start: datetime, time_end: datetime, max_distance: float) -> list[tuple[str, str, float, datetime]]:
        """
        Query for conjunctions (close approaches) between tracks within a time window.

        Args:
            time_start: Start of time window
            time_end: End of time window
            max_distance: Maximum distance in kilometers for conjunction detection

        Returns:
            List of tuples containing (track_a_id, track_b_id, closest_approach_distance, time_of_closest_approach)
        """
        # Query PostGIS for conjunctions
        return self.postgis_db.query_conjunctions(time_start, time_end, max_distance)

    def generate_export(self, scenario_name: str, time_start: datetime, time_end: datetime, output_dir: str | None = None) -> str:
        """
        Generate an export of OmniCat data for the specified time window.

        Args:
            scenario_name: Name of the scenario to export
            time_start: Start of the time window
            time_end: End of the time window
            output_dir: Optional output directory (if None, creates a temporary directory)

        Returns:
            Path to the generated tar.gz file containing the export
        """
        temp_dir = None
        if output_dir is None:
            temp_dir = tempfile.mkdtemp(prefix="omnicat_export_")
            output_dir = temp_dir

        egestor = OmnicatEgestor(
            neo4j_db=self.neo4j_db,
            postgis_db=self.postgis_db
        )

        scenario_dir = egestor.export_scenario(
            scenario_name=scenario_name,
            time_start=time_start,
            time_end=time_end,
            output_dir=output_dir
        )

        export_dir = os.path.dirname(scenario_dir)
        tar_filename = f"{scenario_name}.tar.gz"
        tar_path = os.path.join(export_dir, tar_filename)

        with tarfile.open(tar_path, "w:gz") as tar:
            # Add just the scenario directory (not the full export_dir path)
            scenario_base_name = os.path.basename(scenario_dir)
            tar.add(scenario_dir, arcname=scenario_base_name)

        return tar_path

    def get_track_filter_options(self) -> dict[str, list[str]]:
        """
        Returns available filter options for tracks based on what exists in the database.

        Returns:
            dict[str, list[str]]: Dictionary containing available filter options
        """
        return self.neo4j_db.get_track_filter_options()


def get_omnicat(settings: Settings) -> OmniCat:
    return OmniCat(
        neo4j_db=Neo4jDb(**settings.neo4j.model_dump()),
        postgis_db=PostGisDb.from_settings(settings.postgis)
    )
