from omnicat.models import Vector3D
from orbitalopsengine.physics.quantities.quantities import kilometer
from orbitalopsengine.physics.quantities.geodetic import GeodeticPosition as OOEGeodeticPosition
from orbitalopsengine.physics.quantities.quantities import kilometer, degree, Quantity

import batchprop as bp


def convert_to_ooe_geodetic(geodetic_position: Vector3D) -> OOEGeodeticPosition:
    latitude_deg = geodetic_position[0]
    longitude_deg = geodetic_position[1]
    altitude_km = geodetic_position[2]
    return OOEGeodeticPosition(
        latitude=Quantity(latitude_deg, unit=degree),
        longitude=Quantity(longitude_deg, unit=degree),
        altitude=Quantity(altitude_km, unit=kilometer)
    )


def geodetic_to_ecef(geodetic_position: Vector3D) -> Vector3D:
    ooe_geodetic = convert_to_ooe_geodetic(geodetic_position)
    ooe_ecef = ooe_geodetic.to_ecef().to(kilometer)
    return [ooe_ecef.x, ooe_ecef.y, ooe_ecef.z]


def eci_to_ecef_batch(eci_batch: bp.PropagatedTleBatch) -> bp.PropagatedTleBatch:
    return bp.EciToEcefSatkit().transform_position_batch(eci_batch)
