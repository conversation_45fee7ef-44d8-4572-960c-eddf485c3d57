from omnicat.models import TLE

from datetime import datetime

import batchprop as bp

import numpy as np


def propagate_tle_batch(tles: list[TLE], timestamps: list[datetime]) -> np.ndarray:
    bp_tles = [bp.TLE(line1=tle.line1, line2=tle.line2) for tle in tles]
    bp_propagator = bp.propagators.Sgp4Vectorized()
    positions, velocities = bp_propagator.propagate(bp_tles, timestamps)
    return positions
