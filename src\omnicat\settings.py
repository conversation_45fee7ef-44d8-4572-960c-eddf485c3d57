import json
import os
from functools import lru_cache
from pydantic_settings import BaseSettings


def get_secrets_dir():
    if os.path.exists("./secrets"):
        return "./secrets"
    if os.path.exists("/run/secrets"):
        return "/run/secrets"
    return None


class Neo4jSettings(BaseSettings):
    user: str = "neo4j"
    password: str = "password"
    uri: str = "bolt://localhost:7687"

    model_config = {
        "env_prefix": "NEO4J_",
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "extra": "ignore",
        "secrets_dir": get_secrets_dir()
    }

    def __repr__(self) -> str:
        attrs = dict(self)
        if 'password' in attrs and attrs['password']:
            attrs['password'] = '*' * len(self.password)
        return f"{self.__class__.__name__}:\n{json.dumps(attrs, indent=2)}"

    def __str__(self) -> str:
        return self.__repr__()


class PostgisSettings(BaseSettings):
    user: str = "gis"
    password: str = "password"
    host: str = "localhost"
    port: int = 5432
    batch_size: int = 10000
    prop_table_name: str = "orbital_tracks"

    model_config = {
        "env_prefix": "POSTGIS_",
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "extra": "ignore",
        "secrets_dir": get_secrets_dir()
    }

    @property
    def uri(self) -> str:
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/gis"

    def __repr__(self) -> str:
        attrs = dict(self)
        if 'password' in attrs and attrs['password']:
            attrs['password'] = '*' * len(self.password)
        return f"{self.__class__.__name__}:\n{json.dumps(attrs, indent=2)}"

    def __str__(self) -> str:
        return self.__repr__()


class UdlSettings(BaseSettings):
    user: str = ""
    password: str = ""

    base_url: str = "https://unifieddatalibrary.com/udl"
    currentelset_suffix: str = "elset/current"
    onorbit_suffix: str = "onorbit"

    max_requests_per_second: int = 3
    request_period: int = 1
    max_attempts: int = 3
    wait_exponential_min: int = 4
    wait_exponential_max: int = 10
    wait_exponential_multiplier: float = 1.0

    ingestion_batch_size: int = 1000
    ingestion_window_days_back: int = 1
    ingestion_window_days_forward: int = 1
    ingestion_period_minutes: int = 60

    model_config = {
        "env_prefix": "UDL_",
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "extra": "ignore",
        "secrets_dir": get_secrets_dir()
    }

    def __repr__(self) -> str:
        attrs = dict(self)
        if 'password' in attrs and attrs['password']:
            attrs['password'] = '*' * len(self.password)
        return f"{self.__class__.__name__}:\n{json.dumps(attrs, indent=2)}"

    def __str__(self) -> str:
        return self.__repr__()


class Settings(BaseSettings):
    neo4j: Neo4jSettings = Neo4jSettings()
    postgis: PostgisSettings = PostgisSettings()
    udl: UdlSettings = UdlSettings()
    propagation_period_seconds: int = 300

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "extra": "ignore",
        "secrets_dir": get_secrets_dir()
    }

    def __repr__(self) -> str:
        attrs = dict(self)
        # Handle nested settings objects
        for key, value in attrs.items():
            if hasattr(value, '__repr__') and not isinstance(value, (str, int, float, bool, list)):
                attrs[key] = json.loads(value.__repr__().split(':', 1)[1])
        return f"{self.__class__.__name__}:\n{json.dumps(attrs, indent=2)}"

    def __str__(self) -> str:
        return self.__repr__()


@lru_cache
def get_settings() -> Settings:
    return Settings()


def get_setting_source(setting_name, prefix=""):
    """Check if a setting comes from environment, .env file, or secrets."""
    full_name = f"{prefix}{setting_name}".upper()

    # Check environment variables
    if full_name in os.environ:
        return "environment variable"

    # Check .env file
    if os.path.exists(".env"):
        with open(".env", "r") as f:
            env_content = f.read()
            if f"{full_name}=" in env_content:
                return ".env file"

    # Check secrets
    secrets_dir = get_secrets_dir()
    if secrets_dir and os.path.exists(os.path.join(secrets_dir, full_name)):
        return "secrets"

    return "default value"


def collect_settings_with_sources(settings_obj, prefix="", parent_path=""):
    """Recursively collect all settings with their sources."""
    result = {}

    # Get the class annotations to find all fields
    if hasattr(settings_obj, "__annotations__"):
        fields = settings_obj.__annotations__
    else:
        # If it's not a settings object with annotations, return empty dict
        return result

    for field_name in fields:
        # Form the full path for this setting
        current_path = f"{parent_path}.{field_name}" if parent_path else field_name

        # Get the value
        value = getattr(settings_obj, field_name)

        # If this is a nested settings object, recurse into it
        if hasattr(value, "__annotations__"):
            # Determine the environment variable prefix for this nested object
            new_prefix = ""
            if hasattr(value, "model_config") and "env_prefix" in value.model_config:
                new_prefix = value.model_config["env_prefix"]

            # Recursively collect settings from this nested object
            nested_result = collect_settings_with_sources(
                value, new_prefix, current_path)
            result.update(nested_result)
        else:
            # This is a leaf setting, determine its source
            source = get_setting_source(field_name, prefix)

            # Mask passwords
            display_value = value
            if field_name == "password" and value:
                display_value = "*" * len(value)

            # Store with full path and source
            result[current_path] = {
                "value": display_value,
                "env_name": f"{prefix}{field_name}".upper() if prefix else field_name,
                "source": source
            }

    return result


if __name__ == "__main__":
    # Show the secrets directory
    secrets_dir = get_secrets_dir()
    print(f"Secrets directory: {secrets_dir}")

    # Load settings
    settings = get_settings()
    print(settings)

    # Collect all settings with their sources
    all_settings = collect_settings_with_sources(settings)

    # Prepare all settings for display in a single table
    settings_list = []
    for path, info in all_settings.items():
        settings_list.append({
            "path": path,
            "env_name": info["env_name"],
            "value": info["value"],
            "source": info["source"]
        })

    # Sort settings by path
    sorted_settings = sorted(settings_list, key=lambda x: x["path"])

    # Print settings in a single table with source as a column
    print("\nSettings Table:")

    # Find the maximum width for each column for alignment
    max_path_width = max([len(s["path"]) for s in sorted_settings] + [12])
    max_env_width = max([len(s["env_name"]) for s in sorted_settings] + [12])
    max_value_width = max([len(str(s["value"]))
                          for s in sorted_settings] + [5])
    max_source_width = max([len(s["source"]) for s in sorted_settings] + [6])

    # Print table header
    print(f"  {'SETTING PATH':<{max_path_width}}  {'ENV VARIABLE':<{max_env_width}}  {'VALUE':<{max_value_width}}  {'SOURCE':<{max_source_width}}")
    print(f"  {'-' * max_path_width}  {'-' * max_env_width}  {'-' * max_value_width}  {'-' * max_source_width}")

    # Print each setting with proper alignment
    for setting in sorted_settings:
        path = setting["path"]
        env_name = setting["env_name"]
        value = setting["value"]
        source = setting["source"]

        print(f"  {path:<{max_path_width}}  {env_name:<{max_env_width}}  {str(value):<{max_value_width}}  {source:<{max_source_width}}")
