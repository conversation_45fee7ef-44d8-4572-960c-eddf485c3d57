from datetime import datetime, timezone, timedelta


def ensure_utc(dt: datetime) -> datetime:
    """Ensures a datetime is in UTC, converting if needed."""
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    return dt.astimezone(timezone.utc)


def generate_timestamps(start: datetime, end: datetime, step: timedelta) -> list[datetime]:
    timestamps = []
    current = start
    while current < end:
        timestamps.append(current)
        current += step
    return timestamps
