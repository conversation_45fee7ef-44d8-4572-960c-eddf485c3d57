from sqlalchemy import text
from omnicat.ingestor import OmnicatIngestor
from omnicat.http import create_app
from omnicat.settings import Settings, Neo4jSettings, PostgisSettings, UdlSettings
from omnicat.models import Object, StateVector, Track, TLE, AreaOfInterest, ObjectCorrelation, CorrelatedTracksFilter
from omnicat.db import Neo4jDb, PostGisDb
import os
import sys
import pytest
import shutil
import json
import tempfile
import tarfile
from datetime import datetime, timezone, timedelta
from pathlib import Path
from neo4j import GraphDatabase
from fastapi.testclient import TestClient
from collections.abc import Generator
import pandas as pd
from io import StringIO

# Add the project root to the Python path so tests can be imported as a package
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


# Add the routes directory to the path so helpers.py can be imported from anywhere
sys.path.append(os.path.join(os.path.dirname(
    __file__), "integration", "http", "routes"))


# Shared verification helpers for tests
def verify_object_properties(retrieved_obj: Object, expected_obj: Object):
    """
    Verify that the retrieved object has the expected properties.
    This ignores the internal_id which is set by the database.

    Args:
        retrieved_obj: The object retrieved from the database
        expected_obj: The expected object to compare against
    """
    assert retrieved_obj.source == expected_obj.source
    assert retrieved_obj.type == expected_obj.type
    assert retrieved_obj.data_uris == expected_obj.data_uris
    assert retrieved_obj.metadata == expected_obj.metadata
    assert retrieved_obj.common_name == expected_obj.common_name


def verify_track_lists(retrieved_tracks: list[Track], expected_tracks: list[Track],
                       check_order: bool = True):
    """
    Verify that two lists of tracks contain the same data.

    Args:
        retrieved_tracks: List of tracks retrieved from the database
        expected_tracks: List of expected tracks
        check_order: If True, verify tracks are in the same order
    """
    assert len(retrieved_tracks) == len(expected_tracks), \
        f"Expected {len(expected_tracks)} tracks, got {len(retrieved_tracks)}"

    if check_order:
        # Compare tracks in order
        for retrieved, expected in zip(retrieved_tracks, expected_tracks):
            assert retrieved == expected
    else:
        # Compare tracks regardless of order using source+metadata as a key
        retrieved_by_key = {
            (track.source, str(track.metadata)): track for track in retrieved_tracks
        }
        expected_by_key = {
            (track.source, str(track.metadata)): track for track in expected_tracks
        }

        # Verify same keys
        assert set(retrieved_by_key.keys()) == set(expected_by_key.keys()), \
            "Retrieved tracks don't match expected tracks"

        # Verify each track
        for key in expected_by_key:
            assert retrieved_by_key[key] == expected_by_key[key]

# Helper functions to create test data


def create_test_object(source_name="TEST_SOURCE", data_uris=None, metadata=None, country_code=None, common_name=None):
    """Create a test object with the specified parameters."""
    if data_uris is None:
        data_uris = [f"uri-{datetime.now().timestamp()}"]
    if metadata is None:
        metadata = {
            "International Designator": f"2023-{int(datetime.now().timestamp() % 1000)}A"}

    return Object(
        source=source_name,
        type="test_object",
        data_uris=data_uris,
        metadata=metadata,
        country_code=country_code,
        common_name=common_name
    )


def create_state_vector_track(source_name="TEST_SOURCE", timestamp=None,
                              position=None, velocity=None, metadata=None, object_correlations=None):
    """Create a test track with a state vector."""
    if timestamp is None:
        timestamp = datetime.now(timezone.utc)
    if position is None:
        position = [1.0, 2.0, 3.0]
    if velocity is None:
        velocity = [1.0, 2.0, 3.0]

    return Track(
        source=source_name,
        format="state_vector",
        data=StateVector(
            timestamp=timestamp,
            position=position,
            velocity=velocity,
            attitude=None,
            angular_velocity=None
        ),
        metadata=metadata,
        object_correlations=object_correlations
    )


def create_object_correlation(object_id, validity_time=None, confidence=0.9, metadata=None):
    """Create an object correlation for a track."""
    if validity_time is None:
        validity_time = datetime.now(timezone.utc)

    return ObjectCorrelation(
        object_id=object_id,
        validity_time=validity_time,
        confidence=confidence,
        metadata=metadata
    )


@pytest.fixture(scope="session")
def test_settings() -> Settings:
    """Provide test-specific settings."""
    return Settings(
        neo4j=Neo4jSettings(
            user=os.getenv("NEO4J_USER", "neo4j"),
            password=os.getenv("NEO4J_PASSWORD", "password"),
            uri=os.getenv("NEO4J_URI", "bolt://localhost:7688")
        ),
        postgis=PostgisSettings(
            user=os.getenv("POSTGIS_USER", "gis"),
            password=os.getenv("POSTGIS_PASSWORD", "password"),
            host=os.getenv("POSTGIS_HOST", "localhost"),
            port=int(os.getenv("POSTGIS_PORT", "5433")),
            batch_size=int(os.getenv("POSTGIS_BATCH_SIZE", "1000")),
            prop_table_name=os.getenv(
                "POSTGIS_PROP_TABLE_NAME", "orbital_tracks")
        ),
        udl=UdlSettings()  # Use defaults for UDL
    )


@pytest.fixture(scope="function")
def neo4j_db(test_settings) -> Generator[Neo4jDb, None, None]:
    """Create a Neo4j database connection and clean up after tests."""
    neo4j_db = Neo4jDb(**test_settings.neo4j.model_dump())
    yield neo4j_db
    # Clean up database after each test
    with neo4j_db.driver.session() as session:
        session.run("MATCH (n) DETACH DELETE n")
    neo4j_db.__del__()


@pytest.fixture(scope="function")
def postgis_db(test_settings) -> Generator[PostGisDb, None, None]:
    """Create a PostGIS database connection and clean up after tests."""
    postgis_db = PostGisDb.from_settings(test_settings.postgis)

    yield postgis_db
    # Clean up database after each test
    with postgis_db.engine.connect() as conn:
        conn.execute(text(f"DELETE FROM {postgis_db.prop_table_name}"))
        conn.commit()

# HTTP client fixtures


@pytest.fixture
def client(test_settings, neo4j_db, postgis_db) -> TestClient:
    """Create a FastAPI test client using test settings."""
    app = create_app(test_settings)
    return TestClient(app)

# Common test data fixtures


@pytest.fixture
def sample_state_vector() -> StateVector:
    return StateVector(
        timestamp=datetime.now(timezone.utc),
        position=[7000.0, 0.0, 0.0],
        velocity=[0.0, 7.5, 0.0],
        attitude=[0.0, 0.0, 0.0],
        angular_velocity=[0.0, 0.0, 0.0]
    )


@pytest.fixture
def sample_object() -> Object:
    return Object(
        source="test_source",
        type="test_type",
        data_uris=["uri1", "uri2"],
        metadata={
            "key1": "value1",
            "International Designator": "2023-123A"
        },
        common_name="Test Satellite"
    )


@pytest.fixture
def sample_track(sample_state_vector: StateVector) -> Track:
    return Track(
        source="test_sensor",
        format="state_vector",
        data=sample_state_vector,
        metadata={"key1": "value1"},
        object_correlations=None
    )


@pytest.fixture
def sample_tle_track() -> Track:
    tle_data = TLE(
        line1="1 25544U 98067A   24012.91667824  .00010379  00000+0  18617-3 0  9993",
        line2="2 25544  51.6419 154.8769 0005633  95.5817 332.8906 15.50388511432702"
    )
    return Track(
        source="test_catalog",
        format="tle",
        data=tle_data,
        metadata={"satellite_name": "ISS (ZARYA)"},
        object_correlations=None
    )

# =============================================================================
# AOI Query Truth Data Fixtures
# =============================================================================
# The following fixtures provide a consistent truth dataset for testing
# AOI query functionality across different test modules.
# They work together to create a comprehensive test scenario with known
# tracks that should be found within a specific AOI and
# tracks that should be excluded from query results.


@pytest.fixture
def aoi_query_time_window() -> tuple[datetime, datetime]:
    """
    Time window for AOI query truth data tests.

    Part of the AOI query truth dataset used in multiple test modules.
    """
    time_start = datetime(2025, 2, 12, 21, 28, tzinfo=timezone.utc)
    time_end = datetime(2025, 2, 12, 22, 28, tzinfo=timezone.utc)
    return time_start, time_end


@pytest.fixture
def tracks_in_aoi() -> list[Track]:
    """
    Sample TLE tracks that should be found within the test aoi.

    Part of the aoi query truth dataset used in multiple test modules.
    These tracks are specifically designed to be found when querying the
    aoi defined by the parameters in aoi_query_params.
    """
    return [
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 46553U 20070X   25043.44875729 -.00001396  00000-0 -74896-4 0  9999",
                line2="2 46553  53.0529  98.4859 0001342  89.6768 270.4375 15.06390418241110"
            ),
            metadata={"test": "in_aoi_1"}
        ),
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 13113U 82025A   25043.65088260  .00000086  00000-0  62233-4 0  9997",
                line2="2 13113  82.5404 128.3447 0018337 170.0297 300.4456 13.85548740167855"
            ),
            metadata={"test": "in_aoi_2"}
        ),
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 08685U 75004M   25043.51171333  .00000980  00000-0  24771-3 0  9990",
                line2="2 08685  97.9835 141.5348 0047053   7.3045 352.8827 14.52410928637988"
            ),
            metadata={"test": "in_aoi_3"}
        ),
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 59402U 24064E   25043.58336806  .00018649  00000-0  68610-3 0  9999",
                line2="2 59402  43.0002  99.4901 0001161 272.4999  20.3372 15.27551076049553"
            ),
            metadata={"test": "in_aoi_4"}
        )
    ]


@pytest.fixture
def tracks_outside_aoi() -> list[Track]:
    """
    Sample TLE tracks that should NOT be found within the test aoi.

    Part of the aoi query truth dataset used in multiple test modules.
    These tracks are specifically designed to be excluded from query results when
    using the aoi defined in aoi_query_params.
    """
    return [
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 21126U 64053C   25043.73987186  .00001407  00000-0  26016-3 0  9991",
                line2="2 21126  65.0780 163.6700 0042632  49.4060  65.4716 14.68530021214751"
            ),
            metadata={"test": "outside_aoi_1"}
        ),
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 31948U 99025CMZ 25036.04470645  .00005051  00000-0  21500-2 0  9995",
                line2="2 31948  99.4157  73.4578 0027701 186.3859 203.6195 14.25459986911014"
            ),
            metadata={"test": "outside_aoi_2"}
        ),
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 28188U 04008B   25042.66784501  .00009277  00000-0  13202-2 0  9992",
                line2="2 28188  49.4818 155.8082 4967776 188.4872 158.3930 05.68665351406550"
            ),
            metadata={"test": "outside_aoi_3"}
        ),
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 99999U 00000A   25043.89972687  .00000000  00000-0  00000+0 0  9992",
                line2="2 99999   0.0935 186.0233 0015667 153.4524 149.9827 01.00256041000005"
            ),
            metadata={"test": "outside_aoi_4"}
        )
    ]


@pytest.fixture
def aoi_query_params(aoi_query_time_window) -> dict:
    """
    Query parameters for aoi query tests.

    Part of the aoi query truth dataset used in multiple test modules.
    Defines the aoi that will contain the tracks from tracks_in_aoi
    and exclude tracks from tracks_outside_aoi.
    """
    time_start, time_end = aoi_query_time_window
    return {
        "center": [49.79, 29.91, 1500.0],  # lat, lon, alt in km
        "radius": 2000.0,  # km
        "time_start": time_start,
        "time_end": time_end
    }


@pytest.fixture
def aoi(aoi_query_params) -> AreaOfInterest:
    """
    AOI object for query tests.

    Part of the aoi query truth dataset used in multiple test modules.
    Creates an AreaOfInterest object from the parameters defined in 
    aoi_query_params.
    """
    return AreaOfInterest(**aoi_query_params)


@pytest.fixture
def aoi_query_truth_data(
    aoi_query_time_window,
    tracks_in_aoi,
    tracks_outside_aoi,
    aoi_query_params,
    aoi
):
    """
    Composite fixture providing all related fixtures for aoi query tests.

    This fixture groups all the related truth data fixtures into a single object for
    convenience and to make the relationship between these fixtures explicit.

    Returns:
        dict: A dictionary containing all related truth data fixtures:
            - time_window: Start and end times for the query
            - tracks_in_aoi: Tracks that should be found in the aoi
            - tracks_outside_aoi: Tracks that should not be found in the aoi
            - query_params: Parameters for constructing query requests
            - aoi: The AreaOfInterest object for direct API use
    """
    time_start, time_end = aoi_query_time_window
    return {
        "time_window": {
            "start": time_start,
            "end": time_end
        },
        "tracks_in_aoi": tracks_in_aoi,
        "tracks_outside_aoi": tracks_outside_aoi,
        "query_params": aoi_query_params,
        "aoi": aoi
    }


@pytest.fixture
def test_objects():
    """Create test objects for ingestion and export tests."""
    return [
        Object(
            id="TEST_SAT_1",
            source="test_source",
            type="satellite",
            data_uris=["test://uri1"],
            metadata={
                "name": "Test Satellite 1",
                "type": "satellite",
                "origin": "test",
                "satellite_id": "TEST_SAT_1"
            }
        ),
        Object(
            id="TEST_SAT_2",
            source="test_source",
            type="satellite",
            data_uris=["test://uri2"],
            metadata={
                "name": "Test Satellite 2",
                "type": "satellite",
                "origin": "test",
                "satellite_id": "TEST_SAT_2"
            }
        )
    ]


@pytest.fixture
def test_tracks():
    """Create test tracks for ingestion and export tests."""
    # These are valid TLEs for testing purposes
    return [
        Track(
            format="tle",
            source="test",
            data=TLE(
                line1="1 25544U 98067A   22001.00000000  .00000000  00000-0  00000-0 0  9995",
                line2="2 25544  51.6416 247.4627 0006703 130.5360 325.0288 15.49140040  1196"
            ),
            metadata={
                "origin": "test",
                "satellite_name": "ISS TEST"
            }
            # The object_correlations will be created in the test
        ),
        Track(
            format="tle",
            source="test",
            data=TLE(
                line1="1 43013U 17073A   22001.00000000  .00000000  00000-0  00000-0 0  9993",
                line2="2 43013  98.7490 112.1060 0001247  88.0480 272.0880 14.19710580  1199"
            ),
            metadata={
                "origin": "test",
                "satellite_name": "STARLINK TEST"
            }
            # The object_correlations will be created in the test
        )
    ]


@pytest.fixture
def test_ingest_helper():
    """Helper function for ingesting test data in multiple test files."""
    def _ingest_test_data(
        neo4j_db,
        postgis_db,
        test_objects: list[Object],
        test_tracks: list[Track],
        time_start: datetime,
        time_end: datetime,
        validity_time: datetime,
        propagation_period_seconds: int = 3600
    ) -> tuple[list[str], list[str]]:
        """
        Helper method to ingest test objects and tracks into the databases.

        Args:
            neo4j_db: Neo4j database instance
            postgis_db: PostGIS database instance
            test_objects: List of objects to ingest
            test_tracks: List of tracks to ingest
            time_start: Start time for track propagation
            time_end: End time for track propagation
            validity_time: Time at which object correlations are valid
            propagation_period_seconds: Time interval between propagated states

        Returns:
            Tuple of (object_ids, track_ids)
        """
        # Create ingestor
        ingestor = OmnicatIngestor(
            neo4j_db=neo4j_db,
            postgis_db=postgis_db,
            propagation_period_seconds=propagation_period_seconds
        )

        # Step 1: Ingest objects first and get their IDs
        object_ids = ingestor.ingest_objects(test_objects)

        # Step 2: Update track object correlations with the actual database-assigned object IDs
        updated_tracks = []
        for i, track in enumerate(test_tracks):
            # Create a track with proper object correlations
            updated_track = Track(
                source=track.source,
                format=track.format,
                data=track.data,
                metadata=track.metadata,
                object_correlations=[
                    ObjectCorrelation(
                        # Use modulo to handle case with more tracks than objects
                        object_id=object_ids[i % len(object_ids)],
                        validity_time=validity_time,
                        confidence=1.0
                    )
                ]
            )
            updated_tracks.append(updated_track)

        # Step 3: Now ingest the tracks with proper object IDs
        track_ids = ingestor.ingest_tracks(
            updated_tracks, time_start, time_end)

        return object_ids, track_ids

    return _ingest_test_data

# Export verification helper functions


@pytest.fixture
def export_file_helpers():
    """Fixture providing helper functions for verifying export file contents."""

    def extract_tar_to_temp_dir(response_content=None, tar_path=None, prefix="test_export_"):
        """
        Extract a tar file to a temporary directory.

        Args:
            response_content: Optional binary content from an HTTP response
            tar_path: Optional path to an existing tar file
            prefix: Prefix for the temporary directory

        Returns:
            Dictionary with paths to the extracted content
        """
        # Create a temporary directory
        temp_dir = tempfile.mkdtemp(prefix=prefix)

        # Save the tar file if we got response content
        if response_content is not None:
            if tar_path is None:
                tar_path = os.path.join(temp_dir, f"{prefix}.tar.gz")
            with open(tar_path, "wb") as f:
                f.write(response_content)

        # Verify it's a valid tar.gz file
        assert tarfile.is_tarfile(tar_path), "Not a valid tar file"

        # Extract the tar file
        extract_dir = os.path.join(temp_dir, "extracted")
        os.makedirs(extract_dir, exist_ok=True)

        with tarfile.open(tar_path, "r:gz") as tar:
            tar.extractall(path=extract_dir, filter='data')

        # Get the first directory in the extracted content (should be the scenario dir)
        subdirs = [d for d in os.listdir(extract_dir)
                   if os.path.isdir(os.path.join(extract_dir, d))]

        scenario_dir = None
        if subdirs:
            scenario_name = subdirs[0]
            scenario_dir = os.path.join(extract_dir, scenario_name)

        return {
            "temp_dir": temp_dir,
            "tar_path": tar_path,
            "extract_dir": extract_dir,
            "scenario_dir": scenario_dir,
            "scenario_name": scenario_name if scenario_dir else None
        }

    def get_scenario_files(scenario_dir: str, scenario_name: str) -> tuple[str, str, str, str]:
        """Helper method to get the standard scenario output files."""
        csv_file = os.path.join(scenario_dir, f"{scenario_name}.csv")
        events_file = os.path.join(scenario_dir, "Events.json")
        rockets_file = os.path.join(scenario_dir, "Rockets.json")
        ground_stations_file = os.path.join(
            scenario_dir, "GroundStations.json")
        return csv_file, events_file, rockets_file, ground_stations_file

    def verify_csv_structure(file_handle) -> None:
        """
        Helper method to verify the structure of the CSV output file.

        Args:
            file_handle: An open file handle to a CSV file
        """
        content = file_handle.read()
        # Reset file pointer to beginning for potential further reading
        file_handle.seek(0)

        # Check for header section
        assert "HEADER_START" in content
        assert "HEADER_END" in content
        assert "time_start" in content
        assert "time_end" in content

        # Check for sensor section
        assert "SENSOR_START" in content
        assert "SENSOR_END" in content

    def extract_data_section_from_csv(file_handle) -> pd.DataFrame:
        """
        Extract and parse the data section from a CSV file.

        Args:
            file_handle: An open file handle to a CSV file

        Returns:
            DataFrame containing the parsed CSV data
        """
        content = file_handle.read()
        lines = content.split('\n')

        # Find the start of the data section (the line starting with "SatNo,timestamp")
        data_start_idx = 0
        for i, line in enumerate(lines):
            if line.startswith("SatNo,timestamp"):
                data_start_idx = i
                break

        assert data_start_idx > 0, "CSV header not found"
        csv_data = "\n".join(lines[data_start_idx:])

        # Parse the CSV data into a DataFrame
        return pd.read_csv(StringIO(csv_data), skip_blank_lines=True)

    def verify_json_files(events_file: str, rockets_file: str, ground_stations_file: str) -> None:
        """Helper method to verify the structure of JSON output files."""
        with open(events_file, 'r') as f:
            events_data = json.load(f)
            assert "collisions" in events_data

        with open(rockets_file, 'r') as f:
            rockets_data = json.load(f)
            assert isinstance(rockets_data, list)

        with open(ground_stations_file, 'r') as f:
            ground_stations_data = json.load(f)
            assert "radar_sensors" in ground_stations_data
            assert "optical_sensors" in ground_stations_data

    def verify_export_files(scenario_dir: str, scenario_name: str, test_objects=None) -> dict:
        """
        Complete verification of export files in a scenario directory.

        Args:
            scenario_dir: Path to the scenario directory
            scenario_name: Name of the scenario
            test_objects: Optional list of expected objects for verification

        Returns:
            Dictionary with parsed data including the DataFrame from the CSV
        """
        # Check that the scenario directory exists
        assert os.path.exists(
            scenario_dir), f"Scenario directory not found: {scenario_dir}"

        # Get file paths
        csv_file, events_file, rockets_file, ground_stations_file = get_scenario_files(
            scenario_dir, scenario_name)

        # Check that all files exist
        assert os.path.exists(csv_file), f"CSV file not found: {csv_file}"
        assert os.path.exists(
            events_file), f"Events.json not found: {events_file}"
        assert os.path.exists(
            rockets_file), f"Rockets.json not found: {rockets_file}"
        assert os.path.exists(
            ground_stations_file), f"GroundStations.json not found: {ground_stations_file}"

        # Verify the CSV content
        with open(csv_file, 'r') as f:
            # Verify base CSV structure
            verify_csv_structure(f)

            # Extract the data section and parse as DataFrame
            df = extract_data_section_from_csv(f)

            # Basic verification of the DataFrame
            if df is not None and len(df) > 0:
                # Verify the expected columns exist
                expected_columns = [
                    "SatNo", "timestamp", "x", "y", "z",
                    "vx", "vy", "vz",
                    "rot_x", "rot_y", "rot_z",
                    "sensors",
                    "fused_x", "fused_y", "fused_z",
                    "entry_type"
                ]
                assert all(
                    col in df.columns for col in expected_columns), f"Missing columns in CSV"

                # If test objects are provided, verify they appear in the data
                if test_objects:
                    sat_ids = df["SatNo"].unique().tolist()
                    for obj in test_objects:
                        if "satellite_id" in obj.metadata:
                            assert obj.metadata["satellite_id"] in sat_ids, \
                                f"Satellite ID {obj.metadata['satellite_id']} not found in data"

        # Verify JSON files
        verify_json_files(events_file, rockets_file, ground_stations_file)

        return {
            "scenario_dir": scenario_dir,
            "csv_file": csv_file,
            "events_file": events_file,
            "rockets_file": rockets_file,
            "ground_stations_file": ground_stations_file,
            "df": df
        }

    return {
        "extract_tar_to_temp_dir": extract_tar_to_temp_dir,
        "get_scenario_files": get_scenario_files,
        "verify_csv_structure": verify_csv_structure,
        "extract_data_section_from_csv": extract_data_section_from_csv,
        "verify_json_files": verify_json_files,
        "verify_export_files": verify_export_files
    }


@pytest.fixture
def track_ingest_helper():
    """Base helper function for ingesting tracks with propagation."""
    def _ingest_tracks(
        neo4j_db,
        postgis_db,
        tracks: list[Track],
        time_start: datetime,
        time_end: datetime,
        propagation_period_seconds: float = 300
    ) -> list[str]:
        """
        Ingest tracks with propagation.

        Args:
            neo4j_db: Neo4j database instance
            postgis_db: PostGIS database instance
            tracks: List of tracks to ingest
            time_start: Start time for propagation window
            time_end: End time for propagation window
            propagation_period_seconds: Time between propagation steps in seconds

        Returns:
            List of track ids for the ingested tracks
        """
        from omnicat.ingestor import OmnicatIngestor
        omnicat_ingestor = OmnicatIngestor(
            neo4j_db,
            postgis_db,
            propagation_period_seconds=propagation_period_seconds
        )

        return omnicat_ingestor.ingest_tracks(tracks, time_start, time_end)

    return _ingest_tracks


@pytest.fixture
def aoi_query_ingest_helper(track_ingest_helper):
    """Helper function for ingesting tracks for AOI query tests."""
    def _ingest_tracks_for_aoi_query(
        neo4j_db,
        postgis_db,
        tracks_in_aoi,
        tracks_outside_aoi,
        aoi,
        propagation_period_seconds: float = 300
    ) -> tuple[list[str], list[str]]:
        """
        Ingest tracks for AOI query tests with proper time window buffering.

        Args:
            neo4j_db: Neo4j database instance
            postgis_db: PostGIS database instance
            tracks_in_aoi: List of tracks expected to be found in the AOI
            tracks_outside_aoi: List of tracks expected to be outside the AOI
            aoi: The Area of Interest for the query
            propagation_period_seconds: Propagation period in seconds

        Returns:
            Tuple of (track_ids_in_aoi, track_ids_outside_aoi)
        """
        # Get time window for ingestion with buffer
        time_start = aoi.time_start - timedelta(days=1)
        time_end = aoi.time_end + timedelta(days=1)

        # Use the base track ingestion helper
        track_ids_in_aoi = track_ingest_helper(
            neo4j_db, postgis_db, tracks_in_aoi, time_start, time_end, propagation_period_seconds
        )
        track_ids_outside = track_ingest_helper(
            neo4j_db, postgis_db, tracks_outside_aoi, time_start, time_end, propagation_period_seconds
        )

        return track_ids_in_aoi, track_ids_outside

    return _ingest_tracks_for_aoi_query


@pytest.fixture
def conjunction_query_ingest_helper(track_ingest_helper):
    """Helper function for ingesting tracks for conjunction query tests."""
    def _ingest_tracks_for_conjunction_query(
        neo4j_db,
        postgis_db,
        tracks_with_conjunction,
        tracks_without_conjunction,
        time_start,
        time_end
    ) -> tuple[list[str], list[str]]:
        """
        Ingest tracks for conjunction query tests with precise propagation timing.

        Args:
            neo4j_db: Neo4j database instance
            postgis_db: PostGIS database instance
            tracks_with_conjunction: List of tracks expected to have conjunctions
            tracks_without_conjunction: List of tracks not expected to have conjunctions
            time_start: Start time for propagation and query window
            time_end: End time for propagation and query window

        Returns:
            Tuple of (conjunction_track_ids, no_conjunction_track_ids)
        """
        # Calculate propagation period to get exactly 100 timestamps
        total_seconds = (time_end - time_start).total_seconds()
        # Need 99 intervals to make 100 timestamps
        # For a 1-hour window (3600 seconds), this is 36.36 seconds
        propagation_period = total_seconds / 99

        # Use the base track ingestion helper with the calculated propagation period
        conjunction_track_ids = track_ingest_helper(
            neo4j_db, postgis_db, tracks_with_conjunction, time_start, time_end, propagation_period
        )
        no_conjunction_track_ids = track_ingest_helper(
            neo4j_db, postgis_db, tracks_without_conjunction, time_start, time_end, propagation_period
        )

        return conjunction_track_ids, no_conjunction_track_ids

    return _ingest_tracks_for_conjunction_query


@pytest.fixture
def correlated_tracks_filter_setup(neo4j_db, test_timestamps):
    """
    Setup fixture for testing the CorrelatedTracksFilter functionality.
    Creates test objects and tracks with different allegiances and sources.
    """
    now = test_timestamps["now"]

    # Dictionary to store all created objects and track IDs
    setup = {
        "objects": {},
        "object_ids": {},
        "tracks": {},
        "track_ids": {}
    }

    # 1. Create objects with different types and countries
    # US satellite (Friendly/Blue)
    setup["objects"]["us_satellite"] = create_test_object(
        source_name="NASA",
        metadata={"name": "US Satellite",
                  "test": "filtered_tracks_test", "object_type": "satellite"},
        country_code="US",
        common_name="USA Satellite"
    )
    setup["objects"]["us_satellite"].type = "satellite"  # Explicitly set type
    setup["object_ids"]["us_satellite"] = neo4j_db.add_object(
        setup["objects"]["us_satellite"])

    # US debris (Friendly/Blue)
    setup["objects"]["us_debris"] = create_test_object(
        source_name="USSF",
        metadata={"name": "US Debris",
                  "test": "filtered_tracks_test", "object_type": "debris"},
        country_code="US",
        common_name="USA Debris"
    )
    setup["objects"]["us_debris"].type = "debris"  # Explicitly set type
    setup["object_ids"]["us_debris"] = neo4j_db.add_object(
        setup["objects"]["us_debris"])

    # RU satellite (Hostile/Red)
    setup["objects"]["ru_satellite"] = create_test_object(
        source_name="ROSCOSMOS",
        metadata={"name": "RU Satellite",
                  "test": "filtered_tracks_test", "object_type": "satellite"},
        country_code="RU",
        common_name="Russia Satellite"
    )
    setup["objects"]["ru_satellite"].type = "satellite"  # Explicitly set type
    setup["object_ids"]["ru_satellite"] = neo4j_db.add_object(
        setup["objects"]["ru_satellite"])

    # Commercial satellite (Neutral/Green)
    setup["objects"]["com_satellite"] = create_test_object(
        source_name="COMMERCIAL",
        metadata={"name": "Commercial Satellite",
                  "test": "filtered_tracks_test", "object_type": "satellite"},
        country_code="LU",  # Luxembourg - neutral
        common_name="Luxembourg Satellite"
    )
    setup["objects"]["com_satellite"].type = "satellite"  # Explicitly set type
    setup["object_ids"]["com_satellite"] = neo4j_db.add_object(
        setup["objects"]["com_satellite"])

    # 2. Create tracks for each object using helper function
    # Track for US Satellite
    setup["tracks"]["us_satellite"] = create_state_vector_track(
        source_name="NASA",
        timestamp=now,
        metadata={"allegiance": "Friendly", "type": "satellite"},
        object_correlations=[
            create_object_correlation(
                object_id=setup["object_ids"]["us_satellite"],
                validity_time=now,
                confidence=0.9,
                metadata={"type": "satellite_correlation"}
            )
        ]
    )
    setup["track_ids"]["us_satellite"] = neo4j_db.add_track(
        setup["tracks"]["us_satellite"])

    # Track for US Debris
    setup["tracks"]["us_debris"] = create_state_vector_track(
        source_name="USSF",
        timestamp=now,
        position=[4.0, 5.0, 6.0],
        velocity=[4.0, 5.0, 6.0],
        metadata={"allegiance": "Friendly", "type": "debris"},
        object_correlations=[
            create_object_correlation(
                object_id=setup["object_ids"]["us_debris"],
                validity_time=now,
                confidence=0.9,
                metadata={"type": "debris_correlation"}
            )
        ]
    )
    setup["track_ids"]["us_debris"] = neo4j_db.add_track(
        setup["tracks"]["us_debris"])

    # Track for RU Satellite
    setup["tracks"]["ru_satellite"] = create_state_vector_track(
        source_name="ROSCOSMOS",
        timestamp=now,
        position=[7.0, 8.0, 9.0],
        velocity=[7.0, 8.0, 9.0],
        metadata={"allegiance": "Hostile", "type": "satellite"},
        object_correlations=[
            create_object_correlation(
                object_id=setup["object_ids"]["ru_satellite"],
                validity_time=now,
                confidence=0.9,
                metadata={"type": "satellite_correlation"}
            )
        ]
    )
    setup["track_ids"]["ru_satellite"] = neo4j_db.add_track(
        setup["tracks"]["ru_satellite"])

    # Track for Commercial Satellite
    setup["tracks"]["com_satellite"] = create_state_vector_track(
        source_name="COMMERCIAL",
        timestamp=now,
        position=[10.0, 11.0, 12.0],
        velocity=[10.0, 11.0, 12.0],
        metadata={"allegiance": "Neutral", "type": "satellite"},
        object_correlations=[
            create_object_correlation(
                object_id=setup["object_ids"]["com_satellite"],
                validity_time=now,
                confidence=0.9,
                metadata={"type": "satellite_correlation"}
            )
        ]
    )
    setup["track_ids"]["com_satellite"] = neo4j_db.add_track(
        setup["tracks"]["com_satellite"])

    # Secondary commercial track correlated with same object but from different source
    setup["tracks"]["second_com"] = create_state_vector_track(
        source_name="GROUND_STATION",
        timestamp=now,
        position=[10.1, 11.1, 12.1],
        velocity=[10.1, 11.1, 12.1],
        metadata={"allegiance": "Neutral",
                  "type": "satellite", "version": "secondary"},
        object_correlations=[
            create_object_correlation(
                object_id=setup["object_ids"]["com_satellite"],
                validity_time=now,
                confidence=0.85,
                metadata={"type": "satellite_correlation",
                          "version": "secondary"}
            )
        ]
    )
    setup["track_ids"]["second_com"] = neo4j_db.add_track(
        setup["tracks"]["second_com"])

    # Store additional useful information
    setup["source_to_allegiance"] = {
        "NASA": "Friendly",
        "USSF": "Friendly",
        "ROSCOSMOS": "Hostile",
        "COMMERCIAL": "Neutral",
        "GROUND_STATION": "Neutral"
    }

    setup["total_tracks"] = 5

    return setup


@pytest.fixture
def test_timestamps():
    """Fixture providing common timestamps for testing"""
    now = datetime.now(timezone.utc)
    return {
        "now": now,
        "older": datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
        "middle": datetime(2022, 1, 15, 12, 0, 0, tzinfo=timezone.utc),
        "newer": datetime(2022, 2, 1, 12, 0, 0, tzinfo=timezone.utc),
        "newest": datetime(2022, 3, 1, 12, 0, 0, tzinfo=timezone.utc)
    }


def pytest_sessionfinish(session):
    """Cleanup at the end of the entire test session.

    This hook is automatically called by pytest after all tests have finished.
    It removes all pytest-* directories from the system temp directory.
    """
    # Get the system temp directory
    if os.name == 'nt':  # Windows
        temp_dir = Path(os.environ.get('TEMP') or os.environ.get(
            'TMP') or r'C:\Windows\Temp')
    else:  # Unix/Linux/MacOS
        temp_dir = Path('/tmp')

    # Look for pytest-* directories and remove them
    try:
        for path in temp_dir.glob('pytest-*'):
            if path.is_dir():
                shutil.rmtree(path, ignore_errors=True)
                print(f"\nCleaned up session directory: {path}")
    except Exception as e:
        print(f"Error cleaning up pytest temporary directories: {e}")
