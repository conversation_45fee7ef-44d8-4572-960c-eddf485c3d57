import pytest
from datetime import datetime, timezone
from omnicat.models import Object, StateVector, Track, TLE, ObjectCorrelation
# Import from the project's main conftest instead of the local one
from tests.conftest import (
    verify_object_properties, verify_track_lists,
    create_test_object, create_state_vector_track, create_object_correlation
)

# Helper functions for object creation


def create_test_object(source_name="TEST_SOURCE", metadata=None, country_code=None, common_name=None):
    """Create a test object with given properties."""
    metadata = metadata or {}
    if "International Designator" not in metadata:
        metadata["International Designator"] = "2023-123A"

    return Object(
        source=source_name,
        type="test_object",
        data_uris=["test_uri1", "test_uri2"],
        metadata=metadata,
        country_code=country_code,
        common_name=common_name
    )


def create_state_vector_track(source_name, timestamp, position=None, velocity=None, metadata=None, object_correlations=None):
    """Create a state vector track with given properties."""
    if position is None:
        position = [1.0, 2.0, 3.0]
    if velocity is None:
        velocity = [1.0, 2.0, 3.0]

    return Track(
        source=source_name,
        format="state_vector",
        data=StateVector(
            timestamp=timestamp,
            position=position,
            velocity=velocity
        ),
        metadata=metadata or {},
        object_correlations=object_correlations
    )


def create_object_correlation(object_id, validity_time, confidence=0.9, metadata=None):
    """Create an object correlation with given properties."""
    return ObjectCorrelation(
        object_id=object_id,
        validity_time=validity_time,
        confidence=confidence,
        metadata=metadata or {}
    )


def verify_object_properties(actual, expected):
    """Verify that the actual object matches the expected object's properties."""
    assert actual.source == expected.source
    assert actual.type == expected.type
    assert set(actual.data_uris) == set(expected.data_uris)

    # Check metadata but ignore internal_id if present in actual object
    actual_metadata = {k: v for k,
                       v in actual.metadata.items() if k != 'internal_id'}
    assert actual_metadata == expected.metadata

    # Check country_code if present in expected
    if hasattr(expected, 'country_code'):
        # If expected country_code is None, Neo4j will set it to 'UNK'
        if expected.country_code is None:
            assert actual.country_code == 'UNK'
        else:
            assert actual.country_code == expected.country_code

    # Don't check allegiance as it's derived from relationships rather than set directly
    # actual.allegiance will be populated from the database relationships,
    # while expected.allegiance (if set) would be the value from the Object constructor


def verify_track_lists(actual_tracks, expected_tracks):
    """Verify that two lists of tracks match."""
    assert len(actual_tracks) == len(expected_tracks)

    for actual, expected in zip(actual_tracks, expected_tracks):
        assert actual == expected

# Helper functions for testing


def setup_test_tracks_with_correlations(neo4j_db, num_sources=2, tracks_per_source=2):
    """
    Create a test environment with tracks from multiple sources.

    Args:
        neo4j_db: Neo4j database instance
        num_sources: Number of sources to create
        tracks_per_source: Number of tracks per source

    Returns:
        dict: Dictionary with test data including object ID, tracks, and sources
    """
    # Create a test object
    test_object = create_test_object()
    object_id = neo4j_db.add_object(test_object)

    now = datetime.now(timezone.utc)

    # Dictionary to store tracks by source
    tracks_by_source = {}
    track_ids_by_source = {}
    sources = []

    for source_idx in range(num_sources):
        source_name = f"TEST_SOURCE_{source_idx}"
        sources.append(source_name)
        tracks = []

        for track_idx in range(tracks_per_source):
            # Create track with correlation for even track indices
            has_correlation = track_idx % 2 == 0

            track = create_state_vector_track(
                source_name=source_name,
                timestamp=now,
                position=[source_idx*10 + track_idx, source_idx *
                          10 + track_idx + 1, source_idx*10 + track_idx + 2],
                metadata={"source_idx": source_idx, "track_idx": track_idx}
            )

            # Add object correlation if needed
            if has_correlation:
                track.object_correlations = [
                    create_object_correlation(
                        object_id=object_id,
                        validity_time=now,
                        confidence=0.9 + (source_idx * 0.01) +
                        (track_idx * 0.001)
                    )
                ]

            tracks.append(track)

        tracks_by_source[source_name] = tracks
        track_ids_by_source[source_name] = neo4j_db.add_tracks(tracks)

    return {
        "object_id": object_id,
        "object": test_object,
        "tracks_by_source": tracks_by_source,
        "track_ids_by_source": track_ids_by_source,
        "sources": sources
    }


def setup_test_objects_with_sources(neo4j_db, num_sources=2, objects_per_source=2, country_codes=None):
    """
    Create a test environment with objects from multiple sources.

    Args:
        neo4j_db: Neo4j database instance
        num_sources: Number of sources to create
        objects_per_source: Number of objects per source
        country_codes: Optional list of country codes to assign to objects.
                     If None, no country codes are set.
                     If a list, it will cycle through these codes for each object.

    Returns:
        dict: Dictionary with test data including objects and sources
    """
    # Dictionary to store objects by source
    objects_by_source = {}
    object_ids_by_source = {}
    # Dictionary to store objects by country
    objects_by_country = {}
    object_ids_by_country = {}
    sources = []

    # Ensure we have at least one None in country_codes if specified
    if country_codes is not None and None not in country_codes:
        # Add None to ensure we create at least one object with unknown country
        country_codes = list(country_codes) + [None]

    for source_idx in range(num_sources):
        source_name = f"TEST_SOURCE_{source_idx}"
        sources.append(source_name)
        objects = []

        for obj_idx in range(objects_per_source):
            # Assign country code if provided
            country_code = None
            if country_codes:
                country_code = country_codes[obj_idx % len(country_codes)]

            # Create object with different metadata for each
            obj = create_test_object(
                source_name=source_name,
                metadata={
                    "source_idx": source_idx,
                    "obj_idx": obj_idx,
                    "International Designator": f"2023-{source_idx}{obj_idx}X"
                },
                country_code=country_code
            )
            objects.append(obj)

            # Add to country-based collections
            country_key = country_code if country_code else "UNK"
            if country_key not in objects_by_country:
                objects_by_country[country_key] = []
                object_ids_by_country[country_key] = []
            objects_by_country[country_key].append(obj)

        objects_by_source[source_name] = objects
        object_ids_by_source[source_name] = neo4j_db.add_objects(objects)

        # Update object IDs by country after adding to database
        for obj_idx, obj_id in enumerate(object_ids_by_source[source_name]):
            country_code = None
            if country_codes:
                country_code = country_codes[obj_idx % len(country_codes)]
            country_key = country_code if country_code else "UNK"
            if country_key in object_ids_by_country:
                object_ids_by_country[country_key].append(obj_id)

    return {
        "objects_by_source": objects_by_source,
        "object_ids_by_source": object_ids_by_source,
        "objects_by_country": objects_by_country,
        "object_ids_by_country": object_ids_by_country,
        "sources": sources
    }

# Enhanced helper functions to reduce duplication


def create_test_object_with_type(source_name="TEST_SOURCE", object_type="satellite", country_code=None, metadata=None, common_name=None):
    """
    Create a test object with specified type.

    Args:
        source_name: Source name for the object
        object_type: Type of the object (satellite, debris, etc.)
        country_code: Country code for the object
        metadata: Additional metadata for the object
        common_name: Common name for the object

    Returns:
        Object: Created object with the specified type
    """
    base_metadata = {"object_type": object_type}
    if metadata:
        base_metadata.update(metadata)

    obj = create_test_object(
        source_name=source_name,
        metadata=base_metadata,
        country_code=country_code,
        common_name=common_name
    )
    # Explicitly set the type field
    obj.type = object_type
    return obj


def create_track_with_correlation(source_name, object_id=None, timestamp=None, position=None,
                                  velocity=None, confidence=0.9, metadata=None, correlation_metadata=None):
    """
    Create a track with optional correlation to an object.

    Args:
        source_name: Source name for the track
        object_id: Optional object ID to correlate with
        timestamp: Time for the state vector
        position: Position vector [x, y, z]
        velocity: Velocity vector [vx, vy, vz]
        confidence: Confidence value for the correlation
        metadata: Track metadata
        correlation_metadata: Metadata for the correlation

    Returns:
        Track: Created track with optional correlation
    """
    if timestamp is None:
        timestamp = datetime.now(timezone.utc)

    if position is None:
        position = [1.0, 2.0, 3.0]

    if velocity is None:
        velocity = [1.0, 2.0, 3.0]

    track = create_state_vector_track(
        source_name=source_name,
        timestamp=timestamp,
        position=position,
        velocity=velocity,
        metadata=metadata or {}
    )

    if object_id:
        track.object_correlations = [
            create_object_correlation(
                object_id=object_id,
                validity_time=timestamp,
                confidence=confidence,
                metadata=correlation_metadata or {}
            )
        ]

    return track
