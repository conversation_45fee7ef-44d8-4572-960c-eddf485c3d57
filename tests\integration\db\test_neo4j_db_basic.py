import pytest
from datetime import datetime, timezone
from omnicat.db import Neo4jDb
from omnicat.models import Object, StateVector, Track, TLE
from tests.integration.db.neo4j_test_helpers import (
    verify_object_properties, verify_track_lists,
    create_test_object, create_state_vector_track
)


class TestBasicOperations:
    def test_object_crud(self, neo4j_db: Neo4jDb, sample_object: Object):
        # Create - Test storing the object
        obj_id = neo4j_db.add_object(sample_object)

        # Read - Test retrieving by ID
        retrieved_obj = neo4j_db.get_object(obj_id)
        assert retrieved_obj is not None
        verify_object_properties(retrieved_obj, sample_object)

        # Read - Test retrieving by International Designator
        retrieved_by_intl = neo4j_db.get_object("2023-123A")
        assert retrieved_by_intl is not None
        verify_object_properties(retrieved_by_intl, sample_object)

        # Verify non-existent object returns None
        assert neo4j_db.get_object("nonexistent-id") is None

    def test_objects_crud(self, neo4j_db: Neo4jDb, sample_object: Object):
        # Prepare test data
        objects = [
            Object(
                source="TEST_SOURCE_1",
                type="test_object",
                data_uris=["uri1", "uri2"],
                metadata={
                    "key1": "value1",
                    "International Designator": "2023-123A"
                },
                common_name="Object One"
            ),
            Object(
                source="TEST_SOURCE_2",
                type="test_object",
                data_uris=["uri3", "uri4"],
                metadata={
                    "key2": "value2",
                    "International Designator": "2023-456B"
                },
                common_name="Object Two"
            ),
            Object(
                source="TEST_SOURCE_3",
                type="test_object",
                data_uris=["uri5", "uri6"],
                metadata={
                    "key3": "value3",
                    "International Designator": "2023-789C"
                },
                common_name="Object Three"
            )
        ]

        # Add objects in bulk
        obj_ids = neo4j_db.add_objects(objects)

        # Verify we got the correct number of IDs back
        assert len(obj_ids) == 3
        assert all(id.startswith("OBJ-") for id in obj_ids)

        # Verify all objects were stored correctly using get_objects
        retrieved_objects = neo4j_db.get_objects(obj_ids)
        assert len(retrieved_objects) == 3
        for i, retrieved_obj in enumerate(retrieved_objects):
            verify_object_properties(retrieved_obj, objects[i])
            # Additionally check that common_name is preserved
            assert retrieved_obj.common_name == objects[i].common_name

        # Verify retrieving by International Designator
        retrieved_by_intl = neo4j_db.get_object("2023-456B")
        assert retrieved_by_intl is not None
        verify_object_properties(retrieved_by_intl, objects[1])
        assert retrieved_by_intl.common_name == "Object Two"

        # Test bulk retrieval by International Designators
        intl_designators = ["2023-123A", "2023-456B", "2023-789C"]
        retrieved_by_intl_bulk = neo4j_db.get_objects(intl_designators)
        assert len(retrieved_by_intl_bulk) == 3
        for i, retrieved_obj in enumerate(retrieved_by_intl_bulk):
            verify_object_properties(retrieved_obj, objects[i])
            assert retrieved_obj.common_name == objects[i].common_name

    def test_track_crud(self, neo4j_db: Neo4jDb, sample_track: Track):
        # Create
        track_id = neo4j_db.add_track(sample_track)

        # Read
        retrieved_track = neo4j_db.get_track(track_id)

        assert retrieved_track == sample_track

    def test_tracks_crud(self, neo4j_db: Neo4jDb):
        # Prepare test data
        now = datetime.now(timezone.utc)

        # Create a mix of StateVector and TLE tracks using helper methods
        tracks = [
            create_state_vector_track(
                source_name="TEST_SOURCE_1",
                timestamp=now,
                metadata={"test": "metadata1"}
            ),
            Track(
                source="TEST_SOURCE_2",
                format="tle",
                data=TLE(
                    line1="1 25544U 98067A   08264.51782528 -.00002182  00000-0 -11606-4 0  2927",
                    line2="2 25544  51.6416 247.4627 0006703 130.5360 325.0288 15.72125391563537"
                ),
                metadata={"test": "metadata2"}
            )
        ]

        # Add tracks in bulk
        track_ids = neo4j_db.add_tracks(tracks)

        # Verify we got the correct number of IDs back
        assert len(track_ids) == 2
        assert all(id.startswith("TRK-") for id in track_ids)

        # Verify all tracks were stored correctly using get_tracks
        stored_tracks = neo4j_db.get_tracks(track_ids)
        verify_track_lists(stored_tracks, tracks)

    def test_get_all_track_ids(self, neo4j_db: Neo4jDb):
        # Initially, there should be no tracks
        initial_tracks = neo4j_db.get_all_track_ids()
        assert len(initial_tracks) == 0

        # Add some tracks
        now = datetime.now(timezone.utc)
        tracks = [
            Track(
                source="SOURCE_1",
                format="state_vector",
                data=StateVector(
                    timestamp=now,
                    position=[1.0, 2.0, 3.0],
                    velocity=[1.0, 2.0, 3.0]
                )
            ),
            Track(
                source="SOURCE_2",
                format="state_vector",
                data=StateVector(
                    timestamp=now,
                    position=[4.0, 5.0, 6.0],
                    velocity=[4.0, 5.0, 6.0]
                )
            )
        ]

        # Add tracks in bulk
        track_ids = neo4j_db.add_tracks(tracks)

        # Get all track IDs
        retrieved_track_ids = neo4j_db.get_all_track_ids()

        # Verify we got all tracks
        assert len(retrieved_track_ids) == len(tracks)

        # Verify all our added track IDs are in the retrieved list
        for track_id in track_ids:
            assert track_id in retrieved_track_ids

    def test_get_all_objects(self, neo4j_db: Neo4jDb):
        """Test retrieving all objects from the database."""
        # Initially, there should be no objects or an empty list
        initial_objects = neo4j_db.get_all_objects()
        initial_count = len(initial_objects)

        # Create and add test objects
        test_objects = [
            Object(
                source="TEST_SOURCE_1",
                type="satellite",
                data_uris=["test_uri1", "test_uri2"],
                metadata={"key1": "value1", "object_type": "satellite"}
            ),
            Object(
                source="TEST_SOURCE_2",
                type="debris",
                data_uris=["test_uri3", "test_uri4"],
                metadata={"key2": "value2", "object_type": "debris"}
            ),
            Object(
                source="TEST_SOURCE_3",
                type="satellite",
                data_uris=["test_uri5", "test_uri6"],
                metadata={"key3": "value3", "object_type": "satellite"}
            )
        ]

        # Add objects to the database
        added_ids = neo4j_db.add_objects(test_objects)
        assert len(added_ids) == 3

        # Get all objects
        all_objects = neo4j_db.get_all_objects()

        # Verify we got the expected number of objects
        assert len(all_objects) == initial_count + 3

        # Verify each added object is in the retrieved list
        found_ids = [
            obj.internal_id for obj in all_objects if obj.internal_id is not None]
        for obj_id in added_ids:
            assert obj_id in found_ids

        # Verify object attributes are correctly set
        for obj in all_objects:
            if obj.internal_id in added_ids:
                index = added_ids.index(obj.internal_id)
                verify_object_properties(obj, test_objects[index])

    def test_object_common_name(self, neo4j_db: Neo4jDb):
        """Test storing and retrieving objects with common_name field."""
        # Create test objects with different common names
        test_objects = [
            Object(
                source="TEST_SOURCE",
                type="satellite",
                data_uris=["test_uri1"],
                metadata={"key1": "value1"},
                common_name="ISS"
            ),
            Object(
                source="TEST_SOURCE",
                type="debris",
                data_uris=["test_uri2"],
                metadata={"key2": "value2"},
                common_name="Cosmos Debris"
            ),
            Object(
                source="TEST_SOURCE",
                type="rocket_body",
                data_uris=["test_uri3"],
                metadata={"key3": "value3"},
                common_name=None  # Test with null common_name
            )
        ]

        # Add objects to the database
        object_ids = neo4j_db.add_objects(test_objects)
        assert len(object_ids) == 3

        # Retrieve and verify each object
        for i, obj_id in enumerate(object_ids):
            obj = neo4j_db.get_object(obj_id)
            assert obj is not None
            assert obj.common_name == test_objects[i].common_name

        # Test get_all_objects includes common_name
        all_objects = neo4j_db.get_all_objects()
        assert len(all_objects) >= 3

        # Find our test objects in the retrieved list
        for test_obj in test_objects:
            matching_objs = [obj for obj in all_objects
                             if obj.data_uris == test_obj.data_uris and obj.metadata == test_obj.metadata]
            assert len(matching_objs) == 1
            assert matching_objs[0].common_name == test_obj.common_name

    def test_object_allegiance_field(self, neo4j_db: Neo4jDb, allegiance_test_setup):
        """Test that the allegiance field is properly populated when retrieving objects."""
        setup = allegiance_test_setup

        # Test US Object (should have Blue allegiance)
        us_obj_id = setup["object_ids"]["us_satellite"]
        us_obj = neo4j_db.get_object(us_obj_id)
        assert us_obj is not None
        assert us_obj.allegiance == "Blue"

        # Test Russian Object (should have Red allegiance)
        ru_obj_id = setup["object_ids"]["ru_satellite"]
        ru_obj = neo4j_db.get_object(ru_obj_id)
        assert ru_obj is not None
        assert ru_obj.allegiance == "Red"

        # Test Brazilian Object (should have Green allegiance)
        br_obj_id = setup["object_ids"]["br_satellite"]
        br_obj = neo4j_db.get_object(br_obj_id)
        assert br_obj is not None
        assert br_obj.allegiance == "Green"

        # Test retrieving all objects - allegiance field should be populated for all
        all_objects = neo4j_db.get_all_objects()

        # Find our test objects in the results
        test_objects = {
            "us": next((obj for obj in all_objects if obj.internal_id == us_obj_id), None),
            "ru": next((obj for obj in all_objects if obj.internal_id == ru_obj_id), None),
            "br": next((obj for obj in all_objects if obj.internal_id == br_obj_id), None)
        }

        # Verify allegiances in bulk retrieval
        assert test_objects["us"].allegiance == "Blue"
        assert test_objects["ru"].allegiance == "Red"
        assert test_objects["br"].allegiance == "Green"

    def test_object_allegiance_ignored_when_adding(self, neo4j_db: Neo4jDb):
        """
        Test that providing an allegiance field directly in the Object is ignored when adding to the database,
        and the allegiance is determined by the Country -> Allegiance relationship instead.
        """
        # Create an object with US country code but explicitly setting allegiance
        test_obj = Object(
            source="TEST_SOURCE",
            type="satellite",
            data_uris=["test_uri1", "test_uri2"],
            metadata={"key": "value"},
            country_code="US",  # US is mapped to Blue allegiance
            allegiance="Purple"  # This should be ignored - not used when adding objects
        )

        # Add the object
        obj_id = neo4j_db.add_object(test_obj)

        # Retrieve the object
        retrieved_obj = neo4j_db.get_object(obj_id)
        assert retrieved_obj is not None

        # The allegiance should be "Blue" from the country relationship, not "Purple"
        assert retrieved_obj.allegiance == "Blue"
        assert retrieved_obj.allegiance != "Purple"

    def test_object_with_no_country_allegiance(self, neo4j_db: Neo4jDb):
        """
        Test that objects with no country code get the correct allegiance (Gray).
        """
        # Create an object with no country code
        test_obj = Object(
            source="TEST_SOURCE",
            type="satellite",
            data_uris=["test_uri1", "test_uri2"],
            metadata={"key": "value"},
            country_code=None  # No country code
        )

        # Add the object
        obj_id = neo4j_db.add_object(test_obj)

        # Retrieve the object
        retrieved_obj = neo4j_db.get_object(obj_id)
        assert retrieved_obj is not None

        # The country_code should be set to "UNK"
        assert retrieved_obj.country_code == "UNK"

        # The allegiance should be "Gray" for unknown countries
        assert retrieved_obj.allegiance == "Gray"

    def test_get_objects_edge_cases(self, neo4j_db: Neo4jDb):
        """Test get_objects function with edge cases"""
        # Test with empty list
        empty_result = neo4j_db.get_objects([])
        assert empty_result == []

        # Create a test object first
        test_obj = Object(
            source="TEST_SOURCE",
            type="test_object",
            data_uris=["uri1"],
            metadata={"International Designator": "2024-001A"},
            common_name="Test Object"
        )
        obj_id = neo4j_db.add_object(test_obj)

        # Test with non-existent identifiers
        non_existent_result = neo4j_db.get_objects(
            ["NON-EXISTENT-ID", "2099-999Z"])
        assert non_existent_result == []

        # Test with mixed valid and invalid identifiers
        mixed_identifiers = [obj_id, "NON-EXISTENT-ID", "2024-001A"]
        mixed_result = neo4j_db.get_objects(mixed_identifiers)
        # Should return 2 objects (the same object found by both ID and International Designator)
        assert len(mixed_result) == 2
        assert mixed_result[0].internal_id == obj_id  # Found by ID
        # Found by International Designator
        assert mixed_result[1].internal_id == obj_id

        # Test with duplicate identifiers
        duplicate_identifiers = [obj_id, obj_id, "2024-001A"]
        duplicate_result = neo4j_db.get_objects(duplicate_identifiers)
        # Should return 3 objects (duplicates included, but same object data)
        assert len(duplicate_result) == 3
        for retrieved_obj in duplicate_result:
            assert retrieved_obj.internal_id == obj_id
