import pytest
from datetime import datetime, timezone
from omnicat.db import Neo4jDb
from omnicat.models import Object, Track, ObjectCorrelation, StateVector
from tests.integration.db.neo4j_test_helpers import (
    create_track_with_correlation, create_test_object
)


class TestCorrelations:
    def test_tracks_with_correlations_crud(self, neo4j_db: Neo4jDb, sample_object: Object):
        """Test adding multiple tracks with correlations and verifying they can be retrieved correctly."""
        # First add an object and state that we'll correlate tracks with
        object_id = neo4j_db.add_object(sample_object)

        # Prepare current timestamp for correlations
        now = datetime.now(timezone.utc)

        # Create tracks with correlations using helper methods
        tracks = [
            # Track with object correlation
            create_track_with_correlation(
                source_name="SOURCE_1",
                object_id=object_id,
                timestamp=now,
                metadata={"track_type": "with_object_correlation"},
                correlation_metadata={"correlation_type": "test_object"}
            ),
            # Track with no correlation
            create_track_with_correlation(
                source_name="SOURCE_2",
                timestamp=now,
                position=[4.0, 5.0, 6.0],
                velocity=[4.0, 5.0, 6.0],
                metadata={"track_type": "with_no_correlation"}
            )
        ]

        # Add tracks with correlations in bulk
        track_ids = neo4j_db.add_tracks(tracks)
        assert len(track_ids) == len(tracks)

        # Retrieve tracks and verify their correlations
        retrieved_tracks = neo4j_db.get_tracks(track_ids)
        assert len(retrieved_tracks) == len(tracks)

        # Verify each track has the correct correlations
        for track, original_track in zip(retrieved_tracks, tracks):
            # Basic track data verification
            assert track == original_track

            # Verify object correlations if present
            if original_track.object_correlations:
                assert track.object_correlations is not None
                assert len(track.object_correlations) == len(
                    original_track.object_correlations)

                for assoc, orig_assoc in zip(track.object_correlations, original_track.object_correlations):
                    assert assoc.object_id == orig_assoc.object_id
                    assert assoc.confidence == orig_assoc.confidence
                    assert assoc.metadata == orig_assoc.metadata

    def test_tracks_with_duplicate_data_and_source(self, neo4j_db: Neo4jDb, sample_object: Object):
        """Test adding tracks with duplicate data and source to verify only one node is created."""
        # First add an object and state that we'll correlate tracks with
        object_id = neo4j_db.add_object(sample_object)

        # Prepare current timestamp
        now = datetime.now(timezone.utc)

        # Create a track
        original_track = Track(
            source="TEST_DUPLICATE_SOURCE",
            format="state_vector",
            data=StateVector(
                timestamp=now,
                position=[1.0, 2.0, 3.0],
                velocity=[1.0, 2.0, 3.0]
            ),
            metadata={"test": "metadata1"},
            object_correlations=[
                ObjectCorrelation(
                    object_id=object_id,
                    validity_time=now,
                    confidence=0.9,
                    metadata={"type": "initial"}
                )
            ]
        )

        # Add the track and keep the ID
        original_track_id = neo4j_db.add_track(original_track)
        assert original_track_id is not None

        # Create another track with identical data and source but different correlations
        duplicate_track = Track(
            source="TEST_DUPLICATE_SOURCE",  # Same source
            format="state_vector",
            data=StateVector(  # Same data
                timestamp=now,
                position=[1.0, 2.0, 3.0],
                velocity=[1.0, 2.0, 3.0]
            ),
            # Different metadata, but this doesn't affect duplicity check
            metadata={"test": "metadata2"},
            object_correlations=[
                ObjectCorrelation(
                    object_id=object_id,
                    validity_time=now,
                    confidence=0.95,
                    metadata={"type": "additional"}
                )
            ]
        )

        # Add the duplicate track
        duplicate_track_ids = neo4j_db.add_tracks([duplicate_track])
        assert len(duplicate_track_ids) == 1
        duplicate_track_id = duplicate_track_ids[0]

        # The ID should match the original since the node shouldn't be created again
        assert duplicate_track_id == original_track_id

        # Retrieve the track to verify it has all correlations
        retrieved_track = neo4j_db.get_track(original_track_id)
        assert retrieved_track is not None

        # Verify basic properties
        assert retrieved_track.source == "TEST_DUPLICATE_SOURCE"
        assert retrieved_track.format == "state_vector"

        # Verify object correlations - there should be 2 now (one from original, one from duplicate)
        assert retrieved_track.object_correlations is not None
        assert len(retrieved_track.object_correlations) == 2

        # Sort correlations by confidence to ensure consistent comparison order
        sorted_obj_assocs = sorted(
            retrieved_track.object_correlations, key=lambda x: x.confidence)

        # First correlation (from original track)
        assert sorted_obj_assocs[0].object_id == object_id
        assert sorted_obj_assocs[0].confidence == 0.9
        assert sorted_obj_assocs[0].metadata == {"type": "initial"}

        # Second correlation (from duplicate track)
        assert sorted_obj_assocs[1].object_id == object_id
        assert sorted_obj_assocs[1].confidence == 0.95
        assert sorted_obj_assocs[1].metadata == {"type": "additional"}

        # Now test with a track that has the same data but different source
        different_source_track = Track(
            source="DIFFERENT_SOURCE",  # Different source
            format="state_vector",
            data=StateVector(  # Same data
                timestamp=now,
                position=[1.0, 2.0, 3.0],
                velocity=[1.0, 2.0, 3.0]
            ),
            metadata={"test": "metadata3"}
        )

        # This should create a new track since source is different
        different_source_ids = neo4j_db.add_tracks([different_source_track])
        assert len(different_source_ids) == 1
        different_source_id = different_source_ids[0]

        # ID should be different
        assert different_source_id != original_track_id

        # Add multiple tracks with mix of duplicates and new tracks
        track1 = Track(
            source="TEST_DUPLICATE_SOURCE",  # Same as original
            format="state_vector",
            data=StateVector(  # Same as original
                timestamp=now,
                position=[1.0, 2.0, 3.0],
                velocity=[1.0, 2.0, 3.0]
            )
        )

        track2 = Track(
            source="NEW_SOURCE",
            format="state_vector",
            data=StateVector(
                timestamp=now,
                position=[4.0, 5.0, 6.0],
                velocity=[4.0, 5.0, 6.0]
            )
        )

        batch_ids = neo4j_db.add_tracks([track1, track2])
        assert len(batch_ids) == 2

        # First ID should match original
        assert batch_ids[0] == original_track_id
        # Second ID should be new
        assert batch_ids[1] != original_track_id
        assert batch_ids[1] != different_source_id

        # Count total tracks - should be 3 (original/duplicate, different source, and new source track)
        all_track_ids = neo4j_db.get_all_track_ids()
        assert len(all_track_ids) == 3

    def test_duplicate_correlations_are_ignored(self, neo4j_db: Neo4jDb, sample_object: Object):
        """Test that duplicate correlations are ignored when a duplicate track is added."""
        # First add an object and state that we'll correlate tracks with
        object_id = neo4j_db.add_object(sample_object)

        # Prepare current timestamp
        now = datetime.now(timezone.utc)

        # Create a track with specific correlations
        original_track = Track(
            source="TEST_ASSOCIATION_SOURCE",
            format="state_vector",
            data=StateVector(
                timestamp=now,
                position=[1.0, 2.0, 3.0],
                velocity=[1.0, 2.0, 3.0]
            ),
            metadata={"test": "metadata1"},
            object_correlations=[
                ObjectCorrelation(
                    object_id=object_id,
                    validity_time=now,
                    confidence=0.9,
                    metadata={"type": "original"}
                )
            ]
        )

        # Add the track and keep the ID
        original_track_id = neo4j_db.add_track(original_track)
        assert original_track_id is not None

        # Get the track and verify the correlations
        retrieved_track = neo4j_db.get_track(original_track_id)
        assert retrieved_track is not None
        assert len(retrieved_track.object_correlations) == 1

        # Create a duplicate track with the same correlations (should be ignored)
        duplicate_track = Track(
            source="TEST_ASSOCIATION_SOURCE",  # Same source
            format="state_vector",
            data=StateVector(  # Same data
                timestamp=now,
                position=[1.0, 2.0, 3.0],
                velocity=[1.0, 2.0, 3.0]
            ),
            metadata={"test": "metadata2"},
            object_correlations=[
                ObjectCorrelation(  # Same correlation (object, validity_time, confidence)
                    object_id=object_id,
                    validity_time=now,
                    confidence=0.9,  # Same confidence
                    # Different metadata, but this shouldn't matter
                    metadata={"type": "duplicate"}
                )
            ]
        )

        # Add the duplicate track - should return the original track ID
        duplicate_track_ids = neo4j_db.add_tracks([duplicate_track])
        assert len(duplicate_track_ids) == 1
        duplicate_track_id = duplicate_track_ids[0]
        assert duplicate_track_id == original_track_id

        # Retrieve the track and verify correlations count didn't increase
        retrieved_track = neo4j_db.get_track(original_track_id)
        assert retrieved_track is not None

        # Should still only have 1 object correlation (duplicate was ignored)
        assert len(retrieved_track.object_correlations) == 1
        assert retrieved_track.object_correlations[0].object_id == object_id
        assert retrieved_track.object_correlations[0].confidence == 0.9
        # Metadata should be from the original correlation, not the duplicate
        assert retrieved_track.object_correlations[0].metadata == {
            "type": "original"}

        # Now add another duplicate with a different confidence (should create new correlation)
        different_confidence_track = Track(
            source="TEST_ASSOCIATION_SOURCE",  # Same source
            format="state_vector",
            data=StateVector(  # Same data
                timestamp=now,
                position=[1.0, 2.0, 3.0],
                velocity=[1.0, 2.0, 3.0]
            ),
            metadata={"test": "metadata3"},
            object_correlations=[
                ObjectCorrelation(
                    object_id=object_id,
                    validity_time=now,
                    confidence=0.95,  # Different confidence
                    metadata={"type": "new_confidence"}
                )
            ]
        )

        # Add the track with different confidence correlations
        neo4j_db.add_tracks([different_confidence_track])

        # Retrieve the track and verify the new correlations were added
        retrieved_track = neo4j_db.get_track(original_track_id)
        assert retrieved_track is not None

        # Should now have 2 object correlations
        assert len(retrieved_track.object_correlations) == 2

        # Sort by confidence to ensure consistent test results
        sorted_obj_assocs = sorted(
            retrieved_track.object_correlations, key=lambda x: x.confidence)
        assert sorted_obj_assocs[0].confidence == 0.9
        assert sorted_obj_assocs[0].metadata == {"type": "original"}
        assert sorted_obj_assocs[1].confidence == 0.95
        assert sorted_obj_assocs[1].metadata == {"type": "new_confidence"}

    def test_direct_correlation_methods_handle_duplicates(self, neo4j_db: Neo4jDb, sample_object: Object):
        """Test that correlate_track_with_object and correlate_track_with_state ignore duplicate correlations."""
        # First add an object and state that we'll correlate tracks with
        object_id = neo4j_db.add_object(sample_object)

        # Create a track
        track = Track(
            source="TEST_DIRECT_ASSOCIATION",
            format="state_vector",
            data=StateVector(
                timestamp=datetime.now(timezone.utc),
                position=[1.0, 2.0, 3.0],
                velocity=[1.0, 2.0, 3.0]
            ),
            metadata={"test": "metadata1"}
        )

        # Add the track and get its ID
        track_id = neo4j_db.add_track(track)
        assert track_id is not None

        # Prepare timestamp for correlations
        now = datetime.now(timezone.utc)

        # Create object correlation
        object_correlation = ObjectCorrelation(
            object_id=object_id,
            validity_time=now,
            confidence=0.9,
            metadata={"type": "original"}
        )

        # Correlate the track with the object
        neo4j_db.correlate_track_with_object(track_id, object_correlation)

        # Retrieve the track to verify the correlations
        retrieved_track = neo4j_db.get_track(track_id)
        assert retrieved_track is not None
        assert len(retrieved_track.object_correlations) == 1

        # Verify the correlation properties
        assert retrieved_track.object_correlations[0].object_id == object_id
        assert retrieved_track.object_correlations[0].confidence == 0.9
        assert retrieved_track.object_correlations[0].metadata == {
            "type": "original"}

        # Now try to correlate the same track with the same object again
        # but with different metadata
        duplicate_object_correlation = ObjectCorrelation(
            object_id=object_id,
            validity_time=now,  # Same validity_time
            confidence=0.9,     # Same confidence
            metadata={"type": "duplicate"}  # Different metadata
        )

        # Correlate again with the same values
        neo4j_db.correlate_track_with_object(
            track_id, duplicate_object_correlation)

        # Retrieve the track again to verify no new correlations were created
        retrieved_track = neo4j_db.get_track(track_id)
        assert retrieved_track is not None

        # Should still have only 1 object correlation
        assert len(retrieved_track.object_correlations) == 1
        assert retrieved_track.object_correlations[0].object_id == object_id
        assert retrieved_track.object_correlations[0].confidence == 0.9
        # Metadata should still be from the original correlation
        assert retrieved_track.object_correlations[0].metadata == {
            "type": "original"}

        # Now try with different confidence (should create new correlations)
        different_confidence_object = ObjectCorrelation(
            object_id=object_id,
            validity_time=now,
            confidence=0.95,  # Different confidence
            metadata={"type": "new_confidence"}
        )

        # Correlate with different confidence
        neo4j_db.correlate_track_with_object(
            track_id, different_confidence_object)

        # Retrieve the track again to verify new correlations were created
        retrieved_track = neo4j_db.get_track(track_id)
        assert retrieved_track is not None

        # Now should have 2 object correlations
        assert len(retrieved_track.object_correlations) == 2

        # Sort by confidence for consistent testing
        sorted_obj_assocs = sorted(
            retrieved_track.object_correlations, key=lambda x: x.confidence)
        assert sorted_obj_assocs[0].confidence == 0.9
        assert sorted_obj_assocs[0].metadata == {"type": "original"}
        assert sorted_obj_assocs[1].confidence == 0.95
        assert sorted_obj_assocs[1].metadata == {"type": "new_confidence"}
        
    def test_get_tracks_for_object(self, neo4j_db: Neo4jDb):
        """Test retrieving all tracks correlated with a specific object."""
        # Create a test object
        test_object = Object(
            source="TEST_SOURCE",
            type="test_object",
            data_uris=["track_test_uri1", "track_test_uri2"],
            metadata={"key": "value"}
        )

        # Add the object to the database
        object_id = neo4j_db.add_object(test_object)
        assert object_id is not None

        # Initially, there should be no tracks for this object
        initial_tracks = neo4j_db.get_tracks_for_object(object_id)
        assert len(initial_tracks) == 0

        # Create timestamps for tracks
        now = datetime.now(timezone.utc)

        # Create tracks correlated with this object
        tracks_with_object = [
            Track(
                source="SOURCE_1",
                format="state_vector",
                data=StateVector(
                    timestamp=now,
                    position=[1.0, 2.0, 3.0],
                    velocity=[1.0, 2.0, 3.0]
                ),
                metadata={"track_type": "correlated"},
                object_correlations=[
                    ObjectCorrelation(
                        object_id=object_id,
                        validity_time=now,
                        confidence=0.9,
                        metadata={"correlation_type": "test"}
                    )
                ]
            ),
            Track(
                source="SOURCE_2",
                format="state_vector",
                data=StateVector(
                    timestamp=now,
                    position=[4.0, 5.0, 6.0],
                    velocity=[4.0, 5.0, 6.0]
                ),
                metadata={"track_type": "correlated"},
                object_correlations=[
                    ObjectCorrelation(
                        object_id=object_id,
                        validity_time=now,
                        confidence=0.95,
                        metadata={"correlation_type": "test"}
                    )
                ]
            )
        ]

        # Create a track not correlated with this object
        track_without_object = Track(
            source="SOURCE_3",
            format="state_vector",
            data=StateVector(
                timestamp=now,
                position=[7.0, 8.0, 9.0],
                velocity=[7.0, 8.0, 9.0]
            ),
            metadata={"track_type": "not_correlated"}
            # No object_correlations
        )

        # Add all tracks to the database
        correlated_track_ids = neo4j_db.add_tracks(tracks_with_object)
        uncorrelated_track_id = neo4j_db.add_track(track_without_object)

        # Get tracks for the object
        retrieved_tracks = neo4j_db.get_tracks_for_object(object_id)

        # Verify we got the correct number of tracks
        assert len(retrieved_tracks) == 2

        # Verify we only got the correlated tracks
        retrieved_track_ids = [track.source for track in retrieved_tracks]
        assert "SOURCE_1" in retrieved_track_ids
        assert "SOURCE_2" in retrieved_track_ids
        assert "SOURCE_3" not in retrieved_track_ids

        # Verify track properties
        for track in retrieved_tracks:
            assert track.object_correlations is not None
            assert len(track.object_correlations) == 1
            assert track.object_correlations[0].object_id == object_id

    def test_get_track_ids_for_object(self, neo4j_db: Neo4jDb):
        """Test retrieving all track IDs correlated with a specific object."""
        # Create a test object
        test_object = create_test_object(
            source_name="TEST_SOURCE",
            metadata={"test": "track_ids_test"}
        )
        object_id = neo4j_db.add_object(test_object)
        
        # Initially, there should be no tracks for this object
        initial_track_ids = neo4j_db.get_track_ids_for_object(object_id)
        assert len(initial_track_ids) == 0
        
        # Create and add tracks with correlations to this object
        now = datetime.now(timezone.utc)
        num_tracks = 3
        tracks = []
        
        for i in range(num_tracks):
            track = create_track_with_correlation(
                source_name=f"SOURCE_{i}",
                object_id=object_id,
                timestamp=now,
                position=[i*10.0, i*10.0+1, i*10.0+2],
                velocity=[i*10.0+3, i*10.0+4, i*10.0+5],
                metadata={"track_index": i}
            )
            tracks.append(track)
        
        # Add tracks to the database
        added_track_ids = neo4j_db.add_tracks(tracks)
        assert len(added_track_ids) == num_tracks
        
        # Get track IDs for the object
        retrieved_track_ids = neo4j_db.get_track_ids_for_object(object_id)
        
        # Verify we got the correct number of track IDs
        assert len(retrieved_track_ids) == num_tracks
        
        # Verify all track IDs were retrieved
        for track_id in added_track_ids:
            assert track_id in retrieved_track_ids
            
    def test_get_non_current_track_ids(self, neo4j_db: Neo4jDb, test_timestamps):
        """Test the get_non_current_track_ids function returns only older track IDs."""
        # Create a test object
        test_object = create_test_object(
            source_name="TEST_SOURCE",
            metadata={"test": "non_current_track_test"}
        )

        # Add the object to the database
        object_id = neo4j_db.add_object(test_object)
        assert object_id is not None

        # Create tracks with different timestamps using the test_timestamps fixture
        older_track = create_track_with_correlation(
            source_name="SOURCE_1",
            object_id=object_id,
            timestamp=test_timestamps["older"],
            metadata={"track_type": "older"},
            correlation_metadata={"correlation_type": "older"}
        )

        middle_track = create_track_with_correlation(
            source_name="SOURCE_2",
            object_id=object_id,
            timestamp=test_timestamps["middle"],
            position=[2.0, 3.0, 4.0],
            velocity=[2.0, 3.0, 4.0],
            metadata={"track_type": "middle"},
            correlation_metadata={"correlation_type": "middle"}
        )

        newer_track = create_track_with_correlation(
            source_name="SOURCE_3",
            object_id=object_id,
            timestamp=test_timestamps["newer"],
            position=[4.0, 5.0, 6.0],
            velocity=[4.0, 5.0, 6.0],
            metadata={"track_type": "newer"},
            correlation_metadata={"correlation_type": "newer"}
        )

        # Create a track with no object correlations
        no_correlation_track = create_track_with_correlation(
            source_name="SOURCE_4",
            timestamp=test_timestamps["newest"],
            position=[7.0, 8.0, 9.0],
            velocity=[7.0, 8.0, 9.0],
            metadata={"track_type": "no_correlation"}
            # No object_id provided, so no correlation will be created
        )

        # Add all tracks to the database
        track_ids = neo4j_db.add_tracks(
            [older_track, middle_track, newer_track, no_correlation_track])
        older_track_id = track_ids[0]
        middle_track_id = track_ids[1]
        newer_track_id = track_ids[2]
        no_correlation_track_id = track_ids[3]

        # Get all non-current track IDs
        non_current_track_ids = neo4j_db.get_non_current_track_ids()

        # There should be exactly two non-current track IDs
        assert len(non_current_track_ids) == 2

        # Both the older and middle track IDs should be in the results
        assert older_track_id in non_current_track_ids
        assert middle_track_id in non_current_track_ids

        # The newer track ID should not be in the result
        assert newer_track_id not in non_current_track_ids

        # The track with no object correlation should not be in the result
        assert no_correlation_track_id not in non_current_track_ids
        
    def test_objects_with_duplicates(self, neo4j_db: Neo4jDb):
        """Test adding objects with duplicate data_uris and metadata."""
        # Create an original object
        original_object = Object(
            source="TEST_SOURCE",
            type="test_object",
            data_uris=["original_uri1", "original_uri2"],
            metadata={"key1": "value1", "key2": "value2"},
            common_name="Original Object"
        )

        # Add the object and get its ID
        original_id = neo4j_db.add_object(original_object)
        assert original_id is not None

        # Create a duplicate object with the same data_uris and metadata
        duplicate_object = Object(
            source="TEST_SOURCE",
            type="test_object",
            data_uris=["original_uri1", "original_uri2"],
            metadata={"key1": "value1", "key2": "value2"},
            common_name="Duplicate Name" # Different common name should not affect duplication detection
        )

        # Add the duplicate object - should return the original ID
        duplicate_id = neo4j_db.add_object(duplicate_object)
        assert duplicate_id is not None

        # The ID should match the original since no new object should be created
        assert duplicate_id == original_id
        
        # Verify the original common_name is preserved
        retrieved_obj = neo4j_db.get_object(original_id)
        assert retrieved_obj.common_name == "Original Object"

        # Create an object with different data_uris but same metadata
        different_uris_object = Object(
            source="TEST_SOURCE",
            type="test_object",
            data_uris=["different_uri1", "different_uri2"],
            metadata={"key1": "value1", "key2": "value2"},
            common_name="Different URIs Object"
        )

        # Add object with different data_uris - should create a new object
        different_uris_id = neo4j_db.add_object(different_uris_object)
        assert different_uris_id is not None
        assert different_uris_id != original_id
        
        # Verify the common_name is set correctly
        retrieved_diff_obj = neo4j_db.get_object(different_uris_id)
        assert retrieved_diff_obj.common_name == "Different URIs Object"

        # Create an object with same data_uris but different metadata
        different_metadata_object = Object(
            source="TEST_SOURCE",
            type="test_object",
            data_uris=["original_uri1", "original_uri2"],
            metadata={"key1": "different_value", "key2": "value2"},
            common_name="Different Metadata Object"
        )

        # Add object with different metadata - should create a new object
        different_metadata_id = neo4j_db.add_object(different_metadata_object)
        assert different_metadata_id is not None
        assert different_metadata_id != original_id
        assert different_metadata_id != different_uris_id
        
        # Verify the common_name is set correctly
        retrieved_diff_meta_obj = neo4j_db.get_object(different_metadata_id)
        assert retrieved_diff_meta_obj.common_name == "Different Metadata Object"

        # Test batch operation with a mix of new and duplicate objects
        batch_objects = [
            duplicate_object,  # Should return original ID
            Object(  # New object
                source="TEST_SOURCE",
                type="test_object",
                data_uris=["batch_uri1", "batch_uri2"],
                metadata={"batch": "value"},
                common_name="Batch Object"
            )
        ]

        # Add batch of objects
        batch_ids = neo4j_db.add_objects(batch_objects)
        assert len(batch_ids) == 2

        # First ID should match original
        assert batch_ids[0] == original_id
        # Second ID should be new
        assert batch_ids[1] != original_id
        assert batch_ids[1] != different_uris_id
        assert batch_ids[1] != different_metadata_id
        
        # Verify common_name for new batch object
        retrieved_batch_obj = neo4j_db.get_object(batch_ids[1])
        assert retrieved_batch_obj.common_name == "Batch Object" 