import pytest
import numpy as np
from datetime import datetime, timedelta, timezone
from omnicat.db import PostGisDb
from omnicat.models.base_models import AreaOfInterest
from sqlalchemy import text


# Helper functions for generating test data
def create_orbital_track(radius, n_points, plane='XY'):
    """
    Create points for an orbital track in a specified plane.

    Args:
        radius: Radius of the orbital track
        n_points: Number of points to generate
        plane: Plane for the orbit ('XY', 'YZ', or 'XZ')

    Returns:
        Tuple of (x, y, z) arrays for the track
    """
    theta = np.linspace(0, 2*np.pi, n_points, endpoint=False)

    if plane == 'XY':
        # Track in XY plane
        x = radius * np.cos(theta)
        y = radius * np.sin(theta)
        z = np.zeros(n_points)
    elif plane == 'YZ':
        # Track in YZ plane
        x = np.zeros(n_points)
        y = radius * np.sin(theta)
        z = radius * np.cos(theta)
    elif plane == 'XZ':
        # Track in XZ plane
        x = radius * np.cos(theta)
        y = np.zeros(n_points)
        z = radius * np.sin(theta)
    else:
        raise ValueError(f"Unsupported plane: {plane}")

    return x, y, z


def create_propagated_points(coords, times):
    """
    Create a list of propagated points from coordinate arrays and times.

    Args:
        coords: Tuple of (x, y, z) arrays
        times: List of datetime objects

    Returns:
        List of (x, y, z, time) tuples
    """
    x, y, z = coords
    return [(float(x[i]), float(y[i]), float(z[i]), times[i])
            for i in range(len(times))]


def create_time_series(base_time, n_points, interval=1):
    """
    Create a time series for track points.

    Args:
        base_time: Starting datetime
        n_points: Number of points to generate
        interval: Interval between points in minutes

    Returns:
        List of datetime objects
    """
    base_time = base_time.replace(tzinfo=timezone.utc)
    return [base_time + timedelta(minutes=i*interval) for i in range(n_points)]


def create_aoi_at_point(center, radius, target_time, time_window):
    """
    Create an area of interest at a specific point.

    Args:
        center: Center point [x, y, z]
        radius: Radius of the area
        target_time: Time to center the window on
        time_window: Size of the time window (timedelta)

    Returns:
        AreaOfInterest object
    """
    return AreaOfInterest(
        center=center,
        radius=radius,
        time_start=target_time - time_window,
        time_end=target_time + time_window
    )


@pytest.fixture
def standard_tracks(postgis_db):
    """
    Fixture to create standard test tracks in XY, YZ, and XZ planes.

    Returns:
        Dictionary of track IDs and their parameters
    """
    n_points = 100
    radius = 7000
    base_time = datetime(2024, 1, 1).replace(tzinfo=timezone.utc)
    times = create_time_series(base_time, n_points)

    tracks = {
        'TRK-1': create_orbital_track(radius, n_points, 'XY'),
        'TRK-2': create_orbital_track(radius, n_points, 'YZ'),
        'TRK-3': create_orbital_track(radius, n_points, 'XZ')
    }

    # Insert all tracks into the database
    for track_id, coords in tracks.items():
        points = create_propagated_points(coords, times)
        postgis_db.insert_propagated_points(track_id, points)

    # Return data useful for tests
    return {
        'track_ids': list(tracks.keys()),
        'radius': radius,
        'times': times,
        'base_time': base_time
    }


def test_aoi_query(postgis_db, standard_tracks):
    # Get test parameters
    radius = standard_tracks['radius']
    times = standard_tracks['times']

    # Query near first conjunction point (XY and YZ planes intersect at Y-axis)
    target_time = times[len(times) // 4]
    time_window = timedelta(minutes=5)

    query_aoi = create_aoi_at_point(
        center=[0.0, float(radius), 0.0],
        radius=500.0,
        target_time=target_time,
        time_window=time_window
    )

    # Query tracks in conjunction
    tracks_in_conjunction = postgis_db.query_aoi(query_aoi)

    # Assert both tracks are found
    assert len(
        tracks_in_conjunction) == 2, f"Expected 2 tracks in conjunction, found {len(tracks_in_conjunction)}"
    assert "TRK-1" in tracks_in_conjunction, "TRK-1 not found in conjunction"
    assert "TRK-2" in tracks_in_conjunction, "TRK-2 not found in conjunction"


def test_aoi_query_with_track_ids(postgis_db, standard_tracks):
    """Test query_aoi with the track_ids parameter to limit search results."""
    # Get test parameters
    radius = standard_tracks['radius']
    times = standard_tracks['times']

    # Query near conjunction point (XY and YZ planes intersect at Y-axis)
    target_time = times[len(times) // 4]
    time_window = timedelta(minutes=5)

    query_aoi = create_aoi_at_point(
        center=[0.0, float(radius), 0.0],
        radius=500.0,
        target_time=target_time,
        time_window=time_window
    )

    # Test 1: Without track_ids (should find all matching tracks)
    all_tracks = postgis_db.query_aoi(query_aoi)
    assert len(
        all_tracks) == 2, "Should find 2 tracks (TRK-1 and TRK-2) at [0, r, 0]"
    assert "TRK-1" in all_tracks, "TRK-1 should be found in conjunction"
    assert "TRK-2" in all_tracks, "TRK-2 should be found in conjunction"
    assert "TRK-3" not in all_tracks, "TRK-3 should not be found in conjunction at [0, r, 0]"

    # Test 2: With track_ids containing only TRK-1
    track1_only = postgis_db.query_aoi(query_aoi, track_ids=["TRK-1"])
    assert len(
        track1_only) == 1, "Should only find TRK-1 when filtering by track_ids"
    assert "TRK-1" in track1_only, "TRK-1 should be found"
    assert "TRK-2" not in track1_only, "TRK-2 should be filtered out by track_ids"

    # Test 3: With track_ids containing both TRK-1 and TRK-2
    both_tracks = postgis_db.query_aoi(query_aoi, track_ids=["TRK-1", "TRK-2"])
    assert len(
        both_tracks) == 2, "Should find both TRK-1 and TRK-2 when both are in track_ids"
    assert "TRK-1" in both_tracks, "TRK-1 should be found"
    assert "TRK-2" in both_tracks, "TRK-2 should be found"

    # Test 4: With track_ids containing a non-matching track
    non_matching = postgis_db.query_aoi(query_aoi, track_ids=["TRK-3"])
    assert len(
        non_matching) == 0, "Should find no tracks when track_ids only contains non-matching tracks"

    # Test 5: With empty track_ids list (should match all tracks, same as Test 1)
    empty_filter = postgis_db.query_aoi(query_aoi, track_ids=[])
    assert len(
        empty_filter) == 2, "Empty track_ids list should return all matching tracks"
    assert "TRK-1" in empty_filter, "TRK-1 should be found with empty track_ids list"
    assert "TRK-2" in empty_filter, "TRK-2 should be found with empty track_ids list"


def test_get_unpropagated_tracks_for_time_window(postgis_db):
    # Setup test data
    base_time = datetime(2024, 1, 1)
    points_time_range = [
        (0.0, 0.0, 0.0, base_time + timedelta(minutes=i))
        for i in range(60)  # 1 hour of points
    ]

    # Insert three tracks with different time ranges
    postgis_db.insert_propagated_points(
        'TRK-1', points_time_range)  # Points for full hour
    postgis_db.insert_propagated_points(
        'TRK-2', points_time_range[:30])  # Points for first 30 minutes
    postgis_db.insert_propagated_points(
        'TRK-3', points_time_range[30:])  # Points for last 30 minutes

    # Test case 1: Time window where some tracks have no points
    time_start = base_time + timedelta(minutes=45)
    time_end = base_time + timedelta(minutes=50)
    unpropagated = postgis_db.get_unpropagated_tracks_for_time_window(
        time_start,
        time_end,
        []  # Empty list to check all tracks
    )
    assert 'TRK-2' in unpropagated, "TRK-2 should be unpropagated in 45-50 minute window"
    assert 'TRK-1' not in unpropagated, "TRK-1 should have points in 45-50 minute window"
    assert 'TRK-3' not in unpropagated, "TRK-3 should have points in 45-50 minute window"

    # Test case 2: Check for non-existent tracks
    nonexistent_tracks = ['TRK-4', 'TRK-5']
    unpropagated = postgis_db.get_unpropagated_tracks_for_time_window(
        time_start,
        time_end,
        nonexistent_tracks
    )
    assert all(track in unpropagated for track in nonexistent_tracks), \
        "Non-existent tracks should be included in results"
    assert 'TRK-2' in unpropagated, \
        "TRK-2 should still be included as it has no points in the window"

    # Test case 3: Mixed case - some tracks exist, some don't
    time_start = base_time + timedelta(minutes=15)
    time_end = base_time + timedelta(minutes=45)
    track_ids = ['TRK-1', 'TRK-2', 'TRK-3', 'TRK-4']
    unpropagated = postgis_db.get_unpropagated_tracks_for_time_window(
        time_start,
        time_end,
        track_ids
    )
    assert 'TRK-4' in unpropagated, "TRK-4 should be included (doesn't exist)"
    assert 'TRK-3' not in unpropagated, "TRK-3 should not be unpropagated (has points 30-45)"
    assert 'TRK-1' not in unpropagated, "TRK-1 should not be unpropagated (has points 15-45)"
    assert 'TRK-2' not in unpropagated, "TRK-2 should not be unpropagated (has points 15-30)"


def test_delete_tracks(postgis_db):
    # Create test data - 3 track IDs with propagated data
    base_time = datetime(2023, 1, 1, tzinfo=timezone.utc)
    propagated_time_start = base_time
    propagated_time_end = base_time + timedelta(hours=1)

    # Create points spanning the propagated time range
    points_for_tracks = [
        (100.0, 100.0, 100.0, propagated_time_start),
        (200.0, 200.0, 200.0, propagated_time_start + timedelta(minutes=30)),
        (300.0, 300.0, 300.0, propagated_time_end)
    ]

    # Create 3 track IDs
    track_ids = ['TEST-TRACK-1', 'TEST-TRACK-2', 'TEST-TRACK-3']

    # Insert propagated points for all 3 tracks
    for track_id in track_ids:
        postgis_db.insert_propagated_points(track_id, points_for_tracks)

    # Define a test window outside the propagated time range
    # 1 hour after the propagated data ends
    test_window_start = base_time + timedelta(hours=2)
    test_window_end = test_window_start + timedelta(minutes=30)

    # Call get_unpropagated_tracks_for_time_window for the test window
    unpropagated_tracks = postgis_db.get_unpropagated_tracks_for_time_window(
        test_window_start,
        test_window_end,
        []
    )

    # Assert all 3 track IDs are returned as unpropagated for this time window
    assert set(unpropagated_tracks) == set(
        track_ids), "All 3 tracks should be unpropagated for the test window"

    # Delete 2 of the tracks
    tracks_to_delete = track_ids[:2]  # TEST-TRACK-1 and TEST-TRACK-2
    postgis_db.delete_tracks(tracks_to_delete)

    # Call get_unpropagated_tracks_for_time_window again
    remaining_unpropagated_tracks = postgis_db.get_unpropagated_tracks_for_time_window(
        test_window_start,
        test_window_end,
        []
    )

    # Assert only the non-deleted track is returned (TEST-TRACK-3)
    assert remaining_unpropagated_tracks == [
        'TEST-TRACK-3'], "Only TEST-TRACK-3 should remain as unpropagated"


def test_delete_data_outside_time_window(postgis_db):
    """
    Test deleting data outside a time window and verifying the effects through get_unpropagated_tracks_for_time_window.
    """
    # Create test data with a track that has points within a 1-hour time window
    base_time = datetime(2023, 1, 1, tzinfo=timezone.utc)
    test_window_start = base_time
    test_window_end = base_time + timedelta(hours=1)

    # Calculate the midpoint of the window
    midpoint_time = base_time + timedelta(minutes=30)

    # Create points spanning the test window
    # First half of window (0-30 minutes)
    first_half_points = [
        (100.0, 100.0, 100.0, test_window_start),
        (150.0, 150.0, 150.0, test_window_start + timedelta(minutes=15)),
        (200.0, 200.0, 200.0, midpoint_time - timedelta(minutes=1))
    ]

    # Second half of window (30-60 minutes)
    second_half_points = [
        (250.0, 250.0, 250.0, midpoint_time + timedelta(minutes=1)),
        (300.0, 300.0, 300.0, test_window_start + timedelta(minutes=45)),
        (350.0, 350.0, 350.0, test_window_end - timedelta(minutes=1))
    ]

    # All points for the track
    all_points = first_half_points + second_half_points

    # Create and insert the test track
    test_track_id = 'TEST-TIME-WINDOW-TRACK'
    postgis_db.insert_propagated_points(test_track_id, all_points)

    # Verify the track is properly propagated for the entire window
    unpropagated_full_window = postgis_db.get_unpropagated_tracks_for_time_window(
        test_window_start,
        test_window_end,
        []
    )
    assert len(
        unpropagated_full_window) == 0, "Track should be propagated for the full window initially"

    # Now delete the first half of the time window data
    postgis_db.delete_data_outside_time_window(midpoint_time, test_window_end)

    # Check if the track is now unpropagated for the first half of the window
    first_half_window = postgis_db.get_unpropagated_tracks_for_time_window(
        test_window_start,
        midpoint_time,
        []
    )
    assert test_track_id in first_half_window, "Track should be unpropagated for the first half of the window after deletion"

    # Check that the track still has data for the second half of the window
    second_half_window = postgis_db.get_unpropagated_tracks_for_time_window(
        midpoint_time,
        test_window_end,
        []
    )
    assert len(
        second_half_window) == 0, "Track should still be propagated for the second half of the window"


def test_get_propagated_data_for_tracks(postgis_db):
    """
    Test retrieving propagated data points for tracks within a specified time window.
    """
    # Setup: Create two tracks with known data points
    base_time = datetime(2023, 1, 1, tzinfo=timezone.utc)
    time_start = base_time
    time_end = base_time + timedelta(hours=1)

    # Create evenly spaced points for Track 1
    track1_id = "TRACK-DATA-1"
    track1_points = [
        (100.0, 200.0, 300.0, time_start),
        (110.0, 210.0, 310.0, time_start + timedelta(minutes=15)),
        (120.0, 220.0, 320.0, time_start + timedelta(minutes=30)),
        (130.0, 230.0, 330.0, time_start + timedelta(minutes=45)),
        (140.0, 240.0, 340.0, time_end)
    ]

    # Create evenly spaced points for Track 2
    track2_id = "TRACK-DATA-2"
    track2_points = [
        (500.0, 600.0, 700.0, time_start),
        (510.0, 610.0, 710.0, time_start + timedelta(minutes=15)),
        (520.0, 620.0, 720.0, time_start + timedelta(minutes=30)),
        (530.0, 630.0, 730.0, time_start + timedelta(minutes=45)),
        (540.0, 640.0, 740.0, time_end)
    ]

    # Insert tracks into database
    postgis_db.insert_propagated_points(track1_id, track1_points)
    postgis_db.insert_propagated_points(track2_id, track2_points)

    # Test case 1: Get data for both tracks in full time window
    data = postgis_db.get_propagated_data_for_tracks(
        [track1_id, track2_id],
        time_start,
        time_end
    )

    # Verify structure and content of returned data
    assert len(data) > 0, "Should return data points"

    # Check that we got points for both tracks
    returned_track_ids = {point["track_id"] for point in data}
    assert track1_id in returned_track_ids, f"Should return points for {track1_id}"
    assert track2_id in returned_track_ids, f"Should return points for {track2_id}"

    # Verify data structure for each point
    for point in data:
        assert "track_id" in point, "Each point should have a track_id"
        assert "x" in point, "Each point should have x coordinate"
        assert "y" in point, "Each point should have y coordinate"
        assert "z" in point, "Each point should have z coordinate"
        assert "time" in point, "Each point should have a timestamp"
        assert "obj_id" not in point, "Points should not have obj_id field"
        # Verify timestamp is UTC-aware
        assert point["time"].tzinfo is not None, "Timestamp should have timezone info"
        assert point["time"].tzinfo.tzname(None) in (
            'UTC', 'Z'), "Timestamp should be in UTC"

    # Test case 2: Get data for a single track in a partial time window
    subset_start = time_start + timedelta(minutes=10)
    subset_end = time_start + timedelta(minutes=40)

    subset_data = postgis_db.get_propagated_data_for_tracks(
        [track1_id],
        subset_start,
        subset_end
    )

    # Verify only points for the specified track and time window are returned
    assert all(point["track_id"] == track1_id for point in subset_data), \
        "Should only return points for the specified track"

    # Verify times are within the specified window
    for point in subset_data:
        point_time = point["time"]
        # Verify timezone info exists and is UTC
        assert point_time.tzinfo is not None, "Timestamp should have timezone info"
        assert point_time.tzinfo.tzname(None) in (
            'UTC', 'Z'), "Timestamp should be in UTC"
        assert subset_start <= point_time <= subset_end, \
            f"Point time {point_time} should be within the specified time window"


def test_aoi_query_excludes_tracks_outside_radius(postgis_db):
    """
    Test that tracks outside the AOI radius are properly excluded from results.
    This test validates that the spatial filtering is working correctly.
    """
    base_time = datetime(2024, 1, 1, tzinfo=timezone.utc)
    times = create_time_series(base_time, 10, interval=1)

    # Create a track very close to origin (within 1 km)
    close_track_id = "CLOSE-TRACK"
    close_points = [
        (0.5, 0.0, 0.0, times[i]) for i in range(len(times))
    ]

    # Create a track far from origin (100 km away)
    far_track_id = "FAR-TRACK"
    far_points = [
        (100.0, 0.0, 0.0, times[i]) for i in range(len(times))
    ]

    # Insert both tracks
    postgis_db.insert_propagated_points(close_track_id, close_points)
    postgis_db.insert_propagated_points(far_track_id, far_points)

    # Query with AOI at origin with 10 km radius
    # Should include close track but exclude far track
    aoi = create_aoi_at_point(
        center=[0.0, 0.0, 0.0],
        radius=10.0,  # 10 km radius
        target_time=base_time + timedelta(minutes=5),
        time_window=timedelta(minutes=30)
    )

    result_track_ids = postgis_db.query_aoi(aoi)

    # Verify results
    assert close_track_id in result_track_ids, "Track within AOI should be included"
    assert far_track_id not in result_track_ids, "Track outside AOI should be excluded"

    # Test with larger radius that should include both
    aoi_large = create_aoi_at_point(
        center=[0.0, 0.0, 0.0],
        radius=150.0,  # 150 km radius
        target_time=base_time + timedelta(minutes=5),
        time_window=timedelta(minutes=30)
    )

    result_track_ids_large = postgis_db.query_aoi(aoi_large)

    # Both tracks should be included with larger radius
    assert close_track_id in result_track_ids_large, "Close track should be included with large radius"
    assert far_track_id in result_track_ids_large, "Far track should be included with large radius"


def test_aoi_query_with_track_ids_filter_bug(postgis_db):
    """
    Test to verify the bug fix where query_aoi with track_ids parameter
    was returning tracks that met spatial/time criteria but were not in the track_ids list.

    This test specifically validates that when track_ids is provided, ONLY tracks
    from that list can be returned, even if other tracks exist in the AOI.
    """
    base_time = datetime(2024, 1, 1, tzinfo=timezone.utc)
    times = create_time_series(base_time, 10, interval=1)

    # Create multiple tracks at the same location (within AOI)
    # Track 1: Should be returned (in track_ids filter)
    track_1_id = "FILTERED-TRACK-1"
    track_1_points = [
        (0.5, 0.0, 0.0, times[i]) for i in range(len(times))
    ]

    # Track 2: Should NOT be returned (not in track_ids filter, but in same location)
    track_2_id = "UNFILTERED-TRACK-2"
    track_2_points = [
        (0.6, 0.0, 0.0, times[i]) for i in range(len(times))
    ]

    # Track 3: Should be returned (in track_ids filter)
    track_3_id = "FILTERED-TRACK-3"
    track_3_points = [
        (0.7, 0.0, 0.0, times[i]) for i in range(len(times))
    ]

    # Track 4: Far away track, should never be returned
    track_4_id = "FAR-TRACK-4"
    track_4_points = [
        (100.0, 0.0, 0.0, times[i]) for i in range(len(times))
    ]

    # Add all tracks to the database
    postgis_db.insert_propagated_points(track_1_id, track_1_points)
    postgis_db.insert_propagated_points(track_2_id, track_2_points)
    postgis_db.insert_propagated_points(track_3_id, track_3_points)
    postgis_db.insert_propagated_points(track_4_id, track_4_points)

    # Define AOI at origin with 2km radius (should include tracks 1, 2, 3 but not 4)
    aoi = AreaOfInterest(
        center=[0.0, 0.0, 0.0],
        radius=2.0,  # 2km radius
        time_start=base_time,
        time_end=base_time + timedelta(hours=1)
    )

    # Test 1: Query without track_ids filter (should return tracks 1, 2, 3)
    all_result_ids = postgis_db.query_aoi(aoi)
    assert len(
        all_result_ids) == 3, f"Expected 3 tracks without filter, got {len(all_result_ids)}"
    assert set(all_result_ids) == {track_1_id, track_2_id, track_3_id}, \
        f"Unexpected tracks without filter: {all_result_ids}"

    # Test 2: Query with track_ids filter (should return only tracks 1 and 3)
    # Only include tracks 1 and 3
    filtered_track_ids = [track_1_id, track_3_id]
    filtered_result_ids = postgis_db.query_aoi(
        aoi, track_ids=filtered_track_ids)

    # This is the key test - should only return tracks that are BOTH:
    # 1. Within the AOI (spatial + time filter)
    # 2. In the track_ids list (filter constraint)
    assert len(
        filtered_result_ids) == 2, f"Expected 2 tracks with filter, got {len(filtered_result_ids)}"
    assert set(filtered_result_ids) == {track_1_id, track_3_id}, \
        f"Expected only filtered tracks {filtered_track_ids}, got {filtered_result_ids}"

    # Verify track_2 is NOT in the filtered results (even though it's in the AOI)
    assert track_2_id not in filtered_result_ids, \
        f"Track {track_2_id} should not be in filtered results but was found"

    # Test 3: Query with track_ids filter that includes a non-existent track
    filtered_track_ids_with_missing = [track_1_id, "NON-EXISTENT-TRACK"]
    filtered_result_missing = postgis_db.query_aoi(
        aoi, track_ids=filtered_track_ids_with_missing)

    # Should only return track_1 (track that exists AND is in AOI AND is in filter list)
    assert len(
        filtered_result_missing) == 1, f"Expected 1 track with missing track filter, got {len(filtered_result_missing)}"
    assert filtered_result_missing[0] == track_1_id, \
        f"Expected only {track_1_id}, got {filtered_result_missing}"
