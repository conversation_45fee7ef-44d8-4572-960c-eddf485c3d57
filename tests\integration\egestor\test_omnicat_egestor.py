import os
import json
import csv
import shutil
from datetime import datetime, timedelta, timezone
import pytest
import pandas as pd
from io import String<PERSON>

from omnicat import Settings
from omnicat.egestor import OmnicatEgestor
from omnicat.ingestor import OmnicatIngestor
from omnicat.models import Object, Track, TLE, ObjectCorrelation


@pytest.fixture
def output_dir(tmp_path):
    """Create a temporary directory for test outputs."""
    return str(tmp_path / "test_visualizer_output")


@pytest.fixture
def test_objects():
    """Create test objects for ingestion."""
    return [
        Object(
            source="test_source",
            type="satellite",
            id="TEST_SAT_1",
            data_uris=["test://uri1"],
            metadata={
                "name": "Test Satellite 1",
                "type": "satellite",
                "origin": "test",
                "satellite_id": "TEST_SAT_1"
            }
        ),
        Object(
            source="test_source",
            type="satellite",
            id="TEST_SAT_2",
            data_uris=["test://uri2"],
            metadata={
                "name": "Test Satellite 2",
                "type": "satellite",
                "origin": "test",
                "satellite_id": "TEST_SAT_2"
            }
        )
    ]


@pytest.fixture
def test_tracks():
    """Create test tracks for ingestion."""
    # These are valid TLEs for testing purposes
    return [
        Track(
            format="tle",
            source="test",
            data=TLE(
                line1="1 25544U 98067A   22001.00000000  .00000000  00000-0  00000-0 0  9995",
                line2="2 25544  51.6416 247.4627 0006703 130.5360 325.0288 15.49140040  1196"
            ),
            metadata={
                "origin": "test",
                "satellite_name": "ISS TEST"
            }
            # The object_correlations will be created in the test
        ),
        Track(
            format="tle",
            source="test",
            data=TLE(
                line1="1 43013U 17073A   22001.00000000  .00000000  00000-0  00000-0 0  9993",
                line2="2 43013  98.7490 112.1060 0001247  88.0480 272.0880 14.19710580  1199"
            ),
            metadata={
                "origin": "test",
                "satellite_name": "STARLINK TEST"
            }
            # The object_correlations will be created in the test
        )
    ]


class TestOmnicatEgestorIntegration:
    """
    Integration tests for OmnicatEgestor with real database connections.
    These tests require a running Neo4j and PostGIS database.
    """

    def _create_egestor(self, neo4j_db, postgis_db, output_dir):
        """Helper method to create an egestor instance."""
        return OmnicatEgestor(
            neo4j_db=neo4j_db,
            postgis_db=postgis_db
        )

    def _get_time_window(self, days: int = 1) -> tuple[datetime, datetime]:
        """Helper method to create a time window for testing."""
        time_end = datetime.now(timezone.utc)
        time_start = time_end - timedelta(days=days)
        return time_start, time_end

    def test_export_with_settings(self, neo4j_db, postgis_db, test_settings, output_dir, export_file_helpers):
        """Test exporting data with direct creation from settings."""
        try:
            egestor = self._create_egestor(neo4j_db, postgis_db, output_dir)

            time_start, time_end = self._get_time_window()
            scenario_name = "integration_test"

            scenario_dir = egestor.export_scenario(
                scenario_name=scenario_name,
                time_start=time_start,
                time_end=time_end,
                output_dir=output_dir
            )

            export_file_helpers["verify_export_files"](scenario_dir, scenario_name)
            
        except Exception as e:
            pytest.fail(f"Test failed with exception: {e}")

    def test_create_from_settings(self, test_settings, output_dir):
        """Test creating an egestor from settings."""
        # This test demonstrates using from_settings, but doesn't depend on
        # actual database operations, so we catch and handle any connection errors
        try:
            egestor = OmnicatEgestor.from_settings(test_settings)

            assert egestor.neo4j_db is not None
            assert egestor.postgis_db is not None
        except Exception as e:
            pytest.fail(f"Failed to create egestor from settings: {e}")

    def test_empty_export(self, neo4j_db, postgis_db, output_dir, monkeypatch, export_file_helpers):
        """Test exporting when no data is available."""
        egestor = self._create_egestor(neo4j_db, postgis_db, output_dir)
        
        # Mock get_all_object_ids to return empty list
        monkeypatch.setattr(neo4j_db, "get_all_object_ids", lambda: [])
        
        # Mock get_object and get_tracks_for_object in case they're called
        monkeypatch.setattr(neo4j_db, "get_object", lambda _: None)
        monkeypatch.setattr(neo4j_db, "get_tracks_for_object", lambda _: [])
        monkeypatch.setattr(neo4j_db, "get_track_ids_for_object", lambda _: [])
        
        scenario_name = "empty_test"
        time_start, time_end = self._get_time_window()
        
        scenario_dir = egestor.export_scenario(
            scenario_name=scenario_name,
            time_start=time_start,
            time_end=time_end,
            output_dir=output_dir
        )
        
        csv_file = os.path.join(scenario_dir, f"{scenario_name}.csv")
        
        assert os.path.exists(csv_file)
        
        with open(csv_file, 'r') as f:
            export_file_helpers["verify_csv_structure"](f)
            
            # Additional verification specific to empty export
            f.seek(0)  # Reset file pointer to beginning
            content = f.read()
            assert "number_of_timesteps,0" in content
        
        events_file = os.path.join(scenario_dir, "Events.json")
        rockets_file = os.path.join(scenario_dir, "Rockets.json")
        ground_stations_file = os.path.join(scenario_dir, "GroundStations.json")
        
        assert os.path.exists(events_file)
        assert os.path.exists(rockets_file)
        assert os.path.exists(ground_stations_file)

    def test_ingest_and_export(self, neo4j_db, postgis_db, output_dir, test_objects, test_tracks,
                               test_ingest_helper, export_file_helpers):
        """Test ingesting data with OmnicatIngestor and then exporting with OmnicatEgestor."""
        # Use fixed time window to ensure consistency between ingest and export
        base_time = datetime(2022, 1, 1, tzinfo=timezone.utc)
        time_start = base_time
        time_end = base_time + timedelta(days=2)
        
        object_ids, track_ids = test_ingest_helper(
            neo4j_db=neo4j_db,
            postgis_db=postgis_db,
            test_objects=test_objects,
            test_tracks=test_tracks,
            time_start=time_start,
            time_end=time_end,
            validity_time=base_time
        )
        
        egestor = self._create_egestor(neo4j_db, postgis_db, output_dir)
        
        scenario_name = "test_ingest_export"
        scenario_dir = egestor.export_scenario(
            scenario_name=scenario_name,
            time_start=time_start,
            time_end=time_end,
            output_dir=output_dir
        )
        
        result = export_file_helpers["verify_export_files"](scenario_dir, scenario_name, test_objects)
        
        # Additional verification specific to this test
        df = result["df"]
        assert all(df["entry_type"] == "Propagated"), "All rows should have entry_type='Propagated'"
        assert all(df["timestamp"] >= 0), "All timestamps should be >= 0"
        assert all(df["sensors"] == "[]"), "All sensor values should be empty arrays"
