import pytest
from datetime import datetime, timezone
from fastapi.testclient import Test<PERSON>lient
from fastapi import status
from omnicat.models import (
    Object, Track, StateVector, TLE,
    GetTrackResponse, CorrelateTrackRequest, GetObjectResponse,
    ObjectCorrelation 
)


def add_sample_object(client: TestClient, sample_object: Object) -> str:
    add_object_response = client.post(
        "/objects", json=sample_object.model_dump(mode='json'))
    assert add_object_response.status_code == status.HTTP_201_CREATED
    object_id = add_object_response.json()["object_id"]
    return object_id


def add_sample_track(client: TestClient, sample_track: Track) -> str:
    add_track_response = client.post(
        "/tracks", json=sample_track.model_dump(mode='json'))
    assert add_track_response.status_code == status.HTTP_201_CREATED
    return add_track_response.json()["track_id"]


def create_correlation(client: TestClient, correlate_track_request: CorrelateTrackRequest) -> None:
    correlate_track_response = client.post(
        "/correlate", json=correlate_track_request.model_dump(mode='json'))
    assert correlate_track_response.status_code == status.HTTP_201_CREATED
    assert correlate_track_response.json()[
        "message"] == "Correlation created successfully."


def retrieve_track(client: TestClient, track_id: str, format: str = 'state_vector') -> Track:
    response = client.get(f"tracks/{track_id}?format={format}")
    assert response.status_code == status.HTTP_200_OK
    get_track_response = GetTrackResponse.model_validate(response.json())
    track = get_track_response.track
    return track


def retrieve_object(client: TestClient, object_id: str) -> Object:
    response = client.get(f"/objects/{object_id}")
    assert response.status_code == status.HTTP_200_OK
    get_object_response = GetObjectResponse.model_validate(response.json())
    return get_object_response.object


def verify_object_correlation_was_stored(client: TestClient, track_id: str, expected_object_correlation: ObjectCorrelation) -> None:
    retrieved_track = retrieve_track(client, track_id)
    assert len(retrieved_track.object_correlations) == 1
    actual_object_correlation = retrieved_track.object_correlations[0]
    
    # Verify core properties of the ObjectCorrelation
    assert actual_object_correlation.object_id == expected_object_correlation.object_id
    assert actual_object_correlation.validity_time == expected_object_correlation.validity_time
    assert actual_object_correlation.confidence == expected_object_correlation.confidence
    assert actual_object_correlation.metadata == expected_object_correlation.metadata