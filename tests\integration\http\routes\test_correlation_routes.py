from datetime import datetime, timezone
from fastapi.testclient import TestClient
from fastapi import status
from omnicat.models import (
    Track, Object,
    ObjectCorrelation, CorrelateTrackRequest
)
from helpers import (
    add_sample_track, add_sample_object,
    create_correlation, verify_object_correlation_was_stored
)


class TestCorrelation:
    def test_correlate_track_with_object(self, client: TestClient, sample_track: Track, sample_object: Object):
        track_id = add_sample_track(client, sample_track)
        object_id = add_sample_object(client, sample_object)

        expected_object_correlation = ObjectCorrelation(
            object_id=object_id,
            validity_time=datetime.now(timezone.utc),
            confidence=0.95,
            metadata={"correlation_method": "integration_test"}
        )

        correlate_track_request = CorrelateTrackRequest(
            track_id=track_id,
            correlation_type="object",
            correlation=expected_object_correlation
        )

        create_correlation(client, correlate_track_request)
        verify_object_correlation_was_stored(
            client, track_id, expected_object_correlation)

    def test_correlate_track_with_invalid_track_id(self, client: TestClient, sample_object: Object):
        object_id = add_sample_object(client, sample_object)

        object_correlation = ObjectCorrelation(
            object_id=object_id,
            validity_time=datetime.now(timezone.utc),
            confidence=0.95
        )

        correlate_request = CorrelateTrackRequest(
            track_id="nonexistent_track",
            correlation_type="object",
            correlation=object_correlation
        )

        response = client.post(
            "/correlate", json=correlate_request.model_dump(mode='json'))
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_correlate_track_with_invalid_object(self, client: TestClient, sample_track: Track):
        track_id = add_sample_track(client, sample_track)

        object_correlation = ObjectCorrelation(
            object_id="nonexistent_object",
            validity_time=datetime.now(timezone.utc),
            confidence=0.95
        )

        correlate_request = CorrelateTrackRequest(
            track_id=track_id,
            correlation_type="object",
            correlation=object_correlation
        )

        response = client.post(
            "/correlate", json=correlate_request.model_dump(mode='json'))
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_invalid_correlation_type(self, client: TestClient, sample_track: Track):
        track_id = add_sample_track(client, sample_track)

        object_correlation = ObjectCorrelation(
            object_id="some_id",
            validity_time=datetime.now(timezone.utc),
            confidence=0.95
        )

        correlate_request = CorrelateTrackRequest(
            track_id=track_id,
            correlation_type="invalid_type",
            correlation=object_correlation
        )

        response = client.post(
            "/correlate", json=correlate_request.model_dump(mode='json'))
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY