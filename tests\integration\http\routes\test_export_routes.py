import os
import re
import json
import pytest
import tempfile
import shutil
import tarfile
from io import BytesIO
from datetime import datetime, timezone, timedelta
from fastapi import FastAPI, status, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from starlette.testclient import TestClient
from pathlib import Path
import pandas as pd
from io import StringIO

from omnicat import OmniCat
from omnicat.models import Track, TLE, Object, ObjectCorrelation
from omnicat.http.routes.export_routes import create_export_router
from omnicat.logger import logger


class TestExportRoutes:
    """Tests for the export routes."""

    def test_export_gui_endpoint(self, client):
        """Test that the export/gui endpoint returns an HTML page."""
        response = client.get("/export/gui")
        assert response.status_code == status.HTTP_200_OK, f"Got status {response.status_code}: {response.text}"
        assert "text/html" in response.headers["content-type"]
        
        # Check for expected form elements in the response text
        assert "scenario" in response.text.lower()
        assert "start" in response.text.lower()
        assert "minutes" in response.text.lower() or "duration" in response.text.lower()
        # The fetch('/export/generate') is used in JavaScript instead of form action
        assert "fetch('/export/generate'" in response.text

    def test_export_generate_endpoint(self, client, neo4j_db, postgis_db, test_objects, test_tracks, 
                                      test_ingest_helper, export_file_helpers):
        """Test that the export/generate endpoint returns a successful response with a tar file."""
        # Use fixed time window to ensure consistency between ingest and export
        base_time = datetime(2022, 1, 1, tzinfo=timezone.utc)
        time_start = base_time
        time_end = base_time + timedelta(days=2)
        
        # Ingest test data using the shared helper
        object_ids, track_ids = test_ingest_helper(
            neo4j_db=neo4j_db,
            postgis_db=postgis_db,
            test_objects=test_objects,
            test_tracks=test_tracks,
            time_start=time_start,
            time_end=time_end,
            validity_time=base_time
        )
        
        scenario_name = "test_export_api"
        start_time = time_start.isoformat()
        minutes = int((time_end - time_start).total_seconds() // 60)
        
        response = client.post(
            "/export/generate",
            data={
                "scenario": scenario_name,
                "start_time": start_time,
                "minutes": str(minutes)
            }
        )
        
        assert response.status_code == 200, f"Expected 200, got {response.status_code}: {response.text}"
        assert "application/octet-stream" in response.headers["content-type"]
        assert f'filename="{scenario_name}.tar.gz"' in response.headers["content-disposition"]
        
        paths = export_file_helpers["extract_tar_to_temp_dir"](
            response_content=response.content,
            prefix=f"test_export_{scenario_name}_"
        )
        
        export_file_helpers["verify_export_files"](paths["scenario_dir"], scenario_name, test_objects)

    def test_export_with_invalid_parameters(self, client):
        """Test export with invalid parameters."""
        # Test with invalid start_time
        response = client.post(
            "/export/generate",
            data={
                "scenario": "test_scenario",
                "start_time": "invalid-date-format",
                "minutes": "60"
            }
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, f"Expected 422, got {response.status_code}: {response.text}"
        
        response_json = response.json()
        assert "message" in response_json or "detail" in response_json
        
        error_message = response_json.get("message", response_json.get("detail", ""))
        assert "Invalid start_time format" in error_message

        # Test with missing required parameter
        response = client.post(
            "/export/generate",
            data={
                "scenario": "test_scenario",
                # Missing start_time
                "minutes": "60"
            }
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_export_empty_database(self, client, neo4j_db, postgis_db, export_file_helpers):
        """Test exporting with an empty database."""
        scenario_name = "empty_scenario"
        response = client.post(
            "/export/generate",
            data={
                "scenario": scenario_name,
                "start_time": "2023-01-01T12:00:00Z",
                "minutes": "60"
            }
        )
        
        assert response.status_code == 200, f"Expected 200, got {response.status_code}: {response.text}"
        
        paths = export_file_helpers["extract_tar_to_temp_dir"](
            response_content=response.content,
            prefix=f"test_empty_export_{scenario_name}_"
        )
        
        assert paths["tar_path"] and os.path.exists(paths["tar_path"]) 