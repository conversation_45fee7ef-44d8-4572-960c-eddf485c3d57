from fastapi import status
from fastapi.testclient import TestClient
import pytest
from playwright.sync_api import sync_playwright
import time


class TestGuiRoutes:
    def test_aoi_query_gui(self, client: TestClient):
        """Test that the AOI Query GUI endpoint returns a successful response."""
        response = client.get("/aoi-query-gui")
        print(response.text)  # Print the HTML response
        assert response.status_code == status.HTTP_200_OK

    def test_aoi_query_gui_playwright(self, client: TestClient):
        """Test loading the AOI Query GUI using <PERSON>wright to check for client-side errors."""
        # Get the HTML content
        response = client.get("/aoi-query-gui")
        assert response.status_code == status.HTTP_200_OK

        # Use <PERSON>wright to load the page and check for errors
        with sync_playwright() as p:
            browser = p.chromium.launch()
            page = browser.new_page()

            # Track console errors
            console_errors = []
            page.on("console", lambda msg:
                    console_errors.append(msg.text) if msg.type == "error" else None)

            # Set the content to the response HTML
            page.set_content(response.text)

            # Allow some time for scripts to load
            time.sleep(2)

            # Check specifically for the THREE.js import in the module script
            has_three_import = page.evaluate("""() => {
                const moduleScripts = Array.from(document.querySelectorAll('script[type="module"]'));
                return moduleScripts.some(script => 
                    script.textContent.includes('import * as THREE from') || 
                    script.textContent.includes("import * as THREE from")
                );
            }""")

            print(f"Has THREE.js import: {has_three_import}")
            assert has_three_import, "THREE.js import not found in module scripts"

            # Check for globe.gl script
            has_globe_script = page.evaluate("""() => {
                const scripts = Array.from(document.querySelectorAll('script'));
                return scripts.some(s => s.src && s.src.includes('globe.gl'));
            }""")

            print(f"Has globe.gl script: {has_globe_script}")
            assert has_globe_script, "globe.gl script not found"

            # Assert no console errors occurred
            assert len(
                console_errors) == 0, f"Console errors found: {console_errors}"

            browser.close()
