import uuid
from fastapi import status
from fastapi.testclient import Test<PERSON><PERSON>
from omnicat.models import Object
from helpers import (
    add_sample_object, retrieve_object
)
from tests.conftest import verify_object_properties


class TestObjects:
    def test_add_object_success(self, client: TestClient, sample_object: Object):
        object_id = add_sample_object(client, sample_object)
        object_id_uuid = object_id.replace("OBJ-", "", 1)
        assert uuid.UUID(object_id_uuid)  # Verifies valid UUID

    def test_get_object_success(self, client: TestClient, sample_object: Object):
        object_id = add_sample_object(client, sample_object)
        response = client.get(f"/objects/{object_id}")
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        # For single object requests, expect a single object response (not a list)
        assert isinstance(response_data, dict)
        assert "object_id" in response_data
        assert "object" in response_data
        assert "correlations" in response_data

        # Use the shared verify_object_properties function
        expected_object = Object(
            source=sample_object.source,
            type=sample_object.type,
            internal_id=object_id,
            data_uris=sample_object.data_uris,
            metadata=sample_object.metadata,
            common_name=sample_object.common_name
        )

        # Verify specific internal_id separately since it's not checked by verify_object_properties
        assert response_data["object"]["internal_id"] == object_id
        assert response_data["object_id"] == object_id
        assert response_data["correlations"] == []

        # Convert response data back to Object for verification
        retrieved_object = Object(**response_data["object"])

        # Use the shared verification function for other properties
        verify_object_properties(retrieved_object, expected_object)

    def test_get_object_not_found(self, client: TestClient):
        non_existent_id = "OBJ-" + str(uuid.uuid4())
        response = client.get(f"/objects/{non_existent_id}")
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_multiple_objects_success(self, client: TestClient):
        # Create test objects
        test_objects = [
            Object(
                source="TEST_SOURCE_1",
                type="test_object",
                data_uris=["uri1", "uri2"],
                metadata={
                    "key1": "value1",
                    "International Designator": "2024-001A"
                },
                common_name="Object One"
            ),
            Object(
                source="TEST_SOURCE_2",
                type="test_object",
                data_uris=["uri3", "uri4"],
                metadata={
                    "key2": "value2",
                    "International Designator": "2024-002B"
                },
                common_name="Object Two"
            ),
            Object(
                source="TEST_SOURCE_3",
                type="test_object",
                data_uris=["uri5", "uri6"],
                metadata={
                    "key3": "value3",
                    "International Designator": "2024-003C"
                },
                common_name="Object Three"
            )
        ]

        # Add all objects and collect their IDs
        object_ids = []
        for obj in test_objects:
            obj_id = add_sample_object(client, obj)
            object_ids.append(obj_id)

        # Test retrieving multiple objects by ID using comma-separated list
        comma_separated_ids = ",".join(object_ids)
        response = client.get(f"/objects/{comma_separated_ids}")
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert isinstance(response_data, list)
        assert len(response_data) == 3

        # Verify each response object
        for i, obj_response in enumerate(response_data):
            assert obj_response["object_id"] == object_ids[i]
            assert obj_response["object"]["internal_id"] == object_ids[i]
            assert obj_response["object"]["common_name"] == test_objects[i].common_name
            assert obj_response["correlations"] == []

        # Test retrieving multiple objects by International Designator
        intl_designators = ["2024-001A", "2024-002B", "2024-003C"]
        comma_separated_intl = ",".join(intl_designators)
        response = client.get(f"/objects/{comma_separated_intl}")
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert isinstance(response_data, list)
        assert len(response_data) == 3

        # Verify each response object by International Designator
        for i, obj_response in enumerate(response_data):
            assert obj_response["object_id"] == intl_designators[i]
            assert obj_response["object"]["common_name"] == test_objects[i].common_name

        # Test mixed identifiers (ID and International Designator)
        mixed_identifiers = [object_ids[0], "2024-002B", object_ids[2]]
        comma_separated_mixed = ",".join(mixed_identifiers)
        response = client.get(f"/objects/{comma_separated_mixed}")
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert isinstance(response_data, list)
        assert len(response_data) == 3

    def test_get_multiple_objects_with_non_existent(self, client: TestClient):
        # Create one test object
        test_obj = Object(
            source="TEST_SOURCE",
            type="test_object",
            data_uris=["uri1"],
            metadata={"International Designator": "2024-001A"},
            common_name="Test Object"
        )
        obj_id = add_sample_object(client, test_obj)

        # Test with mix of existing and non-existent identifiers
        mixed_identifiers = f"{obj_id},NON-EXISTENT-ID,2024-001A"
        response = client.get(f"/objects/{mixed_identifiers}")
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert isinstance(response_data, list)
        # Should return 2 objects (same object found by both ID and International Designator)
        assert len(response_data) == 2

    def test_get_multiple_objects_all_non_existent(self, client: TestClient):
        # Test with all non-existent identifiers
        non_existent_ids = "NON-EXISTENT-1,NON-EXISTENT-2,NON-EXISTENT-3"
        response = client.get(f"/objects/{non_existent_ids}")
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_backward_compatibility_return_types(self, client: TestClient):
        """Test that single identifiers return single objects and comma-separated return lists"""
        # Create a test object
        test_obj = Object(
            source="TEST_SOURCE",
            type="test_object",
            data_uris=["uri1"],
            metadata={"International Designator": "2024-001A"},
            common_name="Test Object"
        )
        obj_id = add_sample_object(client, test_obj)

        # Test single ID - should return single object (backward compatibility)
        response = client.get(f"/objects/{obj_id}")
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert isinstance(response_data, dict)  # Single object, not a list
        assert response_data["object_id"] == obj_id

        # Test single International Designator - should return single object
        response = client.get("/objects/2024-001A")
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert isinstance(response_data, dict)  # Single object, not a list
        assert response_data["object_id"] == "2024-001A"

        # Test comma-separated IDs - should return list
        comma_separated = f"{obj_id},{obj_id}"  # Same object twice
        response = client.get(f"/objects/{comma_separated}")
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert isinstance(response_data, list)  # List of objects
        assert len(response_data) == 2
