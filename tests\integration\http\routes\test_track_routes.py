import uuid
from datetime import datetime, timedelta, timezone
from fastapi import status
from fastapi.testclient import TestClient
from omnicat.models import Track, StateVector, QueryTracksResponse, Vector3D, TLE
from helpers import (
    add_sample_track, retrieve_track
)


class TestTracks:
    def test_add_track_success(self, client: TestClient, sample_track: Track):
        track_id = add_sample_track(client, sample_track)
        track_id_uuid = track_id.replace("TRK-", "", 1)
        assert uuid.UUID(track_id_uuid)  # Verifies valid UUID

    def test_get_track_success(self, client: TestClient, sample_track: Track):
        track_id = add_sample_track(client, sample_track)
        retrieved_track = retrieve_track(client, track_id)
        assert retrieved_track == sample_track

    def test_get_track_not_found(self, client: TestClient):
        non_existent_id = "TRK-" + str(uuid.uuid4())
        response = client.get(f"/tracks/{non_existent_id}")
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_tle_track(self, client: TestClient, sample_tle_track: Track):
        track_id = add_sample_track(client, sample_tle_track)
        retrieved_track = retrieve_track(client, track_id, format="tle")

        assert retrieved_track == sample_tle_track

    def test_get_track_filter_options(self, client: TestClient, sample_track: Track):
        # First add a track to ensure there's at least one filter option
        track_id = add_sample_track(client, sample_track)

        # Get filter options
        response = client.get("/tracks/filter-options")
        assert response.status_code == status.HTTP_200_OK

        filter_options = response.json()
        assert "sources" in filter_options
        assert "object_types" in filter_options
        assert "country_codes" in filter_options
        assert "allegiances" in filter_options

        # Verify the source from the sample track is in the sources list
        assert sample_track.source in filter_options["sources"]
