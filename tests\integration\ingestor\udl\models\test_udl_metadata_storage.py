import pytest
from datetime import datetime, timezone
from omnicat.db import Neo4jDb
from omnicat.models import Object, Track, TLE, ObjectCorrelation
from omnicat.ingestor.udl.models.elset import Elset_Abridged, DataMode
from omnicat.ingestor.udl.models.onorbit import OnOrbit_Abridged, DataModeEnum, ObjectTypeEnum, CategoryEnum


class TestUdlMetadataStorage:
    def test_metadata_serialization(self, neo4j_db: Neo4jDb):
        """
        Test that Elset_Abridged and OnOrbit_Abridged objects are properly transformed 
        into Object and Track models with dictionary metadata, and correctly stored/retrieved from Neo4j.
        """
        # Create a fake OnOrbit_Abridged object
        now = datetime.now(timezone.utc)
        fake_onorbit = OnOrbit_Abridged(
            idOnOrbit="12345",
            classificationMarking="UNCLASSIFIED",
            satNo=12345,
            commonName="Test Satellite",
            origin="TEST",
            source="TEST_SOURCE",
            dataMode=DataModeEnum.TEST,
            objectType=ObjectTypeEnum.PAYLOAD,
            category=CategoryEnum.ON_ORBIT,
            intlDes="2023-001A"
        )

        # Create a fake Elset_Abridged object that references the OnOrbit
        fake_elset = Elset_Abridged(
            idElset="elset-12345",
            classificationMarking="UNCLASSIFIED",
            satNo=12345,
            epoch=now,
            meanMotion=15.5,
            idOnOrbit="12345",  # Link to the onorbit object
            eccentricity=0.001,
            inclination=51.6,
            raan=145.3,
            argOfPerigee=28.7,
            meanAnomaly=331.2,
            revNo=1234,
            bStar=0.00001,
            source="TEST_SOURCE",
            dataMode=DataMode.TEST,
            line1="1 12345U 23001A   23123.12345678  .00000000  00000-0  10000-4 0  9995",
            line2="2 12345  51.6000 145.3000 0010000  28.7000 331.2000 15.50000000 12340"
        )

        # Create an Object from the OnOrbit_Abridged in the same style as udl_ingestor_main.py
        fake_object = Object(
            source=fake_onorbit.source,  # Get source from the OnOrbit_Abridged
            type=str(fake_onorbit.objectType),  # Convert ObjectTypeEnum to string
            data_uris=[f"udl://test/{fake_onorbit.idOnOrbit}"],
            metadata={
                "udl_onorbit": fake_onorbit.model_dump(mode='json')
            }
        )

        # Add the Object to Neo4j
        object_id = neo4j_db.add_object(fake_object)

        # Create a Track from the Elset_Abridged with a correlation to the object
        fake_track = Track(
            source=fake_elset.source,
            format="tle",
            data=TLE(
                line1=fake_elset.line1,
                line2=fake_elset.line2
            ),
            metadata={
                "udl_elset": fake_elset.model_dump(mode='json')
            },
            object_correlations=[
                ObjectCorrelation(
                    object_id=object_id,
                    confidence=1.0,
                    metadata={"correlation_source": "test"},
                    validity_time=now
                )
            ]
        )

        # Add the Track to Neo4j
        track_id = neo4j_db.add_track(fake_track)

        # Retrieve the Track from Neo4j
        retrieved_track = neo4j_db.get_track(track_id)

        # Assertions to verify metadata is correctly stored as dictionaries
        assert retrieved_track is not None
        assert retrieved_track.metadata is not None

        # Check that metadata is a dictionary, not a string
        assert isinstance(retrieved_track.metadata, dict)
        assert "udl_elset" in retrieved_track.metadata

        # Verify the metadata content matches what we put in
        udl_elset_metadata = retrieved_track.metadata["udl_elset"]
        assert isinstance(udl_elset_metadata, dict)
        assert udl_elset_metadata["idElset"] == fake_elset.idElset
        assert udl_elset_metadata["satNo"] == fake_elset.satNo
        assert udl_elset_metadata["source"] == fake_elset.source
        assert udl_elset_metadata["dataMode"] == fake_elset.dataMode

        # Verify the track has the object correlation with correct object_id
        assert retrieved_track.object_correlations is not None
        assert len(retrieved_track.object_correlations) == 1
        assert retrieved_track.object_correlations[0].object_id == object_id

        # Retrieve the Object and check its metadata
        retrieved_object = neo4j_db.get_object(object_id)
        assert retrieved_object is not None
        assert retrieved_object.metadata is not None
        assert isinstance(retrieved_object.metadata, dict)

        # Verify the object metadata
        assert "udl_onorbit" in retrieved_object.metadata
        udl_onorbit_metadata = retrieved_object.metadata["udl_onorbit"]
        assert isinstance(udl_onorbit_metadata, dict)
        assert udl_onorbit_metadata["idOnOrbit"] == fake_onorbit.idOnOrbit
        assert udl_onorbit_metadata["satNo"] == fake_onorbit.satNo
        assert udl_onorbit_metadata["objectType"] == fake_onorbit.objectType
        assert udl_onorbit_metadata["dataMode"] == fake_onorbit.dataMode
