import pytest
from datetime import datetime, timedelta, timezone
from unittest.mock import <PERSON><PERSON><PERSON>, patch
import json
from helpers import add_sample_track
import uuid
import numpy as np

from omnicat import OmniCat
from omnicat.models import Track, AreaOfInterest, TLE, QueryTracksResponse, Object, StateVector, ObjectCorrelation
from omnicat.ingestor.udl import Elset_Abridged, UdlReader
from omnicat.ingestor import UdlIngestor
from omnicat.ingestor import OmnicatIngestor
from omnicat.db.neo4j_db import Neo4jDb
from omnicat.db.postgis_db import PostGisDb
from fastapi.testclient import TestClient
from omnicat.models.track_models import CorrelatedTracksFilter, TrackFilter, CorrelationStatus


def create_mock_udl_reader_with_uncorrelated_elsets(tracks_in_aoi: list[Track], tracks_outside_aoi: list[Track]) -> MagicMock:
    """Create a mock UdlReader that returns predefined Elset_Abridged items without OnOrbit IDs."""
    mock_udl_reader = MagicMock(spec=UdlReader)

    # Convert Track objects to Elset_Abridged objects without OnOrbit IDs
    elsets_in_aoi = [
        create_udl_elset_from_track(track, idx)
        for idx, track in enumerate(tracks_in_aoi)
    ]

    elsets_outside_aoi = [
        create_udl_elset_from_track(track, idx + len(tracks_in_aoi))
        for idx, track in enumerate(tracks_outside_aoi)
    ]

    all_elsets = elsets_in_aoi + elsets_outside_aoi

    # Configure mock UdlReader to return our test elsets
    mock_udl_reader.all_current_elsets.return_value = [all_elsets]

    # Configure mock UdlReader.get_onorbits to return mock OnOrbit objects
    def mock_get_onorbits(ids):
        from omnicat.ingestor.udl.models import OnOrbit_Abridged, ObjectTypeEnum, DataModeEnum
        onorbits = []
        for idx, onorbit_id in enumerate(ids):
            onorbits.append(OnOrbit_Abridged(
                idOnOrbit=onorbit_id,
                classificationMarking="UNCLASSIFIED",
                satNo=idx + 10000,
                commonName=f"Test Satellite {idx}",
                source="TEST",
                dataMode=DataModeEnum.REAL,
                objectType=ObjectTypeEnum.PAYLOAD
            ))
        return onorbits

    # Configure mock UdlReader.get_onorbit_uri to return mock URI
    def mock_get_onorbit_uri(onorbit):
        return f"https://test.example.com/onorbit/{onorbit.idOnOrbit}"

    mock_udl_reader.get_onorbits.side_effect = mock_get_onorbits
    mock_udl_reader.get_onorbit_uri.side_effect = mock_get_onorbit_uri

    return mock_udl_reader


def create_mock_udl_reader_with_correlated_elsets(tracks_in_aoi: list[Track], tracks_outside_aoi: list[Track]) -> MagicMock:
    """Create a mock UdlReader that returns predefined Elset_Abridged items with OnOrbit IDs."""
    mock_udl_reader = MagicMock(spec=UdlReader)

    # Generate OnOrbit IDs for tests - UUIDs to stay within 36 char limit
    onorbit_ids = [str(uuid.uuid4()) for _ in range(
        len(tracks_in_aoi) + len(tracks_outside_aoi))]

    # Convert Track objects to Elset_Abridged objects WITH OnOrbit IDs
    elsets_in_aoi = [
        create_udl_elset_from_track(track, idx, onorbit_id=onorbit_ids[idx])
        for idx, track in enumerate(tracks_in_aoi)
    ]

    elsets_outside_aoi = [
        create_udl_elset_from_track(track, idx + len(tracks_in_aoi),
                                    onorbit_id=onorbit_ids[idx + len(tracks_in_aoi)])
        for idx, track in enumerate(tracks_outside_aoi)
    ]

    all_elsets = elsets_in_aoi + elsets_outside_aoi

    # Configure mock UdlReader to return our test elsets
    mock_udl_reader.all_current_elsets.return_value = [all_elsets]

    # Configure mock UdlReader.get_onorbits to return mock OnOrbit objects
    def mock_get_onorbits(ids):
        from omnicat.ingestor.udl.models import OnOrbit_Abridged, ObjectTypeEnum, DataModeEnum
        onorbits = []
        for idx, onorbit_id in enumerate(ids):
            onorbits.append(OnOrbit_Abridged(
                idOnOrbit=onorbit_id,
                classificationMarking="UNCLASSIFIED",
                satNo=idx + 10000,
                commonName=f"Test Satellite {idx}",
                source="TEST",
                dataMode=DataModeEnum.REAL,
                objectType=ObjectTypeEnum.PAYLOAD
            ))
        return onorbits

    # Configure mock UdlReader.get_onorbit_uri to return mock URI
    def mock_get_onorbit_uri(onorbit):
        return f"https://test.example.com/onorbit/{onorbit.idOnOrbit}"

    mock_udl_reader.get_onorbits.side_effect = mock_get_onorbits
    mock_udl_reader.get_onorbit_uri.side_effect = mock_get_onorbit_uri

    return mock_udl_reader


def run_udl_ingestor_test_with_uncorrelated_elsets(
    neo4j_db: Neo4jDb,
    postgis_db: PostGisDb,
    tracks_in_aoi: list[Track],
    tracks_outside_aoi: list[Track],
    aoi: AreaOfInterest
) -> tuple[list[str], list[Track], dict]:
    """
    Run test flow for UdlIngestor with uncorrelated elsets (no OnOrbit IDs).

    Args:
        neo4j_db: Neo4j database instance
        postgis_db: PostGIS database instance
        tracks_in_aoi: List of tracks expected to be in the AOI
        tracks_outside_aoi: List of tracks expected to be outside the AOI
        aoi: The Area of Interest to query

    Returns:
        tuple: (result_ids, result_tracks, in_aoi_tles) from the AOI query
    """
    # Create a dictionary of TLE data from in_aoi tracks for later comparison
    in_aoi_tles = {(track.data.line1, track.data.line2)
                    : True for track in tracks_in_aoi}

    # Create mock UdlReader
    mock_udl_reader = create_mock_udl_reader_with_uncorrelated_elsets(
        tracks_in_aoi, tracks_outside_aoi)

    # Create OmnicatIngestor with test databases
    omnicat_ingestor = OmnicatIngestor(neo4j_db, postgis_db)

    # Create UdlIngestor with mock UdlReader
    udl_ingestor = UdlIngestor(
        batch_size=1000,
        udl_reader=mock_udl_reader,
        omnicat_ingestor=omnicat_ingestor
    )

    # Get time window from AOI for ingestion
    # Add buffer for ingestion window
    window_start = aoi.time_start - timedelta(days=1)
    window_end = aoi.time_end + timedelta(days=1)

    # Ingest tracks using UdlIngestor
    udl_ingestor.ingest(window_start, window_end)

    # Create OmniCat instance with test databases for querying
    omnicat = OmniCat(neo4j_db, postgis_db)

    # Query tracks within aoi
    result_ids, result_tracks = omnicat.query_tracks_within_aoi(aoi)

    return result_ids, result_tracks, in_aoi_tles


def run_udl_ingestor_test_with_correlated_elsets(
    neo4j_db: Neo4jDb,
    postgis_db: PostGisDb,
    tracks_in_aoi: list[Track],
    tracks_outside_aoi: list[Track],
    aoi: AreaOfInterest
) -> tuple[list[str], list[Track], dict]:
    """
    Run test flow for UdlIngestor with correlated elsets (with OnOrbit IDs).

    Args:
        neo4j_db: Neo4j database instance
        postgis_db: PostGIS database instance
        tracks_in_aoi: List of tracks expected to be in the AOI
        tracks_outside_aoi: List of tracks expected to be outside the AOI
        aoi: The Area of Interest to query

    Returns:
        tuple: (result_ids, result_tracks, in_aoi_tles) from the AOI query
    """
    # Create a dictionary of TLE data from in_aoi tracks for later comparison
    in_aoi_tles = {(track.data.line1, track.data.line2)
                    : True for track in tracks_in_aoi}

    # Create mock UdlReader
    mock_udl_reader = create_mock_udl_reader_with_correlated_elsets(
        tracks_in_aoi, tracks_outside_aoi)

    # Create OmnicatIngestor with test databases
    omnicat_ingestor = OmnicatIngestor(neo4j_db, postgis_db)

    # Create UdlIngestor with mock UdlReader
    udl_ingestor = UdlIngestor(
        batch_size=1000,
        udl_reader=mock_udl_reader,
        omnicat_ingestor=omnicat_ingestor
    )

    # Get time window from AOI for ingestion
    window_start = aoi.time_start - timedelta(days=1)
    window_end = aoi.time_end + timedelta(days=1)

    # Ingest tracks using UdlIngestor
    udl_ingestor.ingest(window_start, window_end)

    # Verify that get_onorbits and get_onorbit_uri were called
    mock_udl_reader.get_onorbits.assert_called_once()
    assert mock_udl_reader.get_onorbit_uri.call_count > 0

    # Create OmniCat instance with test databases for querying
    omnicat = OmniCat(neo4j_db, postgis_db)

    # Query tracks within aoi
    result_ids, result_tracks = omnicat.query_tracks_within_aoi(aoi)

    return result_ids, result_tracks, in_aoi_tles


def test_omnicat_aoi_query(
    neo4j_db,
    postgis_db,
    aoi_query_time_window,
    tracks_in_aoi,
    tracks_outside_aoi,
    aoi
):
    """
    Test UdlIngestor by mocking UdlReader to return predefined Elset_Abridged items with TLEs.
    After ingestion, query tracks within AOI and verify the same assertions as in test_query_tracks_within_aoi.
    This test uses the OmniCat API directly.
    """
    # Run test flow with uncorrelated tracks (no OnOrbit IDs)
    result_ids, result_tracks, in_aoi_tles = run_udl_ingestor_test_with_uncorrelated_elsets(
        neo4j_db, postgis_db, tracks_in_aoi, tracks_outside_aoi, aoi
    )

    # Verify results - same assertions as in test_query_tracks_within_aoi
    assert len(
        result_tracks) == 4, f"Expected 4 tracks in aoi, found {len(result_tracks)}"

    # Verify all tracks in result are from the "in_aoi" test tracks
    for track in result_tracks:
        # The original metadata is now nested inside the udl_elset field
        assert "udl_elset" in track.metadata, f"Track missing udl_elset in metadata"

        # Extract the TLE data from the track
        elset_data = json.loads(track.metadata["udl_elset"]) if isinstance(
            track.metadata["udl_elset"], str) else track.metadata["udl_elset"]

        tle_key = (elset_data['line1'], elset_data['line2'])

        # Check that this TLE is from our original in_aoi tracks
        assert tle_key in in_aoi_tles, f"Found track with TLE not in original in_aoi tracks: {tle_key}"


def create_udl_elset_from_track(track, idx, onorbit_id=None) -> Elset_Abridged:
    """Convert a Track with TLE data to an Elset_Abridged object."""
    tle_data = track.data
    line1 = tle_data.line1
    line2 = tle_data.line2

    # Extract TLE fields
    # Line 1 fields
    catalog_number = line1[2:7].strip()
    # Classification (U=Unclassified, C=Classified, S=Secret)
    classification = line1[7:8]
    launch_year = line1[9:11]
    launch_number = line1[11:14]
    launch_piece = line1[14:17]
    epoch_year = int(line1[18:20])
    epoch_day = float(line1[20:32])
    mean_motion_dot = float(line1[33:43])
    mean_motion_ddot = float(f"{line1[44:45]}.{line1[45:50]}e{line1[50:52]}")
    bstar = float(f"{line1[53:54]}.{line1[54:59]}e{line1[59:61]}")
    ephemeris_type = line1[62:63]
    element_number = int(line1[64:68])
    checksum = int(line1[68:69])

    # Line 2 fields
    inclination = float(line2[8:16])
    raan = float(line2[17:25])
    eccentricity = float(f"0.{line2[26:33]}")
    arg_perigee = float(line2[34:42])
    mean_anomaly = float(line2[43:51])
    mean_motion = float(line2[52:63])
    rev_number = int(line2[63:68])

    # Convert two-digit year to four-digit year
    full_year = 2000 + epoch_year if epoch_year < 50 else 1900 + epoch_year

    # Calculate epoch date
    start_of_year = datetime(full_year, 1, 1, tzinfo=timezone.utc)
    epoch = start_of_year + timedelta(days=epoch_day - 1)

    # Create Elset_Abridged
    return Elset_Abridged(
        idElset=f"test_elset_{idx}",
        idOnOrbit=onorbit_id,  # Now can be None or a valid ID
        source=track.source,
        line1=line1,
        line2=line2,
        catalogNumber=catalog_number,
        classificationMarking="UNCLASSIFIED",  # Default classification
        epoch=epoch,
        meanMotion=mean_motion,
        eccentricity=eccentricity,
        inclination=inclination,
        raan=raan,
        argOfPerigee=arg_perigee,
        meanAnomaly=mean_anomaly,
        bStar=bstar,
        dataMode="REAL",  # Default data mode
        metadata=track.metadata
    )


def test_http_aoi_query(
    client: TestClient,
    neo4j_db,
    postgis_db,
    aoi_query_time_window,
    tracks_in_aoi,
    tracks_outside_aoi,
    aoi_query_params,
    aoi_query_ingest_helper
):
    """
    Test the HTTP endpoint for querying tracks within an Area of Interest.
    This test adds tracks to the database and then queries them using the HTTP endpoint.
    """
    # Extract time window from fixture
    time_start, time_end = aoi_query_time_window

    # Use the helper to ingest tracks
    aoi = AreaOfInterest(**aoi_query_params)
    track_ids_in_aoi, track_ids_outside = aoi_query_ingest_helper(
        neo4j_db, postgis_db, tracks_in_aoi, tracks_outside_aoi, aoi
    )

    # Query parameters for the HTTP endpoint
    query_params = {
        "center": aoi_query_params["center"],
        "radius": aoi_query_params["radius"],
        "time_start": time_start.isoformat(),
        "time_end": time_end.isoformat(),
        "format": "tle",
        "page": 0,
        "page_size": 100
    }

    # Make the query request
    response = client.get("/tracks/query", params=query_params)
    assert response.status_code == 200

    # Parse response
    query_response = QueryTracksResponse(**response.json())
    result_tracks = query_response.tracks

    # Verify results
    assert len(
        result_tracks) == 4, f"Expected 4 tracks in aoi, found {len(result_tracks)}"

    # Verify all expected tracks are found
    found_track_ids = {track.track_id for track in result_tracks}
    expected_track_ids = set(track_ids_in_aoi)
    assert found_track_ids == expected_track_ids, \
        f"Missing tracks: {expected_track_ids - found_track_ids}, " \
        f"Extra tracks: {found_track_ids - expected_track_ids}"

    # Verify no unexpected tracks are found
    for track_response in result_tracks:
        assert track_response.track.metadata["test"].startswith("in_aoi_"), \
            f"Found unexpected track: {track_response.track.metadata['test']}"

    # Verify tracks that should not be found are not in results
    outside_track_ids = set(track_ids_outside)
    assert not (found_track_ids & outside_track_ids), \
        "Found tracks that should be outside aoi"

    # Verify pagination info
    assert query_response.pagination.page == 0
    assert query_response.pagination.page_size == 100
    assert query_response.pagination.total_records == 4
    assert query_response.pagination.total_pages == 1


def test_http_aoi_query_with_composite_fixture(
    client: TestClient,
    neo4j_db,
    postgis_db,
    aoi_query_truth_data,
    aoi_query_ingest_helper
):
    """
    Test the HTTP endpoint for querying tracks within an Area of Interest using the composite fixture.

    This test demonstrates how to use the composite aoi_query_truth_data
    fixture that groups all related aoi truth data fixtures.
    """
    # Extract data from the composite fixture
    time_window = aoi_query_truth_data["time_window"]
    tracks_in_aoi = aoi_query_truth_data["tracks_in_aoi"]
    tracks_outside_aoi = aoi_query_truth_data["tracks_outside_aoi"]
    query_params = aoi_query_truth_data["query_params"]
    aoi = aoi_query_truth_data["aoi"]

    # Use the helper to ingest tracks
    track_ids_in_aoi, track_ids_outside = aoi_query_ingest_helper(
        neo4j_db, postgis_db, tracks_in_aoi, tracks_outside_aoi, aoi
    )

    # Create parameters for HTTP endpoint
    http_query_params = {
        "center": query_params["center"],
        "radius": query_params["radius"],
        "time_start": time_window["start"].isoformat(),
        "time_end": time_window["end"].isoformat(),
        "format": "tle",
        "page": 0,
        "page_size": 100
    }

    # Make the query request
    response = client.get("/tracks/query", params=http_query_params)
    assert response.status_code == 200

    # Parse response
    query_response = QueryTracksResponse(**response.json())
    result_tracks = query_response.tracks

    # Verify results
    assert len(
        result_tracks) == 4, f"Expected 4 tracks in aoi, found {len(result_tracks)}"

    # Verify all expected tracks are found
    found_track_ids = {track.track_id for track in result_tracks}
    expected_track_ids = set(track_ids_in_aoi)
    assert found_track_ids == expected_track_ids, \
        f"Missing tracks: {expected_track_ids - found_track_ids}, " \
        f"Extra tracks: {found_track_ids - expected_track_ids}"


def test_omnicat_objects_creation(
    neo4j_db,
    postgis_db,
    aoi_query_truth_data
):
    """
    Test that UdlIngestor correctly creates OmniCat objects from OnOrbit objects.
    This test specifically ensures the get_omnicat_objects function is called
    by assigning OnOrbit IDs to test elsets.
    """
    # Extract data from the composite fixture
    tracks_in_aoi = aoi_query_truth_data["tracks_in_aoi"]
    tracks_outside_aoi = aoi_query_truth_data["tracks_outside_aoi"]
    aoi = aoi_query_truth_data["aoi"]

    # Run test flow with correlated tracks (with OnOrbit IDs)
    result_ids, result_tracks, _ = run_udl_ingestor_test_with_correlated_elsets(
        neo4j_db, postgis_db, tracks_in_aoi, tracks_outside_aoi, aoi
    )

    # Verify we have the expected number of tracks
    assert len(
        result_tracks) == 4, f"Expected 4 tracks in aoi, found {len(result_tracks)}"

    # Verify tracks have object correlations from onorbit objects
    for track in result_tracks:
        # The track should have object correlations now
        assert track.object_correlations, f"Track missing object correlations"


def test_omnicat_aoi_query_with_composite_fixture(
    neo4j_db,
    postgis_db,
    aoi_query_truth_data
):
    """
    Test UdlIngestor and direct OmniCat API using the composite truth data fixture.

    This test demonstrates how to use the composite aoi_query_truth_data fixture
    with the UdlIngestor and direct OmniCat API approach.
    """
    # Extract data from the composite fixture
    tracks_in_aoi = aoi_query_truth_data["tracks_in_aoi"]
    tracks_outside_aoi = aoi_query_truth_data["tracks_outside_aoi"]
    aoi = aoi_query_truth_data["aoi"]

    # Run test flow with uncorrelated tracks (no OnOrbit IDs)
    result_ids, result_tracks, in_aoi_tles = run_udl_ingestor_test_with_uncorrelated_elsets(
        neo4j_db, postgis_db, tracks_in_aoi, tracks_outside_aoi, aoi
    )

    # Verify results
    assert len(
        result_tracks) == 4, f"Expected 4 tracks in aoi, found {len(result_tracks)}"

    # Verify all tracks in result are from the "in_aoi" test tracks
    for track in result_tracks:
        # The original metadata is now nested inside the udl_elset field
        assert "udl_elset" in track.metadata, f"Track missing udl_elset in metadata"

        # Extract the TLE data from the track
        elset_data = json.loads(track.metadata["udl_elset"]) if isinstance(
            track.metadata["udl_elset"], str) else track.metadata["udl_elset"]

        tle_key = (elset_data['line1'], elset_data['line2'])

        # Check that this TLE is from our original in_aoi tracks
        assert tle_key in in_aoi_tles, f"Found track with TLE not in original in_aoi tracks: {tle_key}"


def test_http_aoi_query_with_filters(
    client: TestClient,
    neo4j_db,
    postgis_db,
    aoi_query_time_window,
    tracks_in_aoi,
    tracks_outside_aoi,
    aoi_query_params,
    aoi_query_ingest_helper
):
    """
    Test the HTTP endpoint for querying tracks within an Area of Interest with filters.
    This test verifies that filter parameters correctly limit the results.
    """
    # Extract time window from fixture
    time_start, time_end = aoi_query_time_window

    # Set up test data with different sources
    # First, modify a few tracks to have different sources
    modified_tracks_in_aoi = []
    for i, track in enumerate(tracks_in_aoi):
        # Clone the track and modify its source based on index
        track_copy = Track(
            # Alternate between source_0 and source_1
            source="test_source_" + str(i % 2),
            format=track.format,
            data=track.data,
            metadata={**track.metadata, "filter_test": f"track_{i}"},
            object_correlations=track.object_correlations
        )
        modified_tracks_in_aoi.append(track_copy)

    # Use the helper to ingest tracks
    aoi = AreaOfInterest(**aoi_query_params)
    track_ids_in_aoi, track_ids_outside = aoi_query_ingest_helper(
        neo4j_db, postgis_db, modified_tracks_in_aoi, tracks_outside_aoi, aoi
    )

    # Query parameters for the HTTP endpoint with a source filter
    query_params = {
        "center": aoi_query_params["center"],
        "radius": aoi_query_params["radius"],
        "time_start": time_start.isoformat(),
        "time_end": time_end.isoformat(),
        "format": "tle",
        "page": 0,
        "page_size": 100,
        "sources": ["test_source_0"],  # Filter for just source_0
        # Include both correlated and uncorrelated tracks
        "correlation_statuses": ["Correlated", "Uncorrelated"]
    }

    # Make the query request
    response = client.get("/tracks/query", params=query_params)
    assert response.status_code == 200

    # Parse response
    query_response = QueryTracksResponse(**response.json())
    result_tracks = query_response.tracks

    # We expect approximately half of the tracks (those with source_0)
    expected_count = sum(
        1 for track in modified_tracks_in_aoi if track.source == "test_source_0")

    # Verify results
    assert len(result_tracks) == expected_count, \
        f"Expected {expected_count} filtered tracks in aoi, found {len(result_tracks)}"

    # Verify all results match the filter criteria
    for track_response in result_tracks:
        assert track_response.track.source == "test_source_0", \
            f"Found track with unexpected source: {track_response.track.source}"

        # Also verify it's in the AOI
        assert track_response.track.metadata["test"].startswith("in_aoi_"), \
            f"Found track outside AOI: {track_response.track.metadata['test']}"

    # Try another filter - this time with multiple sources
    query_params["sources"] = ["test_source_0", "test_source_1"]

    # Make the query request
    response = client.get("/tracks/query", params=query_params)
    assert response.status_code == 200

    # Parse response
    query_response = QueryTracksResponse(**response.json())
    result_tracks = query_response.tracks

    # With both sources, we should get all tracks
    assert len(result_tracks) == len(modified_tracks_in_aoi), \
        f"Expected {len(modified_tracks_in_aoi)} tracks with all sources, found {len(result_tracks)}"

    # Try filtering by correlation status - get only correlated tracks
    query_params["correlation_statuses"] = ["Correlated"]

    # Make the query request
    response = client.get("/tracks/query", params=query_params)
    assert response.status_code == 200

    # Parse response
    query_response = QueryTracksResponse(**response.json())
    correlated_tracks = query_response.tracks

    # Verify all returned tracks have object correlations
    for track_response in correlated_tracks:
        assert track_response.track.object_correlations is not None and len(track_response.track.object_correlations) > 0, \
            f"Found track without correlations when filtering for correlated tracks"

    # Try filtering by correlation status - get only uncorrelated tracks
    query_params["correlation_statuses"] = ["Uncorrelated"]

    # Make the query request
    response = client.get("/tracks/query", params=query_params)
    assert response.status_code == 200

    # Parse response
    query_response = QueryTracksResponse(**response.json())
    uncorrelated_tracks = query_response.tracks

    # Verify all returned tracks have no object correlations
    for track_response in uncorrelated_tracks:
        assert (track_response.track.object_correlations is None or
                len(track_response.track.object_correlations) == 0), \
            f"Found track with correlations when filtering for uncorrelated tracks"
