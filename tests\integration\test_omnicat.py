import pytest
from datetime import datetime, timedelta, timezone

from omnicat import OmniCat
from omnicat.models import Track, StateVector, AreaOfInterest, TLE
from omnicat.models.track_models import CorrelatedTracksFilter, TrackFilter, CorrelationStatus


def test_query_tracks_within_aoi(
    neo4j_db,
    postgis_db,
    aoi_query_time_window,
    tracks_in_aoi,
    tracks_outside_aoi,
    aoi,
    aoi_query_ingest_helper
):
    """Test aoi query with known truth data."""
    # Create OmniCat instance with test databases
    omnicat = OmniCat(neo4j_db, postgis_db)

    # Use the helper to ingest tracks
    track_ids_in_aoi, track_ids_outside = aoi_query_ingest_helper(
        neo4j_db, postgis_db, tracks_in_aoi, tracks_outside_aoi, aoi
    )

    # Query tracks within aoi
    result_ids, result_tracks = omnicat.query_tracks_within_aoi(
        aoi)

    # Verify results
    assert len(
        result_tracks) == 4, f"Expected 4 tracks in aoi, found {len(result_tracks)}"

    # Verify all expected tracks are found
    found_track_ids = set(result_ids)
    expected_track_ids = set(track_ids_in_aoi)
    assert found_track_ids == expected_track_ids, \
        f"Missing tracks: {expected_track_ids - found_track_ids}, " \
        f"Extra tracks: {found_track_ids - expected_track_ids}"

    # Verify no unexpected tracks are found
    for track in result_tracks:
        assert track.metadata["test"].startswith("in_aoi_"), \
            f"Found unexpected track: {track.metadata['test']}"

    # Verify tracks that should not be found are not in results
    outside_track_ids = set(track_ids_outside)
    assert not (found_track_ids & outside_track_ids), \
        "Found tracks that should be outside aoi"


def test_query_tracks_with_composite_truth_data(
    neo4j_db,
    postgis_db,
    aoi_query_truth_data,
    aoi_query_ingest_helper
):
    """
    Test aoi query using the composite truth data fixture.

    This test demonstrates how to use the composite aoi_query_truth_data
    fixture that groups all related aoi truth data fixtures.
    """
    # Create OmniCat instance with test databases
    omnicat = OmniCat(neo4j_db, postgis_db)

    # Extract data from the composite fixture
    tracks_in_aoi = aoi_query_truth_data["tracks_in_aoi"]
    tracks_outside_aoi = aoi_query_truth_data["tracks_outside_aoi"]
    aoi = aoi_query_truth_data["aoi"]

    # Use the helper to ingest tracks
    track_ids_in_aoi, track_ids_outside = aoi_query_ingest_helper(
        neo4j_db, postgis_db, tracks_in_aoi, tracks_outside_aoi, aoi
    )

    # Query tracks within aoi
    result_ids, result_tracks = omnicat.query_tracks_within_aoi(
        aoi)

    # Verify results
    assert len(
        result_tracks) == 4, f"Expected 4 tracks in aoi, found {len(result_tracks)}"

    # Verify all expected tracks are found
    found_track_ids = set(result_ids)
    expected_track_ids = set(track_ids_in_aoi)
    assert found_track_ids == expected_track_ids, \
        f"Missing tracks: {expected_track_ids - found_track_ids}, " \
        f"Extra tracks: {found_track_ids - expected_track_ids}"

    # Verify no unexpected tracks are found
    for track in result_tracks:
        assert track.metadata["test"].startswith("in_aoi_"), \
            f"Found unexpected track: {track.metadata['test']}"


def test_query_conjunctions_with_truth_data(
    neo4j_db,
    postgis_db,
    conjunction_query_ingest_helper
):
    """Test conjunction query with known truth data."""
    # Create OmniCat instance with test databases
    omnicat = OmniCat(neo4j_db, postgis_db)

    # Define test time window - using original values
    time_start = datetime(2025, 2, 14, 15, 17, tzinfo=timezone.utc)
    time_end = datetime(2025, 2, 14, 16, 17, tzinfo=timezone.utc)
    max_distance = 50  # km

    # Create tracks that should have a conjunction
    tracks_with_conjunction = [
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 32792U 08021K   25045.01613776  .00002292  00000+0  27670-3 0  9999",
                line2="2 32792  98.1144  31.2478 0026515 120.4139 239.9700 14.84567357906685"
            ),
            metadata={"test": "conjunction_1"}
        ),
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 27652U 03004B   25045.21735808  .00003858  00000+0  38631-3 0  9995",
                line2="2 27652  40.0040 343.7274 0022285 262.9530  96.8725 14.91960520196468"
            ),
            metadata={"test": "conjunction_2"}
        )
    ]

    # Create tracks that should not have conjunctions
    tracks_without_conjunction = [
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 21780U 91077B   25045.47751197  .00000014  00000+0  17477-4 0  9992",
                line2="2 21780  82.6000 197.2794 0003700 173.3306 250.5951 12.63431213533833"
            ),
            metadata={"test": "no_conjunction_1"}
        ),
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 30505U 99025AHT 25045.15492663  .00004179  00000+0  19679-2 0  9993",
                line2="2 30505  98.9382 121.6365 0063486  28.2252   1.9285 14.19407169926628"
            ),
            metadata={"test": "no_conjunction_2"}
        ),
        Track(
            source="test",
            format="tle",
            data=TLE(
                line1="1 62933U 25028F   25044.59963181  .02097510  24112-5  16516-2 0  9996",
                line2="2 62933  97.9399 100.9926 0277737 212.4674 278.8339 15.67935560000712"
            ),
            metadata={"test": "no_conjunction_3"}
        )
    ]

    # Use the helper to ingest tracks with proper propagation period
    conjunction_track_ids, no_conjunction_track_ids = conjunction_query_ingest_helper(
        neo4j_db,
        postgis_db,
        tracks_with_conjunction,
        tracks_without_conjunction,
        time_start,
        time_end
    )

    # Query for conjunctions
    conjunctions = omnicat.query_conjunctions(
        time_start, time_end, max_distance)

    # Expected conjunction time
    expected_conjunction_time = datetime(
        2025, 2, 14, 15, 53, 40, tzinfo=timezone.utc)

    # Verify results
    assert len(conjunctions) == 1, "Expected one conjunction"

    track_a_id, track_b_id, cx_distance, cx_time = conjunctions[0]

    # Verify the conjunction involves the expected tracks
    assert track_a_id in conjunction_track_ids, f"Expected track {track_a_id} to be in {conjunction_track_ids}"
    assert track_b_id in conjunction_track_ids, f"Expected track {track_b_id} to be in {conjunction_track_ids}"
    assert track_a_id != track_b_id

    # Verify conjunction distance is within limits
    assert cx_distance <= max_distance, f"Expected conjunction distance <= {max_distance} km, got {cx_distance} km"

    # Verify conjunction time
    assert cx_time >= time_start, f"Conjunction time {cx_time} should be after {time_start}"
    assert cx_time <= time_end, f"Conjunction time {cx_time} should be before {time_end}"

    time_difference = abs(
        (cx_time.astimezone(timezone.utc) - expected_conjunction_time.astimezone(timezone.utc)).total_seconds())

    # The original assertion would use exact time match, allow a very small tolerance
    assert time_difference <= 1, \
        f"Expected conjunction at {expected_conjunction_time}, got {cx_time} (diff: {time_difference}s)"


def test_query_filtered_tracks_within_aoi(
    neo4j_db,
    postgis_db,
    aoi_query_time_window,
    tracks_in_aoi,
    tracks_outside_aoi,
    aoi,
    aoi_query_ingest_helper,
    correlated_tracks_filter_setup
):
    """Test querying tracks within AOI with additional filtering criteria."""
    # Create OmniCat instance with test databases
    omnicat = OmniCat(neo4j_db, postgis_db)

    # Use the helper to ingest tracks
    track_ids_in_aoi, track_ids_outside = aoi_query_ingest_helper(
        neo4j_db, postgis_db, tracks_in_aoi, tracks_outside_aoi, aoi
    )

    # Create and add an uncorrelated track to test the correlation status filtering
    from datetime import timedelta

    # Create a track with no object correlation, use TLE data to match what the AOI helper expects
    uncorrelated_track = Track(
        source="TEST_UNCORRELATED_SOURCE",
        format="tle",
        data=TLE(
            line1="1 25544U 98067A   22001.00000000  .00000000  00000-0  00000-0 0  9995",
            line2="2 25544  51.6416 247.4627 0006703 130.5360 325.0288 15.49140040  1196"
        ),
        metadata={"test": "uncorrelated_track_test"}
        # No object_correlations provided, so no correlation
    )

    # Add the uncorrelated track to both databases
    uncorrelated_track_ids, _ = aoi_query_ingest_helper(
        neo4j_db, postgis_db, [uncorrelated_track], [], aoi
    )

    # First query without filtering to establish baseline
    result_ids_unfiltered, result_tracks_unfiltered = omnicat.query_tracks_within_aoi(
        aoi)

    # Test with basic filters that should work without relying on complex metadata or correlations
    try:
        # 1. Test with an empty filter (should be the same as unfiltered)
        empty_correlated_filter = CorrelatedTracksFilter()
        empty_filter = TrackFilter(
            correlation_statuses=None,  # None means both correlated and uncorrelated
            correlated_tracks_filter=empty_correlated_filter
        )
        result_ids_empty_filter, result_tracks_empty_filter = omnicat.query_filtered_tracks_within_aoi(
            aoi, empty_filter
        )

        # Assert that we get the same number of tracks as the unfiltered query
        assert len(result_ids_empty_filter) == len(result_ids_unfiltered), \
            "Empty filter should return same number of tracks as unfiltered query"

        # 2. Test with non-matching filter (should return empty results)
        non_matching_correlated_filter = CorrelatedTracksFilter(
            sources=["NON_EXISTENT_SOURCE"])
        non_matching_filter = TrackFilter(
            sources=["NON_EXISTENT_SOURCE"],
            correlation_statuses=[CorrelationStatus.CORRELATED],
            correlated_tracks_filter=non_matching_correlated_filter
        )
        result_ids_non_matching, result_tracks_non_matching = omnicat.query_filtered_tracks_within_aoi(
            aoi, non_matching_filter
        )

        # Assert that we get no results
        assert len(
            result_ids_non_matching) == 0, "Non-matching filter should return zero results"
        assert len(
            result_tracks_non_matching) == 0, "Non-matching filter should return zero results"

        # 3. Test with uncorrelated tracks filtering
        # First test with only correlated tracks
        source_correlated_filter = CorrelatedTracksFilter(
            sources=["TEST_UNCORRELATED_SOURCE"])
        correlated_only_filter = TrackFilter(
            sources=["TEST_UNCORRELATED_SOURCE"],
            correlation_statuses=[CorrelationStatus.CORRELATED],
            correlated_tracks_filter=source_correlated_filter
        )
        result_ids_source_without, result_tracks_source_without = omnicat.query_filtered_tracks_within_aoi(
            aoi, correlated_only_filter
        )

        # Should not find any tracks (since our test track is uncorrelated)
        assert len(result_ids_source_without) == 0, \
            "Should not find uncorrelated tracks when only filtering for correlated tracks"

        # Then test with uncorrelated tracks
        uncorrelated_only_filter = TrackFilter(
            sources=["TEST_UNCORRELATED_SOURCE"],
            correlation_statuses=[CorrelationStatus.UNCORRELATED],
            correlated_tracks_filter=None
        )
        result_ids_source_with, result_tracks_source_with = omnicat.query_filtered_tracks_within_aoi(
            aoi, uncorrelated_only_filter
        )

        # Should find at least one track (our test uncorrelated track)
        assert len(result_ids_source_with) > 0, \
            "Should find at least one track when filtering for uncorrelated tracks"

        # Check that all the found tracks have the right source
        assert all(track.source == "TEST_UNCORRELATED_SOURCE" for track in result_tracks_source_with), \
            "All filtered tracks should have the source we filtered by"

    except Exception as e:
        # If there are any errors, print more detailed information to help debug
        print(f"Error in filter test: {e}")
        print(f"Tracks in AOI: {track_ids_in_aoi}")
        print(f"Uncorrelated track IDs: {uncorrelated_track_ids}")
        # Re-raise the exception to fail the test
        raise
