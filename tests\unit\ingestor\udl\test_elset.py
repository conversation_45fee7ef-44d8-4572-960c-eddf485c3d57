import pytest
from datetime import datetime, timezone
from omnicat.ingestor.udl.models import Elset_Abridged


def test_elset_abridged_deserialization():
    test_data = {
        "idElset": "3fe56759-cb4e-4d2b-8079-ff3aaff6547b",
        "classificationMarking": "U",
        "satNo": 59488,
        "epoch": "2024-11-09T18:43:26.459904Z",
        "meanMotion": 15.40734456,
        "idOnOrbit": "59488",
        "eccentricity": 0.000291,
        "inclination": 43.0022,
        "raan": 221.7459,
        "argOfPerigee": 270.3339,
        "meanAnomaly": 89.7184,
        "revNo": 3378,
        "bStar": 0.00016916,
        "meanMotionDot": 0.00006777,
        "meanMotionDDot": 0,
        "semiMajorAxis": 6822.077,
        "period": 93.462,
        "apogee": 6824.063,
        "perigee": 6820.092000000001,
        "line1": "1 59488U 24071E 24314.78016736 +.00006777 +00000+0 +16916-3 0 99991",
        "line2": "2 59488 43.0022 221.7459 0002910 270.3339 89.7184 15.40734456033783",
        "createdAt": "2024-11-09T23:05:22.950Z",
        "createdBy": "system.ob-ingest",
        "source": "18th SPCS",
        "dataMode": "REAL",
        "algorithm": "SGP4",
        "origNetwork": "OPS1"
    }

    elset = Elset_Abridged.model_validate(test_data)

    # Test required fields
    assert elset.idElset == "3fe56759-cb4e-4d2b-8079-ff3aaff6547b"
    assert elset.classificationMarking == "U"
    assert elset.satNo == 59488
    assert elset.epoch == datetime(
        2024, 11, 9, 18, 43, 26, 459904, tzinfo=timezone.utc)
    assert elset.meanMotion == pytest.approx(15.40734456)

    # Test orbital parameters
    assert elset.eccentricity == pytest.approx(0.000291)
    assert elset.inclination == pytest.approx(43.0022)
    assert elset.raan == pytest.approx(221.7459)
    assert elset.argOfPerigee == pytest.approx(270.3339)
    assert elset.meanAnomaly == pytest.approx(89.7184)

    # Test derived quantities
    assert elset.semiMajorAxis == pytest.approx(6822.077)
    assert elset.period == pytest.approx(93.462)
    assert elset.apogee == pytest.approx(6824.063)
    assert elset.perigee == pytest.approx(6820.092000000001)

    # Test metadata fields
    assert elset.source == "18th SPCS"
    assert elset.dataMode == "REAL"
    assert elset.algorithm == "SGP4"
    assert elset.origNetwork == "OPS1"
    assert elset.createdAt == datetime(
        2024, 11, 9, 23, 5, 22, 950000, tzinfo=timezone.utc)
    assert elset.createdBy == "system.ob-ingest"
