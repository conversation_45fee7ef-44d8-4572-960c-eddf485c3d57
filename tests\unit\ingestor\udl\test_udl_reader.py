import unittest
from omnicat.ingestor.udl.udl_reader import order_onorbits_by
from omnicat.ingestor.udl.models import OnOrbit_Abridged


class TestUdlReader(unittest.TestCase):
    def test_order_onorbits_by(self):
        """Test that onorbit objects are correctly ordered according to the ids list."""
        # Create test data
        onorbits = [
            OnOrbit_Abridged(
                idOnOrbit="3", satNo=3, classificationMarking="U", source="test", dataMode="REAL"),
            OnOrbit_Abridged(
                idOnOrbit="1", satNo=1, classificationMarking="U", source="test", dataMode="REAL"),
            OnOrbit_Abridged(
                idOnOrbit="2", satNo=2, classificationMarking="U", source="test", dataMode="REAL"),
            OnOrbit_Abridged(
                idOnOrbit="4", satNo=4, classificationMarking="U", source="test", dataMode="REAL"),
        ]

        # Test case 1: Order objects in a specific order
        ids = ["1", "2", "3", "4"]
        ordered = order_onorbits_by(onorbits, ids)
        self.assertEqual(len(ordered), 4)
        self.assertEqual([o.idOnOrbit for o in ordered], ids)

        # Test case 2: Order objects in a different order
        ids = ["4", "1", "3", "2"]
        ordered = order_onorbits_by(onorbits, ids)
        self.assertEqual(len(ordered), 4)
        self.assertEqual([o.idOnOrbit for o in ordered], ids)

        # Test case 3: Some ids don't exist in the onorbits list
        ids = ["1", "5", "2", "6"]
        ordered = order_onorbits_by(onorbits, ids)
        self.assertEqual(len(ordered), 2)
        self.assertEqual([o.idOnOrbit for o in ordered], ["1", "2"])

        # Test case 4: Empty ids list
        ids = []
        ordered = order_onorbits_by(onorbits, ids)
        self.assertEqual(len(ordered), 0)

        # Test case 5: Empty onorbits list
        ids = ["1", "2", "3"]
        ordered = order_onorbits_by([], ids)
        self.assertEqual(len(ordered), 0)

        # Test case 6: Duplicate ids
        ids = ["1", "2", "1", "3"]
        ordered = order_onorbits_by(onorbits, ids)
        self.assertEqual(len(ordered), 4)
        self.assertEqual([o.idOnOrbit for o in ordered], ["1", "2", "1", "3"])


if __name__ == "__main__":
    unittest.main()
