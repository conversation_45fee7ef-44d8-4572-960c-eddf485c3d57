import pytest
from datetime import datetime, timezone, timedelta
from omnicat.models.base_models import AreaOfInterest, Pagination
from omnicat.models.track_models import GetTrackResponse, Track, TLE


def test_aoi_naive_datetime_gets_utc():
    """Test that naive datetime objects (no timezone) get UTC timezone."""
    naive_start = datetime(2024, 1, 1, 12, 0)
    naive_end = datetime(2024, 1, 1, 13, 0)
    aoi = AreaOfInterest(
        center=[0.0, 0.0, 0.0],
        radius=100.0,
        time_start=naive_start,
        time_end=naive_end
    )
    assert aoi.time_start.tzinfo == timezone.utc
    assert aoi.time_end.tzinfo == timezone.utc


def test_aoi_non_utc_converts_to_utc():
    """Test that non-UTC timezones get converted to UTC with correct time."""
    est = timezone(timedelta(hours=-5))
    est_start = datetime(2024, 1, 1, 12, 0, tzinfo=est)  # 12:00 EST
    est_end = datetime(2024, 1, 1, 13, 0, tzinfo=est)    # 13:00 EST
    aoi = AreaOfInterest(
        center=[0.0, 0.0, 0.0],
        radius=100.0,
        time_start=est_start,
        time_end=est_end
    )
    assert aoi.time_start.tzinfo == timezone.utc
    assert aoi.time_end.tzinfo == timezone.utc
    assert aoi.time_start.hour == 17  # 12:00 EST = 17:00 UTC
    assert aoi.time_end.hour == 18    # 13:00 EST = 18:00 UTC


def test_aoi_utc_stays_utc():
    """Test that UTC timezone datetimes remain unchanged."""
    utc_start = datetime(2024, 1, 1, 12, 0, tzinfo=timezone.utc)
    utc_end = datetime(2024, 1, 1, 13, 0, tzinfo=timezone.utc)
    aoi = AreaOfInterest(
        center=[0.0, 0.0, 0.0],
        radius=100.0,
        time_start=utc_start,
        time_end=utc_end
    )
    assert aoi.time_start == utc_start
    assert aoi.time_end == utc_end
    assert aoi.time_start.tzinfo == timezone.utc
    assert aoi.time_end.tzinfo == timezone.utc


def test_pagination_normal_case():
    """Test pagination with multiple pages of records."""
    records = [i for i in range(25)]  # 25 records total
    page = 1
    page_size = 10

    pagination, paginated_records = Pagination.paginate(
        records, page, page_size)

    # Check pagination information
    assert pagination.page == 1
    assert pagination.page_size == 10
    assert pagination.total_pages == 3  # Ceil(25/10) = 3
    assert pagination.total_records == 25

    # Check returned records (should be items 10-19)
    assert paginated_records == [10, 11, 12, 13, 14, 15, 16, 17, 18, 19]


def test_pagination_empty_records():
    """Test pagination with an empty list of records."""
    records = []
    page = 0
    page_size = 10

    pagination, paginated_records = Pagination.paginate(
        records, page, page_size)

    # Check pagination information
    assert pagination.page == 0
    assert pagination.page_size == 10
    assert pagination.total_pages == 1  # At least 1 page, even with no records
    assert pagination.total_records == 0

    # Check returned records (should be empty)
    assert paginated_records == []


def test_pagination_large_page_size():
    """Test pagination when page_size is larger than the number of records."""
    records = [i for i in range(5)]
    page = 0
    page_size = 10

    pagination, paginated_records = Pagination.paginate(
        records, page, page_size)

    # Check pagination information
    assert pagination.page == 0
    assert pagination.page_size == 10
    assert pagination.total_pages == 1
    assert pagination.total_records == 5

    # Check returned records (should be all records)
    assert paginated_records == [0, 1, 2, 3, 4]


def test_pagination_beyond_available():
    """Test pagination when requesting a page beyond available pages."""
    records = [i for i in range(10)]
    page = 3  # Beyond available pages
    page_size = 5

    pagination, paginated_records = Pagination.paginate(
        records, page, page_size)

    # Check pagination information
    assert pagination.page == 3
    assert pagination.page_size == 5
    assert pagination.total_pages == 2  # Only 2 pages of data available
    assert pagination.total_records == 10

    # Check returned records (should be empty since page is beyond available data)
    assert paginated_records == []


def test_pagination_first_page():
    """Test pagination specifically for the first page."""
    records = [i for i in range(25)]
    page = 0  # First page
    page_size = 10

    pagination, paginated_records = Pagination.paginate(
        records, page, page_size)

    # Check pagination information
    assert pagination.page == 0
    assert pagination.page_size == 10
    assert pagination.total_pages == 3
    assert pagination.total_records == 25

    # Check returned records (should be items 0-9)
    assert paginated_records == [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]


def test_pagination_with_track_responses():
    """Test pagination with real GetTrackResponse data."""
    track_responses = [
        GetTrackResponse(
            track_id='TRK-374887e1-efe9-4617-a39b-911de3a454fa',
            track=Track(
                source='test',
                format='tle',
                data=TLE(
                    line1='1 46553U 20070X   25043.44875729 -.00001396  00000-0 -74896-4 0  9999',
                    line2='2 46553  53.0529  98.4859 0001342  89.6768 270.4375 15.06390418241110'
                ),
                metadata={'test': 'in_aoi_1'},
                object_correlations=None
            )
        ),
        GetTrackResponse(
            track_id='TRK-3bcc66d8-8d3f-46d9-a81a-ed8f65d1da86',
            track=Track(
                source='test',
                format='tle',
                data=TLE(
                    line1='1 08685U 75004M   25043.51171333  .00000980  00000-0  24771-3 0  9990',
                    line2='2 08685  97.9835 141.5348 0047053   7.3045 352.8827 14.52410928637988'
                ),
                metadata={'test': 'in_aoi_3'},
                object_correlations=None
            )
        ),
        GetTrackResponse(
            track_id='TRK-543ab902-bfdc-4531-8618-a79c07e5a60c',
            track=Track(
                source='test',
                format='tle',
                data=TLE(
                    line1='1 59402U 24064E   25043.58336806  .00018649  00000-0  68610-3 0  9999',
                    line2='2 59402  43.0002  99.4901 0001161 272.4999  20.3372 15.27551076049553'
                ),
                metadata={'test': 'in_aoi_4'},
                object_correlations=None
            )
        ),
        GetTrackResponse(
            track_id='TRK-df8d50f6-c929-4fc3-aac4-41ff2197404f',
            track=Track(
                source='test',
                format='tle',
                data=TLE(
                    line1='1 13113U 82025A   25043.65088260  .00000086  00000-0  62233-4 0  9997',
                    line2='2 13113  82.5404 128.3447 0018337 170.0297 300.4456 13.85548740167855'
                ),
                metadata={'test': 'in_aoi_2'},
                object_correlations=None
            )
        )
    ]
    page = 0
    page_size = 100

    pagination, paginated_records = Pagination.paginate(
        track_responses, page, page_size)

    # Check pagination information
    assert pagination.page == 0
    assert pagination.page_size == 100
    assert pagination.total_pages == 1
    assert pagination.total_records == 4

    # Check returned records (should be all 4 track responses)
    assert len(paginated_records) == 4
    assert paginated_records == track_responses
