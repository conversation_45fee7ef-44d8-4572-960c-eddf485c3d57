import pytest
from pytest import approx
from datetime import datetime
from omnicat.models import TLE, Track, ObjectCorrelation 

test_tle = TLE(
    line1="1 25544U 98067A   08264.51782528 -.00002182  00000-0 -11606-4 0  2927",
    line2="2 25544  51.6416 247.4627 0006703 130.5360 325.0288 15.72125391563537"
)

test_metadata = {'test': '123'}

test_object_correlation = ObjectCorrelation(
    object_id="test", validity_time=datetime.now(), confidence=1.0, metadata=test_metadata)

tle_tracks = [
    Track(
        source="test",
        format="tle",
        data=test_tle,
        metadata=None,
        object_correlations=None
    ),
    Track(
        source="test",
        format="tle",
        data=test_tle,
        metadata=test_metadata,
        object_correlations=None
    ),
    Track(
        source="test",
        format="tle",
        data=test_tle,
        metadata=test_metadata,
        object_correlations=[test_object_correlation]
    ),
    Track(
        source="test",
        format="tle",
        data=test_tle,
        metadata=test_metadata,
        object_correlations=[test_object_correlation]
    ),
    Track(
        source="test",
        format="tle",
        data=test_tle,
        metadata=test_metadata,
        object_correlations=None
    ),
]


def test_tle_state_vector_conversion():
    for tle_track in tle_tracks:

        sv_track = tle_track.as_state_vector()

        assert sv_track.source == tle_track.source
        assert sv_track.format == "state_vector"
        assert sv_track.data == tle_track.data.to_state_vector()
        assert sv_track.data.validity_time == tle_track.data.validity_time
        assert sv_track.metadata == tle_track.metadata
        assert sv_track.object_correlations == tle_track.object_correlations

        roundtrip_tle_track = sv_track.as_tle()

        assert roundtrip_tle_track.source == sv_track.source
        assert roundtrip_tle_track.format == "tle"
        assert roundtrip_tle_track.data == tle_track.data
        assert roundtrip_tle_track.data.validity_time == sv_track.data.validity_time
        assert roundtrip_tle_track.metadata == sv_track.metadata
        assert roundtrip_tle_track.object_correlations == sv_track.object_correlations
