import pytest
from pydantic import ValidationError

from omnicat.models import <PERSON><PERSON>ilter, CorrelationStatus, CorrelatedTracksFilter


class TestTrackFilter:
    def test_empty_filter(self):
        """Test that an empty filter can be created."""
        filter = TrackFilter()
        assert filter.sources is None
        assert filter.correlation_statuses is None
        assert filter.correlated_tracks_filter is None

    def test_filter_with_all_fields(self):
        """Test that a filter with all fields can be created."""
        correlated_filter = CorrelatedTracksFilter(
            sources=["CORR_SOURCE1", "CORR_SOURCE2"],
            object_types=["TYPE1", "TYPE2"],
            country_codes=["US", "JP"],
            allegiances=["BLUE", "RED"]
        )

        filter = TrackFilter(
            sources=["MAIN_SOURCE1", "MAIN_SOURCE2"],
            correlation_statuses=[
                CorrelationStatus.CORRELATED, CorrelationStatus.UNCORRELATED],
            correlated_tracks_filter=correlated_filter
        )

        assert filter.sources == ["MAIN_SOURCE1", "MAIN_SOURCE2"]
        assert filter.correlation_statuses == [
            CorrelationStatus.CORRELATED, CorrelationStatus.UNCORRELATED]
        assert filter.correlated_tracks_filter == correlated_filter
        assert filter.correlated_tracks_filter.sources == [
            "CORR_SOURCE1", "CORR_SOURCE2"]
        assert filter.correlated_tracks_filter.object_types == [
            "TYPE1", "TYPE2"]
        assert filter.correlated_tracks_filter.country_codes == ["US", "JP"]
        assert filter.correlated_tracks_filter.allegiances == ["BLUE", "RED"]

    def test_sources_field(self):
        """Test that sources field works correctly."""
        filter = TrackFilter(sources=["SOURCE1", "SOURCE2"])
        assert filter.sources == ["SOURCE1", "SOURCE2"]
        assert filter.correlation_statuses is None
        assert filter.correlated_tracks_filter is None

    def test_correlated_tracks_filter(self):
        """Test that correlated_tracks_filter works correctly."""
        correlated_filter = CorrelatedTracksFilter(
            sources=["SOURCE1"],
            object_types=["TYPE1"]
        )

        filter = TrackFilter(correlated_tracks_filter=correlated_filter)
        assert filter.sources is None
        assert filter.correlated_tracks_filter == correlated_filter
        assert filter.correlated_tracks_filter.sources == ["SOURCE1"]
        assert filter.correlated_tracks_filter.object_types == ["TYPE1"]
        assert filter.correlated_tracks_filter.country_codes is None
        assert filter.correlated_tracks_filter.allegiances is None

    def test_correlation_statuses_none(self):
        """Test that correlation_statuses can be None."""
        filter = TrackFilter(correlation_statuses=None)
        assert filter.correlation_statuses is None

    def test_correlation_statuses_empty(self):
        """Test that correlation_statuses can be empty."""
        filter = TrackFilter(correlation_statuses=[])
        assert filter.correlation_statuses == []

    def test_correlation_statuses_correlated(self):
        """Test that correlation_statuses can be [Correlated]."""
        filter = TrackFilter(correlation_statuses=[
                             CorrelationStatus.CORRELATED])
        assert filter.correlation_statuses == [CorrelationStatus.CORRELATED]

    def test_correlation_statuses_uncorrelated(self):
        """Test that correlation_statuses can be [Uncorrelated]."""
        filter = TrackFilter(correlation_statuses=[
                             CorrelationStatus.UNCORRELATED])
        assert filter.correlation_statuses == [CorrelationStatus.UNCORRELATED]

    def test_correlation_statuses_both(self):
        """Test that correlation_statuses can include both values."""
        filter = TrackFilter(correlation_statuses=[
            CorrelationStatus.CORRELATED,
            CorrelationStatus.UNCORRELATED
        ])
        assert filter.correlation_statuses == [
            CorrelationStatus.CORRELATED, CorrelationStatus.UNCORRELATED]

    def test_correlation_statuses_string_values(self):
        """Test that correlation_statuses accepts string values that match enum values."""
        filter = TrackFilter(correlation_statuses=[
                             "Correlated", "Uncorrelated"])
        assert filter.correlation_statuses == [
            CorrelationStatus.CORRELATED, CorrelationStatus.UNCORRELATED]

    def test_invalid_correlation_status(self):
        """Test that invalid correlation status values raise a validation error."""
        with pytest.raises(ValidationError):
            TrackFilter(correlation_statuses=["Invalid"])

    def test_partially_invalid_correlation_status(self):
        """Test that partially invalid correlation status values raise a validation error."""
        with pytest.raises(ValidationError):
            TrackFilter(correlation_statuses=["Correlated", "Invalid"])
