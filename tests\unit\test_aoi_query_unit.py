import pytest
from datetime import datetime, timezone
from unittest.mock import <PERSON><PERSON>ock, patch

from omnicat import OmniCat
from omnicat.models import Track, AreaOfInterest, TLE, ObjectCorrelation
from omnicat.models.track_models import TrackFilter, CorrelationStatus


def test_filtered_aoi_query_consistency():
    """
    Test that filtered AOI queries return consistent track IDs and Track objects.
    This test uses mocks to verify the fix without requiring actual database operations.
    """
    from unittest.mock import MagicMock
    from omnicat import OmniCat

    # Create mock databases
    mock_neo4j_db = MagicMock()
    mock_postgis_db = MagicMock()

    # Create OmniCat instance with mocks
    omnicat = OmniCat(mock_neo4j_db, mock_postgis_db)

    # Mock Neo4j response: only correlated tracks (track_1)
    mock_track_1 = Track(
        source="TEST",
        format="tle",
        data=TLE(
            line1="1 12345U 23001A   24001.00000000  .00000000  00000-0  10000-4 0  9990",
            line2="2 12345  51.6000 145.3000 0010000  28.7000 331.2000 15.50000000 12340"
        ),
        metadata={},
        object_correlations=[ObjectCorrelation(
            object_id="obj_1",
            validity_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
            confidence=0.95
        )]
    )

    # Neo4j returns only the correlated track when filtering for correlated tracks
    mock_neo4j_db.get_filtered_tracks.return_value = (
        ["track_1"], [mock_track_1])

    # Mock PostGIS response: both correlated and uncorrelated tracks are within AOI
    # This simulates the bug where PostGIS returns tracks that don't match the filter
    mock_postgis_db.query_aoi.return_value = [
        "track_1", "track_2"]  # track_2 is uncorrelated

    # Mock coordinate transformation
    with patch('omnicat.processor.geodetic_to_ecef', return_value=[0.0, 0.0, 0.0]):
        # Test the query
        aoi = AreaOfInterest(
            center=[40.0, -74.0, 0.4],
            radius=1000.0,
            time_start=datetime(2024, 1, 1, tzinfo=timezone.utc),
            time_end=datetime(2024, 1, 1, 12, tzinfo=timezone.utc)
        )

        filter_params = TrackFilter(
            correlation_statuses=[CorrelationStatus.CORRELATED]
        )

        result_track_ids, result_tracks = omnicat.query_filtered_tracks_within_aoi(
            aoi, filter_params)

        # Verify the fix: should only return tracks that match BOTH filter criteria AND are within AOI
        assert len(result_track_ids) == len(result_tracks), \
            f"Mismatch: {len(result_track_ids)} track IDs but {len(result_tracks)} Track objects"

        # Should only include track_1 (correlated), not track_2 (uncorrelated)
        assert result_track_ids == ["track_1"], \
            f"Expected only ['track_1'] but got {result_track_ids}"

        # Should have exactly one Track object
        assert len(
            result_tracks) == 1, f"Expected 1 track but got {len(result_tracks)}"

        # The returned track should be correlated
        assert result_tracks[0].object_correlations, "Returned track should have object correlations"


def test_filtered_aoi_query_uncorrelated_tracks():
    """
    Test that filtering for uncorrelated tracks works correctly and doesn't include correlated tracks.
    """
    from unittest.mock import MagicMock
    from omnicat import OmniCat

    # Create mock databases
    mock_neo4j_db = MagicMock()
    mock_postgis_db = MagicMock()

    # Create OmniCat instance with mocks
    omnicat = OmniCat(mock_neo4j_db, mock_postgis_db)

    # Mock Neo4j response: only uncorrelated tracks (track_2)
    mock_track_2 = Track(
        source="TEST",
        format="tle",
        data=TLE(
            line1="1 67890U 23002A   24001.00000000  .00000000  00000-0  10000-4 0  9991",
            line2="2 67890  51.6000 145.3000 0010000  28.7000 331.2000 15.50000000 12341"
        ),
        metadata={},
        object_correlations=None  # Uncorrelated track
    )

    # Neo4j returns only the uncorrelated track when filtering for uncorrelated tracks
    mock_neo4j_db.get_filtered_tracks.return_value = (
        ["track_2"], [mock_track_2])

    # Mock PostGIS response: both correlated and uncorrelated tracks are within AOI
    # This simulates tracks within the spatial boundary, but only one matches the filter
    mock_postgis_db.query_aoi.return_value = [
        "track_1", "track_2"]  # track_1 is correlated

    # Mock coordinate transformation
    with patch('omnicat.processor.geodetic_to_ecef', return_value=[0.0, 0.0, 0.0]):
        # Test the query
        aoi = AreaOfInterest(
            center=[40.0, -74.0, 0.4],
            radius=1000.0,
            time_start=datetime(2024, 1, 1, tzinfo=timezone.utc),
            time_end=datetime(2024, 1, 1, 12, tzinfo=timezone.utc)
        )

        filter_params = TrackFilter(
            correlation_statuses=[CorrelationStatus.UNCORRELATED]
        )

        result_track_ids, result_tracks = omnicat.query_filtered_tracks_within_aoi(
            aoi, filter_params)

        # Verify the fix: should only return tracks that match BOTH filter criteria AND are within AOI
        assert len(result_track_ids) == len(result_tracks), \
            f"Mismatch: {len(result_track_ids)} track IDs but {len(result_tracks)} Track objects"

        # Should only include track_2 (uncorrelated), not track_1 (correlated)
        assert result_track_ids == ["track_2"], \
            f"Expected only ['track_2'] but got {result_track_ids}"

        # Should have exactly one Track object
        assert len(
            result_tracks) == 1, f"Expected 1 track but got {len(result_tracks)}"

        # The returned track should be uncorrelated
        assert result_tracks[0].object_correlations is None, "Returned track should not have object correlations"


def test_real_world_filtered_aoi_query():
    """
    Test filtered AOI query with real-world satellite data.
    This test uses actual TLE data and validates that only tracks within the AOI 
    and matching the correlation filter are returned.

    AOI: Over Ukraine region (49.01°N, 33.55°E) at 1500km altitude, 1500km radius
    Time window: 05/23/2025 15:14 - 16:14 UTC
    Filter: Correlated tracks only

    This test specifically validates the fix for the issue where filtered AOI queries
    were returning track IDs that didn't match the filter criteria. The test simulates
    a scenario where:
    - Neo4j returns 4 correlated tracks (all matching the "Correlated" filter)  
    - PostGIS spatial query returns only 1 track ID (within the AOI)
    - The final result should be the intersection: only the 1 track that is 
      both correlated AND within the AOI

    Before the fix, the system would incorrectly return tracks that were spatially
    within the AOI but didn't match the correlation filter criteria.
    """
    from unittest.mock import MagicMock
    from omnicat import OmniCat

    # Create mock databases
    mock_neo4j_db = MagicMock()
    mock_postgis_db = MagicMock()

    # Create OmniCat instance with mocks
    omnicat = OmniCat(mock_neo4j_db, mock_postgis_db)

    # Define AOI parameters
    aoi = AreaOfInterest(
        center=[49.01, 33.55, 1500.0],  # Ukraine region, 1500km altitude
        radius=1500.0,  # 1500km radius
        time_start=datetime(2025, 5, 23, 15, 14, tzinfo=timezone.utc),
        time_end=datetime(2025, 5, 23, 16, 14, tzinfo=timezone.utc)
    )

    # Track that SHOULD be returned - CZ-6A DEB (Chinese debris)
    expected_track = Track(
        source="18th SPCS",
        format="tle",
        data=TLE(
            line1="1 54460U 22151GL  25143.03971626 +.00001614 +00000+0 +54493-3 0 99997",
            line2="2 54460  98.7117 168.4162 0126318 143.2905 217.7015 14.33916265131702"
        ),
        metadata={
            "udl_elset": {
                "idElset": "33e0efe6-f24d-4609-9828-1a98289a3599",
                "satNo": 54460,
                "source": "18th SPCS",
                "countryCode": "CN",
                "objectType": "DEBRIS"
            }
        },
        object_correlations=[ObjectCorrelation(
            object_id="OBJ-87c20640-1ad8-4f66-b6dc-99f4f7c9bdac",
            validity_time=datetime(2025, 5, 23, 15, 14, tzinfo=timezone.utc),
            confidence=0.95
        )]
    )

    # Tracks that should NOT be returned (outside AOI or different filter criteria)

    # Track 1: STARLINK-31645 (US payload) - should not be in AOI
    starlink_track = Track(
        source="18th SPCS",
        format="tle",
        data=TLE(
            line1="1 59264U 24050L   25142.56049394 +.00000028 +00000+0 +13071-4 0 99995",
            line2="2 59264  53.1599  93.3812 0001208  95.3975 264.7164 15.30193454065977"
        ),
        metadata={
            "udl_elset": {
                "satNo": 59264,
                "source": "18th SPCS",
                "countryCode": "US",
                "objectType": "PAYLOAD"
            }
        },
        object_correlations=[ObjectCorrelation(
            object_id="OBJ-fb37cc1d-bbfa-4f1a-b302-d9891b451640",
            validity_time=datetime(2025, 5, 23, 15, 14, tzinfo=timezone.utc),
            confidence=0.95
        )]
    )

    # Track 2: PSLV DEB (Indian debris) - should not be in AOI
    pslv_track = Track(
        source="18th SPCS",
        format="tle",
        data=TLE(
            line1="1 27141U 01049CM  25142.68470317 +.00001795 +00000+0 +19263-3 0 99993",
            line2="2 27141  98.1283 180.8882 0048855  75.0662  42.0372 14.89089701263960"
        ),
        metadata={
            "udl_elset": {
                "satNo": 27141,
                "source": "18th SPCS",
                "countryCode": "IN",
                "objectType": "DEBRIS"
            }
        },
        object_correlations=[ObjectCorrelation(
            object_id="OBJ-9fc70bfc-9aac-4bb9-a48c-82a0dd7796c2",
            validity_time=datetime(2025, 5, 23, 15, 14, tzinfo=timezone.utc),
            confidence=0.95
        )]
    )

    # Track 3: BREEZE-M R/B (CIS rocket body) - should not be in AOI
    breeze_track = Track(
        source="18th SPCS",
        format="tle",
        data=TLE(
            line1="1 40385U 15005B   25141.90356047 -.00000324 +00000+0 +00000+0 0 99995",
            line2="2 40385  27.6418 310.0743 7613656  72.1056 352.0870  1.11109030041807"
        ),
        metadata={
            "udl_elset": {
                "satNo": 40385,
                "source": "18th SPCS",
                "countryCode": "CIS",
                "objectType": "ROCKET BODY"
            }
        },
        object_correlations=[ObjectCorrelation(
            object_id="OBJ-a5a3084c-5a59-4e2f-b4ef-1bd0d770cb76",
            validity_time=datetime(2025, 5, 23, 15, 14, tzinfo=timezone.utc),
            confidence=0.95
        )]
    )

    # Mock Neo4j to return all correlated tracks (simulating the filter for "Correlated")
    all_tracks = [expected_track, starlink_track, pslv_track, breeze_track]
    all_track_ids = ["track_54460", "track_59264",
                     "track_27141", "track_40385"]

    mock_neo4j_db.get_filtered_tracks.return_value = (
        all_track_ids, all_tracks)

    # Mock PostGIS to return only the track that's actually within the AOI
    # In reality, only the CZ-6A DEB should pass through the Ukraine region during this time window
    # Only the expected track is within AOI
    mock_postgis_db.query_aoi.return_value = ["track_54460"]

    # Mock coordinate transformation
    with patch('omnicat.processor.geodetic_to_ecef', return_value=[2500000.0, 3500000.0, 4800000.0]):
        # Apply filter for correlated tracks
        filter_params = TrackFilter(
            correlation_statuses=[CorrelationStatus.CORRELATED]
        )

        # Execute the query
        result_track_ids, result_tracks = omnicat.query_filtered_tracks_within_aoi(
            aoi, filter_params)

        # Validate results
        assert len(result_track_ids) == len(result_tracks), \
            f"Mismatch: {len(result_track_ids)} track IDs but {len(result_tracks)} Track objects"

        # Should only return the CZ-6A DEB track (within AOI and correlated)
        assert len(
            result_track_ids) == 1, f"Expected 1 track but got {len(result_track_ids)}"
        assert result_track_ids[0] == "track_54460", \
            f"Expected track_54460 but got {result_track_ids[0]}"

        # Validate the returned track data
        returned_track = result_tracks[0]
        assert returned_track.data.line1 == expected_track.data.line1, \
            "Returned track has incorrect TLE line1"
        assert returned_track.data.line2 == expected_track.data.line2, \
            "Returned track has incorrect TLE line2"
        assert returned_track.object_correlations, \
            "Returned track should have object correlations"
        assert returned_track.metadata["udl_elset"]["satNo"] == 54460, \
            "Returned track should be satellite 54460 (CZ-6A DEB)"

        # Verify the PostGIS query was called with the correct filtered track IDs
        mock_postgis_db.query_aoi.assert_called_once()
        call_args = mock_postgis_db.query_aoi.call_args
        assert call_args[1]["track_ids"] == all_track_ids, \
            "PostGIS should be queried with all filtered track IDs"

        # Verify coordinate transformation was called
        assert mock_postgis_db.query_aoi.call_count == 1, \
            "PostGIS query_aoi should be called exactly once"
