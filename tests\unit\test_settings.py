import pytest
import json
from omnicat.settings import Neo4jSettings, PostgisSettings, UdlSettings, Settings


class TestSettingsRepr:

    def test_udl_settings_repr_masks_password(self):
        # Create settings with explicitly set values
        settings = UdlSettings(
            user="udl_user",
            password="api_key_12345",
            base_url="https://api.example.com/udl",
            currentelset_suffix="custom/elset",
            onorbit_suffix="custom/onorbit",
            max_requests_per_second=5,
            request_period=2,
            max_attempts=4,
            wait_exponential_min=5,
            wait_exponential_max=15,
            wait_exponential_multiplier=1.5
        )

        assert "api_key_12345" not in repr(settings)

        assert str(settings) == repr(settings)

        assert settings.password == "api_key_12345"

    def test_settings_repr_contains_nested_settings(self):
        # Create nested settings with explicitly set values
        neo4j_settings = Neo4jSettings(
            user="neo4j_user",
            password="pwd1",
            uri="bolt://neo4j.example.com:7687"
        )

        postgis_settings = PostgisSettings(
            user="postgis_user",
            password="pwd2",
            host="postgis.example.com",
            port=5433
        )

        udl_settings = UdlSettings(
            user="udl_user",
            password="udl.password.12345",
            base_url="https://api.example.com"
        )

        # Create main settings with explicitly set nested settings
        settings = Settings(
            neo4j=neo4j_settings,
            postgis=postgis_settings,
            udl=udl_settings
        )

        assert repr(settings) == str(settings)
        assert 'udl.password.12345' not in repr(settings)
